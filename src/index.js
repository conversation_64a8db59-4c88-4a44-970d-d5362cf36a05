'use strict';

module.exports = {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/*{ strapi }*/) {},

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  bootstrap({ strapi }) {
    // Set up permissions for test-pack-options API
    strapi.db.lifecycles.subscribe({
      async afterCreate(event) {
        const { model } = event;
        
        if (model.uid === 'plugin::users-permissions.role') {
          await setupTestPackOptionsPermissions(strapi);
        }
      },
    });

    async function setupTestPackOptionsPermissions(strapi) {
      try {
        // Find Public role
        const publicRole = await strapi
          .query('plugin::users-permissions.role')
          .findOne({ where: { type: 'public' } });

        // Find Authenticated role
        const authenticatedRole = await strapi
          .query('plugin::users-permissions.role')
          .findOne({ where: { type: 'authenticated' } });

        const roles = [publicRole, authenticatedRole].filter(Boolean);

        for (const role of roles) {
          const permissions = await strapi
            .query('plugin::users-permissions.permission')
            .findMany({ where: { role: role.id } });

          // Check if test-pack-option permissions already exist
          const hasTestPackOptionPermissions = permissions.some(
            permission => permission.action.includes('test-pack-option')
          );

          // Check if test-pack permissions already exist
          const hasTestPackPermissions = permissions.some(
            permission => permission.action.includes('api::test-pack.test-pack')
          );

          if (!hasTestPackOptionPermissions) {
            // Create permissions for test-pack-options
            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: 'api::test-pack-option.test-pack-option.find',
                role: role.id,
              },
            });

            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: 'api::test-pack-option.test-pack-option.findOne',
                role: role.id,
              },
            });

            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: 'api::test-pack-option.test-pack-option.create',
                role: role.id,
              },
            });

            console.log(`✅ Test pack options permissions created for ${role.type} role`);
          }

          if (!hasTestPackPermissions) {
            console.log(`🔧 Creating test pack permissions for ${role.type} role...`);
            
            // Create permissions for test-pack
            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: 'api::test-pack.test-pack.find',
                role: role.id,
              },
            });

            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: 'api::test-pack.test-pack.findOne',
                role: role.id,
              },
            });

            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: 'api::test-pack.test-pack.create',
                role: role.id,
              },
            });

            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: 'api::test-pack.test-pack.update',
                role: role.id,
              },
            });

            await strapi.query('plugin::users-permissions.permission').create({
              data: {
                action: 'api::test-pack.test-pack.delete',
                role: role.id,
              },
            });

            console.log(`✅ Test pack permissions created for ${role.type} role`);
          } else {
            console.log(`ℹ️ Test pack permissions already exist for ${role.type} role`);
          }
        }
      } catch (error) {
        console.error('❌ Error setting up test pack permissions:', error);
      }
    }

    // Run initial setup
    setupTestPackOptionsPermissions(strapi);

    // Also run setup on startup to ensure permissions exist
    setTimeout(() => {
      setupTestPackOptionsPermissions(strapi);
    }, 3000);

    // Create sample test pack options if none exist
    async function createSampleData() {
      try {
        const existingOptions = await strapi.entityService.findMany('api::test-pack-option.test-pack-option');
        
        if (existingOptions.length === 0) {
          console.log('🚀 Creating sample test pack options...');
          
          const sampleOptions = [
            {
              name: 'Paket Dasar',
              description: 'Paket tes dasar untuk mengenal kepribadian Anda',
              price: 150000,
              tests: ['DISC', 'MBTI'],
              isActive: true,
              sortOrder: 1,
              publishedAt: new Date(),
            },
            {
              name: 'Paket Profesional',
              description: 'Paket tes lengkap untuk pengembangan karir profesional',
              price: 250000,
              tests: ['DISC', 'MBTI', 'TEAMS', 'VALUES'],
              isActive: true,
              sortOrder: 2,
              publishedAt: new Date(),
            },
            {
              name: 'Paket Bisnis',
              description: 'Paket tes khusus untuk pemilik bisnis dan entrepreneur',
              price: 350000,
              tests: ['DISC', 'MBTI', 'TEAMS', 'VALUES'],
              isActive: true,
              sortOrder: 3,
              publishedAt: new Date(),
            },
          ];

          for (const option of sampleOptions) {
            await strapi.entityService.create('api::test-pack-option.test-pack-option', {
              data: option,
            });
            console.log(`✅ Created: ${option.name}`);
          }
          
          console.log('✨ Sample test pack options created!');
        }
      } catch (error) {
        console.error('❌ Error creating sample data:', error);
      }
    }

    // Run after a short delay to ensure Strapi is fully loaded
    setTimeout(createSampleData, 2000);
  },
};
