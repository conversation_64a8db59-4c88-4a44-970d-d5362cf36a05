{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "disc_answers": {"type": "relation", "relation": "oneToMany", "target": "api::disc-answer.disc-answer", "mappedBy": "user"}, "teams_answers": {"type": "relation", "relation": "oneToMany", "target": "api::teams-answer.teams-answer", "mappedBy": "user"}, "values_answers": {"type": "relation", "relation": "oneToMany", "target": "api::values-answer.values-answer", "mappedBy": "user"}, "mbti_answers": {"type": "relation", "relation": "oneToMany", "target": "api::mbti-answer.mbti-answer", "mappedBy": "user"}, "test_packs": {"type": "relation", "relation": "manyToMany", "target": "api::test-pack.test-pack", "mappedBy": "users"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "payments": {"type": "relation", "relation": "manyToMany", "target": "api::payment.payment", "mappedBy": "users"}}}