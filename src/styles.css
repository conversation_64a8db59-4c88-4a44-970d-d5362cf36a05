/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

@import "~@angular/material/prebuilt-themes/indigo-pink.css";

/* Modern Design System Variables */
:root {
  /* Primary Brand Colors - Professional Blue Palette */
  --color-primary: #2563eb;
  --color-primary-light: #3b82f6;
  --color-primary-dark: #1d4ed8;
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-900: #1e3a8a;

  /* Secondary Colors - Complementary Palette */
  --color-secondary: #10b981;
  --color-secondary-light: #34d399;
  --color-secondary-dark: #059669;
  --color-accent: #f59e0b;
  --color-accent-light: #fbbf24;

  /* Neutral Colors - Modern Gray Scale */
  --color-white: #ffffff;
  --color-gray-25: #fcfcfd;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  /* Semantic Colors */
  --color-success: #10b981;
  --color-success-light: #d1fae5;
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-error: #ef4444;
  --color-error-light: #fee2e2;
  --color-info: #3b82f6;
  --color-info-light: #dbeafe;

  /* Typography System */
  --font-family-primary: "Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-secondary: "Inter", system-ui, sans-serif;
  --font-family-mono: "JetBrains Mono", "Fira Code", Consolas, monospace;

  /* Font Sizes - Type Scale */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Spacing System - 8px Grid */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  --spacing-24: 6rem;     /* 96px */
  --spacing-32: 8rem;     /* 128px */

  /* Shadows - Layered Depth */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

  /* Border Radius - Consistent Curves */
  --radius-none: 0;
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 200ms ease;
  --transition-slow: 300ms ease;
  --transition-slower: 500ms ease;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Modern Reset & Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-gray-25);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  display: flex;
  flex-direction: column;
}

/* Modern Typography System */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-gray-900);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

p {
  margin: 0 0 var(--spacing-4) 0;
  color: var(--color-gray-700);
  line-height: var(--line-height-relaxed);
}

/* Enhanced Prose Styles */
.prose {
  color: var(--color-gray-700);
  line-height: var(--line-height-relaxed);
  max-width: 65ch;
}

.prose p {
  margin-bottom: var(--spacing-6);
  line-height: var(--line-height-relaxed);
}

.prose strong {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

.prose a {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
}

.prose a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Lists */
ul, ol {
  margin: 0 0 var(--spacing-4) 0;
  padding-left: var(--spacing-6);
}

li {
  margin-bottom: var(--spacing-2);
  color: var(--color-gray-700);
  line-height: var(--line-height-relaxed);
}

.prose h1, .prose h2, .prose h3, .prose h4 {
  font-weight: 600;
  color: var(--color-gray-900);
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.prose h1 { font-size: 2.25rem; }
.prose h2 { font-size: 1.875rem; }
.prose h3 { font-size: 1.5rem; }
.prose h4 { font-size: 1.25rem; }

.prose strong {
  font-weight: 600;
  color: var(--color-gray-900);
}

.prose ul, .prose ol {
  margin-left: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.prose li {
  margin-bottom: var(--spacing-xs);
}

.prose a {
  color: var(--color-primary);
  text-decoration: underline;
  text-underline-offset: 2px;
  transition: color 0.2s ease;
}

.prose a:hover {
  color: var(--color-primary-dark);
}

/* Modern Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

/* Container Classes */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-sm {
  max-width: 640px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-lg {
  max-width: 1536px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 160px);
}

/* Modern Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-lg);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
}

.btn:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-base);
}

.btn-xl {
  padding: var(--spacing-5) var(--spacing-10);
  font-size: var(--font-size-lg);
}

/* Button Variants */
.btn-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  color: var(--color-white);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--color-white);
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
  color: var(--color-gray-900);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-outline:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-gray-700);
  border-color: transparent;
}

.btn-ghost:hover {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
}

/* Modern Card System */
.card {
  background-color: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-base);
}

.card-sm {
  padding: var(--spacing-4);
  border-radius: var(--radius-xl);
}

.card-lg {
  padding: var(--spacing-8);
  border-radius: var(--radius-3xl);
}

.card-hover {
  transition: all var(--transition-base);
  cursor: pointer;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-gray-300);
}

.card-interactive:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-200);
}

/* Card Variants */
.card-elevated {
  box-shadow: var(--shadow-md);
  border: none;
}

.card-bordered {
  border: 2px solid var(--color-gray-200);
  box-shadow: none;
}

.card-gradient {
  background: linear-gradient(135deg, var(--color-white) 0%, var(--color-gray-50) 100%);
  border: 1px solid var(--color-gray-100);
}

/* Modern Grid System */
.grid {
  display: grid;
  gap: var(--spacing-6);
}

.grid-gap-sm { gap: var(--spacing-4); }
.grid-gap-md { gap: var(--spacing-6); }
.grid-gap-lg { gap: var(--spacing-8); }

.grid-cols-1 {
  grid-template-columns: 1fr;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Auto-fit responsive grids */
.grid-auto-fit-sm {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-auto-fit-md {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fit-lg {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* Responsive Grid Breakpoints */
@media (max-width: 640px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }

  .grid {
    gap: var(--spacing-4);
  }
}

@media (max-width: 768px) {
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1024px) {
  .grid-cols-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Modern Animations */
.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
  animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading Animations */
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-gray-200);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern Form Styles */
.form-group {
  margin-bottom: var(--spacing-6);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: var(--font-family-primary);
  background-color: var(--color-white);
  transition: all var(--transition-fast);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled,
.form-textarea:disabled,
.form-select:disabled {
  background-color: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
}

.form-error {
  border-color: var(--color-error);
}

.form-error:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-help-text {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  margin-top: var(--spacing-1);
}

.form-error-text {
  font-size: var(--font-size-sm);
  color: var(--color-error);
  margin-top: var(--spacing-1);
}

/* Focus States */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

button:focus,
a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Status Indicators */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

.badge-primary {
  background-color: var(--color-primary-100);
  color: var(--color-primary-700);
}

.badge-success {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.badge-warning {
  background-color: var(--color-warning-light);
  color: var(--color-warning);
}

.badge-error {
  background-color: var(--color-error-light);
  color: var(--color-error);
}

/* Alert Components */
.alert {
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  border: 1px solid;
  margin-bottom: var(--spacing-4);
}

.alert-success {
  background-color: var(--color-success-light);
  border-color: var(--color-success);
  color: var(--color-success);
}

.alert-error {
  background-color: var(--color-error-light);
  border-color: var(--color-error);
  color: var(--color-error);
}

/* Spacing Utilities */
.mt-0 { margin-top: 0 !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mt-6 { margin-top: var(--spacing-6) !important; }
.mt-8 { margin-top: var(--spacing-8) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }
.mb-6 { margin-bottom: var(--spacing-6) !important; }
.mb-8 { margin-bottom: var(--spacing-8) !important; }

.p-4 { padding: var(--spacing-4) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }

/* Responsive Utilities */
@media (max-width: 640px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
