import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment.development';
import { map, catchError, switchMap } from 'rxjs/operators';
import { MBTIQuestion } from '../interfaces/mbti-question';
import { Observable, of } from 'rxjs';
import { ChartData, ChartOptions, ChartType } from 'chart.js';

// Interface untuk hasil MBTI
export interface MbtiReport {
  mbtiResult: string;
  categoryTotals: { [key: string]: number };
  chartData: ChartData<'bar'>;
}

// Interface untuk analisis MBTI
export interface MbtiAnalysis {
  pattern: string;
  patternName: string;
  patternSlogan: string;
  analysisDetail: string;
}

@Injectable({
  providedIn: 'root',
})
export class MbtiService {
  url = `${environment.url}` + 'mbti-questions?populate=*&pagination[limit]=-1';
  urlPost = `${environment.url}` + 'mbti-answers';
  urlAnalyzer = `${environment.url}` + 'mbti-analyzers';

  constructor(private http: HttpClient) {}

  getQuestion() {
    return this.http.get(this.url).pipe(
      map((response: any) => {
        return response.data.map((item: any) => ({
          id: item.id,
          attributes: {
            leftText: item.attributes.leftText,
            rightText: item.attributes.rightText,
            category: item.attributes.category,
          },
        }));
      })
    );
  }

  saveAnswer(userId: number, answers: any[], testManagerId?: number) {
    const payload: any = {
      data: {
        answer: answers,
        user: userId,
      },
    };

    // Tambahkan testManagerId ke payload jika ada
    if (testManagerId) {
      payload.data.testManagerId = testManagerId;
    }

    return this.http.post(this.urlPost, payload, {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  getResult(userId: number) {
    return this.http.get(`${this.urlPost}?filters[user][id][$eq]=${userId}`);
  }

  getResultById(answerId: number) {
    return this.http.get(`${this.urlPost}/${answerId}?populate=*`);
  }

  /**
   * Mendapatkan analisis MBTI berdasarkan pola
   * @param pattern Pola MBTI (contoh: "ESTJ")
   * @returns Observable berisi data analisis
   */
  getAnalyzer(pattern: string): Observable<any> {
    if (pattern) {
      return this.http
        .get(`${this.urlAnalyzer}?filters[pattern][$eq]=${pattern}`)
        .pipe(
          catchError((error) => {
            console.error('Error fetching MBTI analyzer data:', error);
            return of({ data: [] });
          })
        );
    }
    return of({ data: [] });
  }

  /**
   * Menghitung hasil MBTI berdasarkan jawaban
   * @param answers Array jawaban
   * @returns String hasil MBTI (contoh: "ESTJ")
   */
  calculateMBTI(answers: any[]): string {
    // Objek untuk menyimpan total value per kategori
    const categoryTotals: { [key: string]: number } = {
      EI: 0,
      SN: 0,
      TF: 0,
      JP: 0,
    };

    // Loop melalui jawaban dan hitung total value per kategori
    answers.forEach((answer) => {
      const { category, value } = answer;
      if (category in categoryTotals) {
        categoryTotals[category] += value;
      }
    });

    // Tentukan hasil MBTI berdasarkan total value
    const result = Object.keys(categoryTotals).map((category) => {
      const totalValue = categoryTotals[category];
      if (totalValue < 0) {
        return category[0]; // Ambil karakter pertama jika total < 0
      } else {
        return category[1]; // Ambil karakter kedua jika total >= 0
      }
    });

    // Gabungkan hasil menjadi string MBTI
    return result.join('');
  }

  /**
   * Menghasilkan data chart berbasis horizontal stacked bar
   * @param answers Array jawaban
   * @returns ChartData untuk horizontal stacked bar chart
   */
  generateHorizontalStackedBarData(answers: any[]): ChartData<'bar'> {
    // Objek untuk menyimpan total value per kategori
    const categoryTotals: { [key: string]: number } = {
      EI: 0,
      SN: 0,
      TF: 0,
      JP: 0,
    };

    // Loop melalui jawaban dan hitung total value per kategori
    answers.forEach((answer) => {
      const { category, value } = answer;
      if (category in categoryTotals) {
        categoryTotals[category] += value;
      }
    });

    // Konversi data ke format yang sesuai untuk horizontal stacked bar chart
    const labels = Object.keys(categoryTotals).map((category) => {
      const totalValue = categoryTotals[category];
      return `${totalValue < 0 ? category[0] : category[1]} (${Math.abs(
        totalValue
      )})`;
    });

    // Pisahkan nilai positif dan negatif
    const positiveData = Object.values(categoryTotals).map((value) =>
      Math.max(value, 0)
    );
    const negativeData = Object.values(categoryTotals).map((value) =>
      Math.min(value, 0)
    );

    return {
      labels: labels,
      datasets: [
        {
          label: 'Negatif',
          data: negativeData,
          backgroundColor: 'rgba(255, 99, 132, 0.6)', // Merah
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
        },
        {
          label: 'Positif',
          data: positiveData,
          backgroundColor: 'rgba(75, 192, 192, 0.6)', // Biru
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1,
        },
      ],
    };
  }

  /**
   * Mendapatkan opsi chart default untuk MBTI
   * @returns Opsi chart yang siap digunakan
   */
  getMbtiChartOptions(): ChartOptions {
    return {
      responsive: true,
      maintainAspectRatio: false,
      indexAxis: 'y', // Membuat grafik horizontal
      plugins: {
        legend: {
          display: false, // Menyembunyikan legenda
        },
      },
      scales: {
        x: {
          beginAtZero: true,
          stacked: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)', // Garis grid tipis
          },
          ticks: {
            stepSize: 1, // Interval pada sumbu X
          },
        },
        y: {
          grid: {
            display: false, // Menghilangkan garis grid vertikal
          },
        },
      },
    };
  }

  // Ambil semua hasil MBTI (untuk admin)
  getAllResults(): Observable<any> {
    return this.http.get(`${this.urlPost}?populate=user`);
  }

  // Tambahkan method untuk mendapatkan hasil berdasarkan test manager ID
  getResultByTestManagerId(testManagerId: number): Observable<any> {
    return this.http.get(
      `${this.urlPost}?filters[testManagerId][$eq]=${testManagerId}`
    );
  }

  /**
   * Mendapatkan laporan MBTI lengkap berdasarkan ID jawaban
   * @param answerId ID jawaban
   * @returns Observable berisi laporan MBTI lengkap
   */
  getMbtiReportById(answerId: number): Observable<MbtiReport> {
    return this.getResultById(answerId).pipe(
      map((response: any) => {
        const answers = response.data.attributes.answer;

        // Hitung hasil MBTI
        const mbtiResult = this.calculateMBTI(answers);

        // Objek untuk menyimpan total value per kategori
        const categoryTotals: { [key: string]: number } = {
          EI: 0,
          SN: 0,
          TF: 0,
          JP: 0,
        };

        // Loop melalui jawaban dan hitung total value per kategori
        answers.forEach((answer: any) => {
          const { category, value } = answer;
          if (category in categoryTotals) {
            categoryTotals[category] += value;
          }
        });

        // Generate data chart
        const chartData = this.generateHorizontalStackedBarData(answers);

        return {
          mbtiResult,
          categoryTotals,
          chartData,
        };
      }),
      catchError((error) => {
        console.error('Error generating MBTI report:', error);
        // Return empty report
        return of({
          mbtiResult: '',
          categoryTotals: { EI: 0, SN: 0, TF: 0, JP: 0 },
          chartData: this.generateHorizontalStackedBarData([]),
        });
      })
    );
  }

  /**
   * Mendapatkan analisis MBTI lengkap berdasarkan pola
   * @param pattern Pola MBTI (contoh: "ESTJ")
   * @returns Observable berisi analisis MBTI lengkap
   */
  getMbtiAnalysis(pattern: string): Observable<MbtiAnalysis> {
    if (!pattern) {
      return of({
        pattern: '',
        patternName: 'Tidak Ditemukan',
        patternSlogan: 'Tidak Ditemukan',
        analysisDetail: 'Tidak ada data pola yang ditemukan.',
      });
    }

    return this.getAnalyzer(pattern).pipe(
      map((analyzer: any) => {
        if (!analyzer.data || analyzer.data.length === 0) {
          return {
            pattern,
            patternName: 'Tidak Ditemukan',
            patternSlogan: 'Tidak Ditemukan',
            analysisDetail: `Tidak ada data analisis yang ditemukan untuk pola ${pattern}.`,
          };
        }

        return {
          pattern,
          patternName:
            analyzer.data[0].attributes?.pattern || 'Tidak Ditemukan',
          patternSlogan:
            analyzer.data[0].attributes?.patternSlogan || 'Tidak Ditemukan',
          analysisDetail:
            analyzer.data[0].attributes?.hasil || 'Tidak Ditemukan',
        };
      }),
      catchError((error) => {
        console.error('Error generating MBTI analysis:', error);
        return of({
          pattern,
          patternName: 'Error',
          patternSlogan: 'Error',
          analysisDetail: 'Terjadi kesalahan saat mengambil data analisis.',
        });
      })
    );
  }

  /**
   * Mendapatkan laporan dan analisis MBTI lengkap berdasarkan ID jawaban
   * @param answerId ID jawaban
   * @returns Observable berisi laporan dan analisis MBTI lengkap
   */
  getCompleteMbtiReport(
    answerId: number
  ): Observable<{ report: MbtiReport; analysis: MbtiAnalysis }> {
    return this.getMbtiReportById(answerId).pipe(
      switchMap((report: MbtiReport) => {
        return this.getMbtiAnalysis(report.mbtiResult).pipe(
          map((analysis: MbtiAnalysis) => ({
            report,
            analysis,
          }))
        );
      })
    );
  }
}
