import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { map, catchError } from 'rxjs/operators';
import { User } from '../interfaces/user';

// Interface untuk response Strapi
interface StrapiUser {
  id: number;
  username: string;
  email: string;
  blocked: boolean;
  confirmed: boolean;
  createdAt: string;
  updatedAt: string;
  provider: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private apiUrl = `${environment.url}` + 'auth/local';
  private registerUrl = `${environment.url}` + 'auth/local/register';
  private usersApiUrl = `${environment.url}` + 'users'; // Sesuaikan dengan endpoint Strapi

  constructor(private http: HttpClient) {}

  login(email: string, password: string): Observable<any> {
    return this.http.post(this.apiUrl, {
      identifier: email,
      password: password,
    });
  }

  register(username: string, email: string, password: string): Observable<any> {
    return this.http.post(this.registerUrl, {
      username,
      email,
      password,
    });
  }

  saveUserData(token: string, user: any): void {
    localStorage.setItem('jwt', token); // Menyimpan token

    // Periksa struktur role dari Strapi
    let userRole = 'member'; // Default role

    // Cek berbagai kemungkinan struktur data role dari Strapi
    if (user.role?.type) {
      userRole = user.role.type;
    } else if (user.role?.name) {
      userRole = user.role.name;
    } else if (user.roles && user.roles.length > 0) {
      // Jika user memiliki array roles
      const adminRole = user.roles.find(
        (r: any) =>
          (r.name && r.name.toLowerCase() === 'admin') ||
          (r.type && r.type.toLowerCase() === 'admin')
      );
      if (adminRole) {
        userRole = 'admin';
      }
    }

    // Konversi nama role ke lowercase untuk konsistensi
    userRole = typeof userRole === 'string' ? userRole.toLowerCase() : 'member';

    // Jika username adalah admin, berikan role admin
    if (user.username && user.username.toLowerCase() === 'admin') {
      userRole = 'admin';
    }

    const userData = {
      ...user,
      role: userRole,
    };

    localStorage.setItem('user', JSON.stringify(userData));
  }

  // Dapatkan token JWT dari localStorage
  getToken(): string | null {
    return localStorage.getItem('jwt');
  }

  // Mendapatkan user yang sedang login dari localStorage
  getCurrentUser(): any {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  }

  // Logout: Hapus semua data login
  logout(): void {
    localStorage.removeItem('jwt');
    localStorage.removeItem('user');
  }

  // Cek apakah user sudah login (dengan memeriksa token JWT)
  isLoggedIn(): boolean {
    return !!this.getToken(); // Mengembalikan true jika token ada
  }

  // Cek apakah user memiliki role tertentu
  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user && user.role === role;
  }

  // Cek apakah user adalah admin
  isAdmin(): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Cek berbagai kemungkinan nilai role admin
    const role = user.role ? user.role.toLowerCase() : '';
    return (
      role === 'admin' ||
      role === 'administrator' ||
      (user.username && user.username.toLowerCase() === 'admin')
    );
  }

  getUsers(): Observable<User[]> {
    return this.http.get<StrapiUser[]>(this.usersApiUrl).pipe(
      map((response) => {
        return response.map((item) => ({
          id: item.id.toString(),
          name: item.username,
          email: item.email,
          role: 'user',
          blocked: item.blocked,
          confirmed: item.confirmed,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          provider: item.provider,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching users:', error);
        throw error;
      })
    );
  }

  // Fungsi untuk mengambil role user dari Strapi
  getUserRole(userId: number): Observable<any> {
    // Endpoint untuk Strapi v4
    return this.http.get(`${this.usersApiUrl}/me?populate=role`).pipe(
      map((response: any) => {
        // Ekstrak role dari respons
        let role = 'member'; // Default role

        // Cek berbagai kemungkinan struktur data role dari Strapi v4
        if (response.role) {
          // Prioritaskan name daripada type
          if (response.role.name) {
            role = response.role.name;
          } else if (response.role.type) {
            role = response.role.type;
          }
        }

        // Konversi nama role ke lowercase untuk konsistensi
        role = typeof role === 'string' ? role.toLowerCase() : 'member';

        // Jika role adalah "admin" (case insensitive), pastikan disimpan sebagai "admin"
        if (role.toLowerCase() === 'admin') {
          role = 'admin';
        }

        // Update user di localStorage
        const user = this.getCurrentUser();
        if (user) {
          user.role = role;
          localStorage.setItem('user', JSON.stringify(user));
        }

        return role;
      })
    );
  }
}
