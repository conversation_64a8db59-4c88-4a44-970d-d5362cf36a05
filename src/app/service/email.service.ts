import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment.development';

export interface EmailRequest {
  to: string;
  subject: string;
  html: string;
  attachments?: Array<{
    filename: string;
    content: string; // Base64 encoded content
    contentType: string;
  }>;
}

@Injectable({
  providedIn: 'root'
})
export class EmailService {
  private apiUrl = `${environment.url}email`;

  constructor(private http: HttpClient) { }

  // Send email with test results
  sendTestResults(emailRequest: EmailRequest): Observable<any> {
    // For development, return mock data if API is not ready
    if (environment.useMockData) {
      return this.getMockEmailResponse(emailRequest);
    }

    return this.http.post(this.apiUrl, emailRequest).pipe(
      catchError(error => {
        console.error('Error sending email:', error);
        return this.getMockEmailResponse(emailRequest);
      })
    );
  }

  // Mock email response for development
  private getMockEmailResponse(request: EmailRequest): Observable<any> {
    console.log('Mock email sent to:', request.to);
    console.log('Subject:', request.subject);
    
    // Simulate network delay
    return new Observable(observer => {
      setTimeout(() => {
        observer.next({
          success: true,
          messageId: `mock_email_${Date.now()}`,
          to: request.to
        });
        observer.complete();
      }, 1500);
    });
  }
}
