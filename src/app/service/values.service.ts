import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { environment } from 'src/environments/environment.development';
import { ChartData, ChartOptions, ChartType } from 'chart.js';

type Category = 'L' | 'E' | 'P' | 'J';

interface Answer {
  category: Category;
  value: number;
}

interface CategoryResult {
  category: Category;
  value: number;
}

// Interface untuk hasil Values
export interface ValuesReport {
  categoryTotals: Record<Category, number>;
  highestCategories: CategoryResult[];
  chartData: ChartData<'line'>;
}

// Interface untuk analisis Values
export interface ValuesAnalysis {
  mainCategory: string;
  mainCategoryName: string;
  mainCategoryValue: number;
  analysisDetail: string;
}
@Injectable({
  providedIn: 'root',
})
export class ValuesService {
  url =
    `${environment.url}` + 'values-questions?populate=*&pagination[limit]=-1';
  urlPost = `${environment.url}` + 'values-answers';
  urlAnalyzer = `${environment.url}` + 'values-analyzers';

  filterMap: Record<string, string> = {
    L: 'Loyalty',
    E: 'Equality',
    P: 'Personal Freedom',
    J: 'Justice',
  };

  constructor(private http: HttpClient) {}

  getQuestion() {
    return this.http.get(this.url);
  }

  saveAnswer(userId: number, answers: any[], testManagerId?: number) {
    const payload: any = {
      data: {
        answer: answers,
        user: userId,
      },
    };

    // Tambahkan testManagerId ke payload jika ada
    if (testManagerId) {
      payload.data.testManagerId = testManagerId;
    }

    return this.http.post(this.urlPost, payload, {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  getResult(userId: number) {
    return this.http.get(`${this.urlPost}?filters[user][id][$eq]=${userId}`);
  }

  getResultById(answerId: number) {
    return this.http.get(`${this.urlPost}/${answerId}?populate=*`);
  }

  /**
   * Menghitung total nilai untuk setiap kategori Values dari jawaban
   * @param answers Array jawaban atau objek jawaban dari API
   * @returns Record berisi total nilai untuk setiap kategori
   */
  calculateReport(answers: any): Record<Category, number> {
    const categoryTotals: Record<Category, number> = {
      L: 0,
      E: 0,
      P: 0,
      J: 0,
    };

    const answersArray: Answer[] = Array.isArray(answers)
      ? answers
      : answers.data || [];

    for (const item of answersArray) {
      if (item.category && categoryTotals.hasOwnProperty(item.category)) {
        categoryTotals[item.category] += Number(item.value);
      }
    }

    return categoryTotals;
  }

  /**
   * Mendapatkan data chart untuk Values berdasarkan hasil perhitungan
   * @param categoryTotals Hasil perhitungan kategori
   * @returns Data chart yang siap digunakan
   */
  getValuesChartData(
    categoryTotals: Record<Category, number>
  ): ChartData<'line'> {
    const labels = ['L', 'E', 'P', 'J'];

    return {
      labels,
      datasets: [
        {
          data: Object.values(categoryTotals),
          fill: false,
          borderColor: '#FFA726',
          tension: 0.1,
        },
      ],
    };
  }

  /**
   * Mendapatkan opsi chart default untuk Values
   * @returns Opsi chart yang siap digunakan
   */
  getValuesChartOptions(): ChartOptions {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        y: {
          beginAtZero: false,
          min: 10,
          max: 40,
          ticks: {
            stepSize: 1,
          },
          grid: {
            color: (context) => {
              // Ubah warna garis grid pada nilai tertentu
              if (context.tick.value === 25) {
                return 'rgba(0, 0, 0, 1)'; // Hitam untuk garis tebal
              }
              return 'rgba(0, 0, 0, 0.1)'; // Abu-abu untuk garis biasa
            },
            lineWidth: (context) => {
              if (context.tick.value === 25) {
                return 2;
              }
              return 1;
            },
          },
        },
      },
    };
  }

  /**
   * Mendapatkan kategori dengan nilai tertinggi di atas threshold
   * @param categoryTotals Hasil perhitungan kategori
   * @param threshold Nilai threshold (default: 30)
   * @param maxResults Jumlah maksimum hasil (default: 2)
   * @returns Array berisi kategori dengan nilai tertinggi
   */
  getHighestCategory(
    categoryTotals: Record<Category, number>,
    threshold: number = 30,
    maxResults: number = 2
  ): CategoryResult[] {
    const sortedCategories = Object.entries(categoryTotals)
      .sort(([, a], [, b]) => {
        return b - a;
      })
      .filter(([, value]) => value >= threshold)
      .map(([category, value]) => ({
        category: category as Category,
        value: value,
      }));

    if (sortedCategories.length >= 1) {
      return sortedCategories.slice(0, maxResults);
    }

    // Jika tidak ada kategori di atas threshold, ambil yang tertinggi
    const highest = Object.entries(categoryTotals).reduce(
      (highest, current) => {
        return current[1] > highest[1] ? current : highest;
      }
    );

    return [
      {
        category: highest[0] as Category,
        value: highest[1],
      },
    ];
  }

  /**
   * Mendapatkan analisis untuk kategori tertentu
   * @param category Kode kategori (L, E, P, J)
   * @returns Observable berisi data analisis
   */
  getAnalyzer(category: string): Observable<any> {
    const name = this.filterMap[category];
    if (name) {
      return this.http
        .get(`${this.urlAnalyzer}?filters[name][$eq]=${name}`)
        .pipe(
          catchError((error) => {
            console.error('Error fetching analyzer data:', error);
            return of({ data: [] });
          })
        );
    }
    return of({ data: [] });
  }

  // Ambil semua hasil Values (untuk admin)
  getAllResults(): Observable<any> {
    return this.http.get(`${this.urlPost}?populate=user`);
  }

  // Tambahkan method untuk mendapatkan hasil berdasarkan test manager ID
  getResultByTestManagerId(testManagerId: number): Observable<any> {
    return this.http.get(
      `${this.urlPost}?filters[testManagerId][$eq]=${testManagerId}`
    );
  }

  /**
   * Mendapatkan laporan Values lengkap berdasarkan ID jawaban
   * @param answerId ID jawaban
   * @returns Observable berisi laporan Values lengkap
   */
  getValuesReportById(answerId: number): Observable<ValuesReport> {
    return this.getResultById(answerId).pipe(
      map((answer: any) => {
        const valuesAnswers = answer?.data?.attributes?.answer || [];
        const categoryTotals = this.calculateReport(valuesAnswers);
        const highestCategories = this.getHighestCategory(categoryTotals);
        const chartData = this.getValuesChartData(categoryTotals);

        return {
          categoryTotals,
          highestCategories,
          chartData,
        };
      }),
      catchError((error) => {
        console.error('Error generating Values report:', error);
        // Return empty report
        const emptyCategoryTotals = { L: 0, E: 0, P: 0, J: 0 };
        return of({
          categoryTotals: emptyCategoryTotals,
          highestCategories: [],
          chartData: this.getValuesChartData(emptyCategoryTotals),
        });
      })
    );
  }

  /**
   * Mendapatkan analisis Values lengkap berdasarkan kategori tertinggi
   * @param highestCategories Array kategori tertinggi
   * @returns Observable berisi analisis Values lengkap
   */
  getValuesAnalysis(
    highestCategories: CategoryResult[]
  ): Observable<ValuesAnalysis> {
    if (highestCategories.length === 0) {
      return of({
        mainCategory: '',
        mainCategoryName: '',
        mainCategoryValue: 0,
        analysisDetail: 'Tidak ada data kategori yang ditemukan.',
      });
    }

    const firstCategory = highestCategories[0].category;
    const firstCategoryName = this.filterMap[firstCategory];
    const firstCategoryValue = highestCategories[0].value;

    return this.getAnalyzer(firstCategory).pipe(
      map((analyzer: any) => {
        return {
          mainCategory: firstCategory,
          mainCategoryName:
            analyzer?.data[0]?.attributes?.name || 'Tidak Ditemukan',
          mainCategoryValue: firstCategoryValue,
          analysisDetail:
            analyzer?.data[0]?.attributes?.detail || 'Tidak Ditemukan',
        };
      }),
      catchError((error) => {
        console.error('Error generating Values analysis:', error);
        return of({
          mainCategory: firstCategory,
          mainCategoryName: firstCategoryName || '',
          mainCategoryValue: firstCategoryValue,
          analysisDetail: 'Terjadi kesalahan saat mengambil data analisis.',
        });
      })
    );
  }

  /**
   * Mendapatkan laporan dan analisis Values lengkap berdasarkan ID jawaban
   * @param answerId ID jawaban
   * @returns Observable berisi laporan dan analisis Values lengkap
   */
  getCompleteValuesReport(
    answerId: number
  ): Observable<{ report: ValuesReport; analysis: ValuesAnalysis }> {
    return this.getValuesReportById(answerId).pipe(
      switchMap((report: ValuesReport) => {
        return this.getValuesAnalysis(report.highestCategories).pipe(
          map((analysis: ValuesAnalysis) => ({
            report,
            analysis,
          }))
        );
      })
    );
  }
}
