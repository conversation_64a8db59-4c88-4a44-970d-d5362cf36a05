import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {
  BehaviorSubject,
  Observable,
  catchError,
  map,
  forkJoin,
  switchMap,
  tap,
  of,
} from 'rxjs';
import { TestAccess, CreateTestAccess } from '../interfaces/test-access';
import { environment } from 'src/environments/environment.development';
import { Router } from '@angular/router';
import { AuthService } from './auth.service';

/**
 * Interface for the response from the test pack API
 */
interface TestPackResponse {
  data: {
    id: number;
    attributes: {
      createdAt: string;
      updatedAt: string;
      status: string;
      users: {
        data: Array<{
          id: number;
          attributes: {
            username: string;
            email: string;
            // Add other user attributes as needed
          };
        }>;
      };
      tests?: {
        data: Array<{
          id: number;
          attributes: {
            type: string;
            isCompleted: boolean;
            order: number;
            route: string;
            resultId?: number;
            // Add other test attributes as needed
          };
        }>;
      };
      testManager?: {
        data: Array<{
          id: number;
          attributes: {
            type: string;
            isCompleted: boolean;
            order: number;
            route: string;
            resultId?: number;
          };
        }>;
      };
    };
  };
}

@Injectable({
  providedIn: 'root',
})
export class TestManagerService {
  /**
   * Get a test pack by ID
   * @param testPackId The ID of the test pack to retrieve
   * @returns Observable with the test pack data
   */
  getTestPackById(testPackId: number): Observable<TestPackResponse> {
    return this.http
      .get<TestPackResponse>(`${this.testPackUrl}/${testPackId}?populate=*`, {
        headers: this.getHeaders(),
      })
      .pipe(
        catchError((error) => {
          console.error(
            `Error fetching test pack with ID ${testPackId}:`,
            error
          );
          throw error;
        })
      );
  }
  private testPackUrl = `${environment.url}test-packs`;
  private testManagerUrl = `${environment.url}test-managers`;
  private currentTestIndex = new BehaviorSubject<number>(0);
  private userTests = new BehaviorSubject<TestAccess[]>([]);
  private testStatusChanged = new BehaviorSubject<boolean>(false);

  constructor(
    private http: HttpClient,
    private router: Router,
    private authService: AuthService
  ) {}

  private getHeaders(): HttpHeaders {
    const token = this.authService.getToken();
    return new HttpHeaders({
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    });
  }

  /**
   * Creates a new test pack for a user
   * @param userId The ID of the user to create the test pack for (can be string or number)
   * @returns Observable with the created test pack ID and other details
   */
  createTestPack(
    userId: string | number
  ): Observable<{ id: number; userId: number; createdAt?: string }> {
    // For development, return mock data if API is not ready
    if (environment.useMockData) {
      return of({
        id: Math.floor(Math.random() * 1000) + 1,
        userId: typeof userId === 'string' ? Number(userId) : userId,
        createdAt: new Date().toISOString(),
      });
    }

    // Ensure userId is valid
    if (userId === null || userId === undefined) {
      throw new Error('Invalid userId provided');
    }

    // Convert userId to number if it's a string
    const numericUserId = typeof userId === 'string' ? Number(userId) : userId;

    if (isNaN(numericUserId)) {
      throw new Error('Invalid userId provided: not a number');
    }

    // Prepare the request data for Strapi relations
    const requestData = {
      data: {
        users: {
          connect: [numericUserId],
        },
        status: 'open', // Set to open (default status)
        paymentStatus: 'UNPAID', // Set payment status to UNPAID until payment is confirmed
      },
    };

    // Make the API call
    return this.http
      .post<{ data: { id: number; attributes?: any } }>(
        this.testPackUrl,
        requestData,
        { headers: this.getHeaders() }
      )
      .pipe(
        map((response) => ({
          id: response.data.id,
          userId: numericUserId,
          createdAt:
            response.data.attributes?.createdAt || new Date().toISOString(),
        })),
        catchError((error) => {
          console.error('Error creating test pack:', error);
          // Return mock data on error for development
          if (environment.useMockData) {
            return of({
              id: Math.floor(Math.random() * 1000) + 1,
              userId: numericUserId,
              createdAt: new Date().toISOString(),
            });
          }
          throw error;
        })
      );
  }

  /**
   * Assigns tests to a test pack
   * @param testPackId The ID of the test pack to assign tests to
   * @param tests Array of test configurations to assign
   * @returns Observable with the result of the assignment
   */
  assignTestsToTestPack(
    testPackId: number,
    tests: CreateTestAccess[] | any[]
  ): Observable<any> {
    // For development, return mock data if API is not ready
    if (environment.useMockData) {
      return of({
        success: true,
        testPackId: testPackId,
        testsAssigned: tests.length,
      });
    }

    // Format data according to Strapi expectations
    const testManagerData = tests.map((test) => ({
      data: {
        type: test.type,
        isCompleted: test.isCompleted || false,
        order: test.order,
        route: test.route,
        test_packs: testPackId,
      },
    }));

    // Send a request for each test separately
    const requests = testManagerData.map((data) =>
      this.http.post(this.testManagerUrl, data, { headers: this.getHeaders() })
    );

    // Use forkJoin to run all requests in parallel
    return forkJoin(requests).pipe(
      map((responses) => ({
        success: true,
        testPackId: testPackId,
        testsAssigned: responses.length,
        responses: responses,
      })),
      catchError((error) => {
        console.error('Error assigning tests to test pack:', error);
        // Return mock data on error for development
        if (environment.useMockData) {
          return of({
            success: true,
            testPackId: testPackId,
            testsAssigned: tests.length,
          });
        }
        throw error;
      })
    );
  }

  // Method untuk mendapatkan observable perubahan status
  getTestStatusChanges(): Observable<boolean> {
    return this.testStatusChanged.asObservable();
  }

  // Method untuk memperbarui data test
  refreshTests() {
    const userId = this.authService.getCurrentUser().id;
    this.getUserTestAccess(userId).subscribe({
      next: (tests) => {
        this.userTests.next(tests);
        // Trigger perubahan status
        this.testStatusChanged.next(true);
      },
      error: (error) => {
        console.error('Error refreshing tests:', error);
      },
    });
  }

  // Get test access dari Strapi
  getUserTestAccess(userId: number): Observable<TestAccess[]> {
    console.log('Fetching test access for user ID:', userId);
    return this.http
      .get<{
        data: Array<{
          id: number;
          attributes: {
            users: {
              data: Array<{ id: number }>;
            };
            tests: {
              data: Array<{
                id: number;
                attributes: {
                  type: string;
                  isCompleted: boolean;
                  order: number;
                  route: string;
                };
              }>;
            };
            status?: string;
            paymentStatus?: string;
            createdAt?: string;
          };
        }>;
      }>(
        // Modified query: get all test packs for user with any paymentStatus (PAID or UNPAID)
        // This will help catch test packs that might not have been updated yet
        `${this.testPackUrl}?populate=users,tests&filters[users][id][$eq]=${userId}&sort=createdAt:desc`
      )
      .pipe(
        map((response) => {
          console.log('API Response for test access:', response);
          if (!response.data || !Array.isArray(response.data)) {
            console.error('Invalid response format:', response);
            return [];
          }

          console.log('Found test packs:', response.data.length);

          // Filter and process test packs
          const validTestPacks = response.data.filter((testPack) => {
            const paymentStatus = testPack.attributes?.paymentStatus;
            const status = testPack.attributes?.status;

            console.log(
              'Processing test pack:',
              testPack.id,
              'Status:',
              status,
              'Payment Status:',
              paymentStatus
            );

            // Only include test packs that are PAID
            // This ensures users only get access to tests they have actually paid for
            return paymentStatus === 'PAID';
          });

          // Ambil test manager dari test pack yang valid
          const testManagers = validTestPacks.flatMap((testPack) => {
            if (!testPack?.attributes?.tests?.data) {
              console.warn('Invalid test pack structure:', testPack);
              return [];
            }

            return testPack.attributes.tests.data.map((item) => ({
              id: item.id,
              type: item.attributes.type as
                | 'DISC'
                | 'TEAMS'
                | 'VALUES'
                | 'MBTI',
              isCompleted: item.attributes.isCompleted,
              order: item.attributes.order,
              route: item.attributes.route,
              test_packs: testPack.id,
            }));
          });

          console.log('Final test managers:', testManagers);
          // Urutkan berdasarkan order
          return testManagers.sort((a, b) => a.order - b.order);
        }),
        catchError((error) => {
          console.error('Error fetching user test access:', error);
          return of([]); // Return empty array instead of throwing error
        })
      );
  }

  // Update status test di test manager
  updateTestStatus(testId: number, isCompleted: boolean): Observable<any> {
    const url = `${this.testManagerUrl}/${testId}`;
    const data = {
      data: {
        isCompleted: isCompleted,
      },
    };
    return this.http.put(url, data, { headers: this.getHeaders() }).pipe(
      tap(() => {
        // Emit perubahan status
        this.testStatusChanged.next(true);
      })
    );
  }

  // Update status test pack menjadi finish
  updateTestPackStatus(testPackId: number): Observable<any> {
    const url = `${this.testPackUrl}/${testPackId}`;
    const data = {
      data: {
        status: 'finish',
      },
    };
    return this.http.put(url, data, { headers: this.getHeaders() });
  }

  // Update status and payment status of test pack
  updateTestPackStatusAndPayment(
    testPackId: number,
    status: string,
    paymentStatus: string
  ): Observable<any> {
    const url = `${this.testPackUrl}/${testPackId}`;
    const data = {
      data: {
        status: status,
        paymentStatus: paymentStatus,
      },
    };
    return this.http.put(url, data, { headers: this.getHeaders() });
  }

  // Clean up expired pending test packs (older than 30 minutes)
  cleanupExpiredTestPacks(): Observable<any> {
    const thirtyMinutesAgo = new Date(
      Date.now() - 30 * 60 * 1000
    ).toISOString();

    return this.http
      .get<{
        data: Array<{
          id: number;
          attributes: {
            createdAt: string;
            status: string;
            paymentStatus: string;
          };
        }>;
      }>(
        `${this.testPackUrl}?filters[status][$eq]=pending&filters[paymentStatus][$eq]=PENDING&filters[createdAt][$lt]=${thirtyMinutesAgo}`
      )
      .pipe(
        switchMap((response) => {
          if (!response.data || response.data.length === 0) {
            return of({ cleaned: 0 });
          }

          // Delete expired test packs
          const deleteRequests = response.data.map((testPack) =>
            this.http.delete(`${this.testPackUrl}/${testPack.id}`, {
              headers: this.getHeaders(),
            })
          );

          return forkJoin(deleteRequests).pipe(
            map(() => ({ cleaned: response.data.length })),
            catchError((error) => {
              console.error('Error cleaning up expired test packs:', error);
              return of({ cleaned: 0, error: error });
            })
          );
        })
      );
  }

  // Update status test dan test pack
  completeTest(
    testId: number,
    testPackId: number,
    resultId?: number
  ): Observable<any> {
    // Jika ada resultId, simpan ke test manager
    let updateObs: Observable<any>;

    if (resultId) {
      updateObs = this.updateTestStatus(testId, true).pipe(
        switchMap(() => this.linkTestManagerWithResult(testId, resultId))
      );
    } else {
      updateObs = this.updateTestStatus(testId, true);
    }

    return updateObs.pipe(
      switchMap(() => {
        // Kemudian, cek apakah semua test dalam test pack sudah selesai
        return this.http
          .get<{
            data: {
              attributes: {
                tests: {
                  data: Array<{
                    attributes: {
                      isCompleted: boolean;
                    };
                  }>;
                };
              };
            };
          }>(`${this.testPackUrl}/${testPackId}?populate=tests`, {
            headers: this.getHeaders(),
          })
          .pipe(
            switchMap((response) => {
              const tests = response.data.attributes.tests.data;
              const allCompleted = tests.every(
                (test) => test.attributes.isCompleted
              );

              if (allCompleted) {
                return this.updateTestPackStatus(testPackId);
              }
              return of(null);
            })
          );
      })
    );
  }

  initializeTests(tests: TestAccess[]) {
    const sortedTests = tests.sort((a, b) => a.order - b.order);
    // Cari index test yang belum selesai pertama
    const nextIncompleteIndex = sortedTests.findIndex(
      (test) => !test.isCompleted
    );

    // Set index ke test yang belum selesai, atau 0 jika semua sudah selesai
    this.currentTestIndex.next(
      nextIncompleteIndex >= 0 ? nextIncompleteIndex : 0
    );
    this.userTests.next(sortedTests);
  }

  getCurrentTest(): TestAccess | null {
    return this.userTests.value[this.currentTestIndex.value] || null;
  }

  getNextTest(): TestAccess | null {
    const nextIndex = this.currentTestIndex.value + 1;
    return this.userTests.value[nextIndex] || null;
  }

  moveToNextTest() {
    const currentIndex = this.currentTestIndex.value;
    const nextIndex = currentIndex + 1;

    if (nextIndex < this.userTests.value.length) {
      this.currentTestIndex.next(nextIndex);
      this.router.navigate(['/home/<USER>']);
    } else {
      this.router.navigate(['/home/<USER>']);
    }
  }

  // Method untuk menghapus satu test
  deleteTest(testId: number): Observable<any> {
    return this.http.delete(`${this.testManagerUrl}/${testId}`);
  }

  // Method untuk menghapus semua test user
  deleteUserTests(userId: number): Observable<any> {
    return this.getUserTestAccess(userId).pipe(
      switchMap((tests) => {
        if (tests.length === 0) {
          return new Observable((observer) => {
            observer.next();
            observer.complete();
          });
        }

        console.log(
          'Menghapus test dengan ID:',
          tests.map((test) => test.id)
        );
        const deleteRequests = tests.map((test) =>
          this.deleteTest(test.id).pipe(
            catchError((error) => {
              console.error(
                `Gagal menghapus test dengan ID ${test.id}:`,
                error
              );
              return new Observable(); // Lanjutkan meskipun ada error
            })
          )
        );

        return forkJoin(deleteRequests);
      })
    );
  }

  // Method untuk mengecek dan menghapus test yang sudah selesai
  cleanupCompletedTests() {
    const userId = this.authService.getCurrentUser().id;
    const allTestsCompleted = this.userTests.value.every(
      (test) => test.isCompleted
    );

    if (allTestsCompleted) {
      setTimeout(() => {
        this.deleteUserTests(userId).subscribe({
          next: () => {
            this.userTests.next([]); // Kosongkan state setelah penghapusan
          },
          error: (error) => {
            console.error('Error saat menghapus test:', error);
          },
        });
      }, 3000);
    }
  }

  markCurrentTestComplete(resultId?: number) {
    const currentTest = this.getCurrentTest();
    if (currentTest) {
      const tests = [...this.userTests.value];
      const testToUpdateInTests = tests.find((t) => t.id === currentTest.id);
      if (testToUpdateInTests) {
        testToUpdateInTests.isCompleted = true;
        this.userTests.next(tests);
      }

      // Gunakan completeTest yang diperbarui untuk mengupdate status test dan test pack
      this.completeTest(
        currentTest.id,
        currentTest.test_packs,
        resultId
      ).subscribe({
        next: () => {
          this.refreshTests();
        },
        error: () => {
          if (testToUpdateInTests) {
            testToUpdateInTests.isCompleted = false;
            this.userTests.next(tests);
          }
        },
      });
    }
  }

  getProgress(): { current: number; total: number } {
    return {
      current: this.currentTestIndex.value + 1,
      total: this.userTests.value.length,
    };
  }

  // Tambahkan method untuk mendapatkan urutan test saat ini
  getCurrentTestOrder(): number {
    const currentTest = this.getCurrentTest();
    return currentTest ? currentTest.order : 0;
  }

  // Tambahkan method untuk mengecek apakah ini test terakhir
  isLastTest(): boolean {
    return this.currentTestIndex.value === this.userTests.value.length - 1;
  }

  assignTestsToUser(
    userId: string,
    tests: CreateTestAccess[]
  ): Observable<any> {
    // Implementasi untuk menyimpan data ke database
    return this.http.post(
      `${this.testManagerUrl}/users/${userId}/test-access`,
      { tests }
    );
  }

  getTestPackUrl(): string {
    return `${this.testPackUrl}`;
  }

  getTestPackResults(url: string): Observable<any> {
    return this.http.get(url, {
      headers: {
        Authorization: `Bearer ${this.authService.getToken()}`,
      },
    });
  }

  // Tambahkan method untuk mengaitkan test manager dengan hasil test
  linkTestManagerWithResult(
    testManagerId: number,
    resultId: number
  ): Observable<any> {
    const url = `${this.testManagerUrl}/${testManagerId}`;
    const data = {
      data: {
        resultId: resultId,
      },
    };
    return this.http.put(url, data, { headers: this.getHeaders() });
  }

  // Get the most recent test pack for a user (useful for recovery scenarios)
  getRecentTestPackForUser(userId: number): Observable<any> {
    console.log('Fetching recent test pack for user ID:', userId);
    return this.http
      .get<{
        data: Array<{
          id: number;
          attributes: {
            status?: string;
            paymentStatus?: string;
            createdAt: string;
          };
        }>;
      }>(
        `${this.testPackUrl}?populate=*&filters[users][id][$eq]=${userId}&sort=createdAt:desc&pagination[limit]=1`
      )
      .pipe(
        map((response) => {
          console.log('Recent test pack API response:', response);
          if (
            !response.data ||
            !Array.isArray(response.data) ||
            response.data.length === 0
          ) {
            return null;
          }
          return response.data[0];
        }),
        catchError((error) => {
          console.error('Error fetching recent test pack:', error);
          return of(null);
        })
      );
  }

  // The duplicated functions have been merged with the ones above
}
