import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment.development';

@Injectable({
  providedIn: 'root',
})
export class DiscResultService {
  private apiUrl = `${environment.url}` + 'disc-answers';

  constructor(private http: HttpClient) {}

  // Ambil data DISC Answers berdasarkan user ID
  getDiscAnswersByUser(userId: number): Observable<any> {
    const url = `${this.apiUrl}?filters[user][id][$eq]=${userId}`; // Filter berdasarkan user ID
    return this.http.get(url);
  }

  getDiscAnswerById(answerId: number) {
    const url = `${this.apiUrl}/${answerId}`; // Filter berdasarkan id result
    return this.http.get(url);
  }

  // Ambil semua data DISC Answers (untuk admin)
  getAllDiscAnswers(): Observable<any> {
    const url = `${this.apiUrl}?populate=user`; // Tambahkan populate user untuk mendapatkan data user
    return this.http.get(url);
  }

  // Tambahkan metode untuk mengambil hasil berdasarkan ID
  getResultById(resultId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${resultId}?populate=*`);
  }

  generateDISCString(
    counts: Record<'D' | 'I' | 'S' | 'C', number>,
    criteria: { D: number; I: number; S: number; C: number },
    isLeast: boolean = false
  ): string {
    const resultArray: ('D' | 'I' | 'S' | 'C')[] = [];

    if (isLeast) {
      if (counts.D <= criteria.D) resultArray.push('D');
      if (counts.I <= criteria.I) resultArray.push('I');
      if (counts.S <= criteria.S) resultArray.push('S');
      if (counts.C <= criteria.C) resultArray.push('C');

      resultArray.sort((a, b) => counts[a] - counts[b]);
    } else {
      if (counts.D >= criteria.D) resultArray.push('D');
      if (counts.I >= criteria.I) resultArray.push('I');
      if (counts.S >= criteria.S) resultArray.push('S');
      if (counts.C >= criteria.C) resultArray.push('C');

      resultArray.sort((a, b) => counts[b] - counts[a]);
    }

    return resultArray.join('');
  }

  normalizeValue(
    value: number,
    min: number,
    max: number,
    range: number
  ): number {
    if (max === min) {
      return min;
    }
    return ((value - min) / (max - min)) * range + 1;
  }

  getStringAnalyzer(pattern: string) {
    const url =
      `${environment.url}` + 'disc-analyzers?filters[pattern][$eq]=' + pattern;
    return this.http.get(url);
  }
}
