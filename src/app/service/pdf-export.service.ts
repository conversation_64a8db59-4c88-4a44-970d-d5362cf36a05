import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class PdfExportService {
  constructor() {}

  /**
   * Generate a PDF from an HTML element
   * @param element The HTML element to convert to PDF
   * @param filename The name of the PDF file
   */
  async generatePdf(element: HTMLElement, filename: string): Promise<void> {
    // Display loading message
    const loadingMessage = this.createLoadingMessage();
    document.body.appendChild(loadingMessage);

    try {
      // Dynamically import libraries to avoid bundling issues
      const html2canvasModule = await import('html2canvas');
      const html2canvas = html2canvasModule.default;
      const jsPDFModule = await import('jspdf');
      const { jsPDF } = jsPDFModule;

      // Create a new jsPDF instance
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      // Use a simpler approach - capture the entire element
      const canvas = await html2canvas(element, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff',
      });

      // Convert canvas to image
      const imgData = canvas.toDataURL('image/png');

      // Calculate the aspect ratio to fit the page width
      const aspectRatio = canvas.height / canvas.width;
      const imgWidth = pageWidth;
      const imgHeight = pageWidth * aspectRatio;

      // If the image is taller than the page, split it into multiple pages
      const pageCount = Math.ceil(imgHeight / pageHeight);

      for (let i = 0; i < pageCount; i++) {
        if (i > 0) {
          pdf.addPage();
        }

        // Calculate the portion of the image to add to this page
        const sourceY = ((i * pageHeight) / imgHeight) * canvas.height;
        const sourceHeight = Math.min(
          (pageHeight / imgHeight) * canvas.height,
          canvas.height - sourceY
        );

        pdf.addImage(
          imgData,
          'PNG',
          0,
          i === 0 ? 0 : -pageHeight * i, // Position the image to show the correct portion
          imgWidth,
          imgHeight
        );
      }

      // Save the PDF
      pdf.save(filename);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      // Remove loading message
      if (document.body.contains(loadingMessage)) {
        document.body.removeChild(loadingMessage);
      }
    }
  }

  /**
   * Generate a PDF from an HTML element and return it as a base64 string
   * @param element The HTML element to convert to PDF
   * @returns A Promise that resolves to a base64 string of the PDF
   */
  async generatePdfAsBase64(element: HTMLElement): Promise<string> {
    try {
      // Dynamically import libraries to avoid bundling issues
      const html2canvasModule = await import('html2canvas');
      const html2canvas = html2canvasModule.default;
      const jsPDFModule = await import('jspdf');
      const { jsPDF } = jsPDFModule;

      // Create a new jsPDF instance
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      // Use a simpler approach - capture the entire element
      const canvas = await html2canvas(element, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff',
      });

      // Convert canvas to image
      const imgData = canvas.toDataURL('image/png');

      // Calculate the aspect ratio to fit the page width
      const aspectRatio = canvas.height / canvas.width;
      const imgWidth = pageWidth;
      const imgHeight = pageWidth * aspectRatio;

      // If the image is taller than the page, split it into multiple pages
      const pageCount = Math.ceil(imgHeight / pageHeight);

      for (let i = 0; i < pageCount; i++) {
        if (i > 0) {
          pdf.addPage();
        }

        pdf.addImage(
          imgData,
          'PNG',
          0,
          i === 0 ? 0 : -pageHeight * i, // Position the image to show the correct portion
          imgWidth,
          imgHeight
        );
      }

      // Return the PDF as base64 string
      return pdf.output('datauristring').split(',')[1];
    } catch (error) {
      console.error('Error generating PDF as base64:', error);
      throw error;
    }
  }

  /**
   * Create a loading message element
   */
  private createLoadingMessage(): HTMLDivElement {
    const loadingMessage = document.createElement('div');
    loadingMessage.style.position = 'fixed';
    loadingMessage.style.top = '50%';
    loadingMessage.style.left = '50%';
    loadingMessage.style.transform = 'translate(-50%, -50%)';
    loadingMessage.style.padding = '20px';
    loadingMessage.style.background = 'rgba(0, 0, 0, 0.7)';
    loadingMessage.style.color = 'white';
    loadingMessage.style.borderRadius = '5px';
    loadingMessage.style.zIndex = '9999';
    loadingMessage.textContent = 'Generating PDF...';
    return loadingMessage;
  }
}
