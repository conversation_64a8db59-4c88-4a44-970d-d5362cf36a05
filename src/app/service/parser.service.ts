import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ParserService {
  pageData: any;

  constructor(private http: HttpClient) {}

  // Method untuk mengambil halaman berdasarkan slug
  getPageData(slug: string = 'home-page'): Observable<any> {
    // Convert slug to proper Single Type endpoint
    let endpoint = '';
    switch (slug) {
      case 'about-page':
        endpoint = 'about-page';
        break;
      case 'why-page':
        endpoint = 'why-page';
        break;
      case 'home-page':
      default:
        endpoint = 'home-page';
        break;
    }

    const urlSlug = `${environment.url}${endpoint}?populate[content][populate]=*`;

    return this.http.get(urlSlug).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('ParserService Error:', error);

        if (error.status === 401 || error.status === 403) {
          console.warn(
            `Access denied to ${endpoint}. Check Strapi permissions for ${endpoint} endpoint.`
          );
        }

        // Return mock data or empty object instead of throwing error
        return of({ data: { attributes: { content: [] } } });
      })
    );
  }

  // Backward compatibility
  getData(): Observable<any> {
    return this.getPageData('home-page');
  }
}
