import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment.development';

export enum AnswerType {
  D = 'D',
  I = 'I',
  S = 'S',
  C = 'C',
  V = 'V',
}

export interface Answer {
  mostAnswer: AnswerType;
  leastAnswer: AnswerType;
}

export interface Report {
  mostCounts: { D: number; I: number; S: number; C: number; V: number };
  leastCounts: { D: number; I: number; S: number; C: number; V: number };
  difference: { D: number; I: number; S: number; C: number; V: number };
}

@Injectable({
  providedIn: 'root',
})
export class ReportService {
  private apiUrl = `${environment.url}` + 'disc-answers/';

  constructor(private http: HttpClient) {}

  getAnswers(answerId: number): Observable<Answer[]> {
    return this.http.get<any>(this.apiUrl + `${answerId}`).pipe(
      map((response) => {
        if (response && response.data && response.data.attributes) {
          const answers =
            response.data.attributes.answers || response.data.attributes.answer;
          return answers || [];
        }

        if (Array.isArray(response)) {
          return response;
        }

        return [];
      })
    );
  }

  normalizeValue(
    value: number,
    observedMin: number,
    observedMax: number,
    newMax: number,
    newMin: number = 0
  ): number {
    return (
      ((value - observedMin) / (observedMax - observedMin)) *
        (newMax - newMin) +
      newMin
    );
  }

  calculateReport(answers: any[]) {
    if (!answers || !Array.isArray(answers) || answers.length === 0) {
      return {
        mostCounts: { D: 0, I: 0, S: 0, C: 0 },
        leastCounts: { D: 0, I: 0, S: 0, C: 0 },
        difference: { D: 0, I: 0, S: 0, C: 0 },
      };
    }

    const mostCounts = { D: 0, I: 0, S: 0, C: 0 };
    const leastCounts = { D: 0, I: 0, S: 0, C: 0 };
    const difference = { D: 0, I: 0, S: 0, C: 0 };

    answers.forEach((answer) => {
      if (
        answer &&
        answer.mostAnswer &&
        mostCounts.hasOwnProperty(answer.mostAnswer)
      ) {
        mostCounts[answer.mostAnswer as keyof typeof mostCounts]++;
      }
      if (
        answer &&
        answer.leastAnswer &&
        leastCounts.hasOwnProperty(answer.leastAnswer)
      ) {
        leastCounts[answer.leastAnswer as keyof typeof leastCounts]++;
      }
    });

    Object.keys(difference).forEach((key) => {
      const keyAsType = key as keyof typeof difference;
      difference[keyAsType] = mostCounts[keyAsType] - leastCounts[keyAsType];
    });

    return { mostCounts, leastCounts, difference };
  }
}
