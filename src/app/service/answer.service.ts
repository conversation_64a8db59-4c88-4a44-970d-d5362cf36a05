import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AnswerService {
  private apiUrl = `${environment.url}` + 'disc-answers';

  constructor(private http: HttpClient) {}

  saveAnswers(answers: any[], userId: number, testManagerId?: number) {
    const payload: any = {
      data: {
        answers: answers, // Ubah dari 'answer' menjadi 'answers' agar sesuai dengan struktur database
        user: userId,
      },
    };

    // Tambahkan testManagerId ke payload jika ada
    if (testManagerId) {
      payload.data.testManagerId = testManagerId;
    }

    console.log('DISC Payload:', JSON.stringify(payload));

    return this.http
      .post(this.apiUrl, payload, {
        headers: { 'Content-Type': 'application/json' },
      })
      .pipe(
        catchError((error) => {
          console.error('Error in saveAnswers API call:', error);
          if (error.error) {
            console.error('Error details:', error.error);
          }
          throw error;
        })
      );
  }
}
