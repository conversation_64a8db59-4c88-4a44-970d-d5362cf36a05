import { Injectable } from '@angular/core';
import { Observable, map, forkJoin, of } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { ReportService } from './report.service';
import { DiscResultService } from './disc-result.service';
import { ChartData, ChartOptions, ChartType } from 'chart.js';

export interface DiscDataSource {
  category: string;
  most: number;
  least: number;
  difference: number;
}

export interface DiscReportData {
  dataSource: DiscDataSource[];
  mostString: string;
  leastString: string;
  differenceString: string;
  mostChartData: ChartData<'line'>;
  leastChartData: ChartData<'line'>;
  differenceChartData: ChartData<'line'>;
  mostChartOptions: ChartOptions;
  leastChartOptions: ChartOptions;
  differenceChartOptions: ChartOptions;
  chartType: ChartType;
  // Tambahan untuk analyzer data
  mostAnalyzer?: {
    pattern: string;
    patternName: string;
    patternSlogan: string;
    description: string;
  } | null;
  leastAnalyzer?: {
    pattern: string;
    patternName: string;
    patternSlogan: string;
    description: string;
  } | null;
  differenceAnalyzer?: {
    pattern: string;
    patternName: string;
    patternSlogan: string;
    description: string;
  } | null;
}

@Injectable({
  providedIn: 'root',
})
export class DiscReportService {
  private labels: string[] = ['D', 'I', 'S', 'C'];

  constructor(
    private reportService: ReportService,
    private discResultService: DiscResultService
  ) {}

  /**
   * Mendapatkan data laporan DISC berdasarkan ID jawaban
   * @param answerId ID jawaban DISC
   * @returns Observable berisi data laporan DISC
   */
  getDiscReportByAnswerId(answerId: number): Observable<DiscReportData> {
    return this.reportService.getAnswers(answerId).pipe(
      map((answers: any[]) => {
        if (!answers || answers.length === 0) {
          return this.getEmptyReportData();
        }

        const report = this.reportService.calculateReport(answers);
        const mostCounts = report.mostCounts;
        const leastCounts = report.leastCounts;
        const difference = report.difference;

        // Buat data sumber untuk tabel
        const dataSource = this.createDataSource(
          mostCounts,
          leastCounts,
          difference
        );

        // Normalisasi nilai untuk chart
        const normalizedCriteriaMost = {
          D: this.discResultService.normalizeValue(7, 0, 20, 28),
          I: this.discResultService.normalizeValue(5, 0, 17, 28),
          S: this.discResultService.normalizeValue(5, 0, 19, 28),
          C: this.discResultService.normalizeValue(4, 0, 15, 28),
        };

        const normalizedCriteriaLeast = {
          D: this.discResultService.normalizeValue(4, 0, 21, 28),
          I: this.discResultService.normalizeValue(3, 0, 10, 28),
          S: this.discResultService.normalizeValue(5, 0, 19, 27),
          C: this.discResultService.normalizeValue(6, 0, 18, 27),
        };

        const normalizedCriteriaDifference = {
          D: 1,
          I: 0,
          S: -1,
          C: -2,
        };

        const normalizedMostCounts = {
          D: this.discResultService.normalizeValue(mostCounts.D, 0, 20, 28),
          I: this.discResultService.normalizeValue(mostCounts.I, 0, 17, 28),
          S: this.discResultService.normalizeValue(mostCounts.S, 0, 19, 28),
          C: this.discResultService.normalizeValue(mostCounts.C, 0, 15, 28),
        };

        const normalizedLeastCounts = {
          D: this.discResultService.normalizeValue(leastCounts.D, 0, 21, 28),
          I: this.discResultService.normalizeValue(leastCounts.I, 0, 10, 28),
          S: this.discResultService.normalizeValue(leastCounts.S, 0, 19, 27),
          C: this.discResultService.normalizeValue(leastCounts.C, 0, 18, 27),
        };

        // Generate DISC strings
        const mostString = this.discResultService.generateDISCString(
          normalizedMostCounts,
          normalizedCriteriaMost
        );

        const leastString = this.discResultService.generateDISCString(
          normalizedLeastCounts,
          normalizedCriteriaLeast,
          true
        );

        const differenceString = this.discResultService.generateDISCString(
          difference,
          normalizedCriteriaDifference
        );

        // Buat data chart
        const mostChartData = this.createChartData(
          this.labels,
          Object.values(normalizedMostCounts),
          'MOST',
          '#42A5F5'
        );

        const leastChartData = this.createChartData(
          this.labels,
          Object.values(normalizedLeastCounts),
          'LEAST',
          '#FFA726'
        );

        const differenceChartData = this.createChartData(
          this.labels,
          Object.values(difference),
          'DIFFERENCE',
          '#66BB6A'
        );

        return {
          dataSource,
          mostString,
          leastString,
          differenceString,
          mostChartData,
          leastChartData,
          differenceChartData,
          mostChartOptions: this.getMostChartOptions(),
          leastChartOptions: this.getLeastChartOptions(),
          differenceChartOptions: this.getDifferenceChartOptions(),
          chartType: 'line' as ChartType,
        };
      }),
      switchMap((reportData: DiscReportData) => {
        // Ambil analyzer data untuk setiap pattern
        const mostAnalyzer$ = this.getAnalyzerData(reportData.mostString);
        const leastAnalyzer$ = this.getAnalyzerData(reportData.leastString);
        const differenceAnalyzer$ = this.getAnalyzerData(
          reportData.differenceString
        );

        return forkJoin({
          mostAnalyzer: mostAnalyzer$,
          leastAnalyzer: leastAnalyzer$,
          differenceAnalyzer: differenceAnalyzer$,
        }).pipe(
          map((analyzers) => ({
            ...reportData,
            mostAnalyzer: analyzers.mostAnalyzer,
            leastAnalyzer: analyzers.leastAnalyzer,
            differenceAnalyzer: analyzers.differenceAnalyzer,
          }))
        );
      })
    );
  }

  /**
   * Membuat data laporan DISC dari data yang sudah ada
   * @param mostCounts Nilai MOST untuk D, I, S, C
   * @param leastCounts Nilai LEAST untuk D, I, S, C
   * @param difference Nilai DIFFERENCE untuk D, I, S, C
   * @returns Data laporan DISC
   */
  createDiscReport(
    mostCounts: { D: number; I: number; S: number; C: number },
    leastCounts: { D: number; I: number; S: number; C: number },
    difference: { D: number; I: number; S: number; C: number }
  ): DiscReportData {
    // Buat data sumber untuk tabel
    const dataSource = this.createDataSource(
      mostCounts,
      leastCounts,
      difference
    );

    // Normalisasi nilai untuk chart
    const normalizedCriteriaMost = {
      D: this.discResultService.normalizeValue(7, 0, 20, 28),
      I: this.discResultService.normalizeValue(5, 0, 17, 28),
      S: this.discResultService.normalizeValue(5, 0, 19, 28),
      C: this.discResultService.normalizeValue(4, 0, 15, 28),
    };

    const normalizedCriteriaLeast = {
      D: this.discResultService.normalizeValue(4, 0, 21, 28),
      I: this.discResultService.normalizeValue(3, 0, 10, 28),
      S: this.discResultService.normalizeValue(5, 0, 19, 27),
      C: this.discResultService.normalizeValue(6, 0, 18, 27),
    };

    const normalizedCriteriaDifference = {
      D: 1,
      I: 0,
      S: -1,
      C: -2,
    };

    const normalizedMostCounts = {
      D: this.discResultService.normalizeValue(mostCounts.D, 0, 20, 28),
      I: this.discResultService.normalizeValue(mostCounts.I, 0, 17, 28),
      S: this.discResultService.normalizeValue(mostCounts.S, 0, 19, 28),
      C: this.discResultService.normalizeValue(mostCounts.C, 0, 15, 28),
    };

    const normalizedLeastCounts = {
      D: this.discResultService.normalizeValue(leastCounts.D, 0, 21, 28),
      I: this.discResultService.normalizeValue(leastCounts.I, 0, 10, 28),
      S: this.discResultService.normalizeValue(leastCounts.S, 0, 19, 27),
      C: this.discResultService.normalizeValue(leastCounts.C, 0, 18, 27),
    };

    // Generate DISC strings
    const mostString = this.discResultService.generateDISCString(
      normalizedMostCounts,
      normalizedCriteriaMost
    );

    const leastString = this.discResultService.generateDISCString(
      normalizedLeastCounts,
      normalizedCriteriaLeast,
      true
    );

    const differenceString = this.discResultService.generateDISCString(
      difference,
      normalizedCriteriaDifference
    );

    // Buat data chart
    const mostChartData = this.createChartData(
      this.labels,
      Object.values(normalizedMostCounts),
      'MOST',
      '#42A5F5'
    );

    const leastChartData = this.createChartData(
      this.labels,
      Object.values(normalizedLeastCounts),
      'LEAST',
      '#FFA726'
    );

    const differenceChartData = this.createChartData(
      this.labels,
      Object.values(difference),
      'DIFFERENCE',
      '#66BB6A'
    );

    return {
      dataSource,
      mostString,
      leastString,
      differenceString,
      mostChartData,
      leastChartData,
      differenceChartData,
      mostChartOptions: this.getMostChartOptions(),
      leastChartOptions: this.getLeastChartOptions(),
      differenceChartOptions: this.getDifferenceChartOptions(),
      chartType: 'line',
    };
  }

  /**
   * Membuat data sumber untuk tabel
   */
  private createDataSource(
    mostCounts: { D: number; I: number; S: number; C: number },
    leastCounts: { D: number; I: number; S: number; C: number },
    difference: { D: number; I: number; S: number; C: number }
  ): DiscDataSource[] {
    return [
      {
        category: 'D',
        most: mostCounts.D,
        least: leastCounts.D,
        difference: difference.D,
      },
      {
        category: 'I',
        most: mostCounts.I,
        least: leastCounts.I,
        difference: difference.I,
      },
      {
        category: 'S',
        most: mostCounts.S,
        least: leastCounts.S,
        difference: difference.S,
      },
      {
        category: 'C',
        most: mostCounts.C,
        least: leastCounts.C,
        difference: difference.C,
      },
    ];
  }

  /**
   * Membuat data chart
   */
  private createChartData(
    labels: string[],
    data: number[],
    label: string,
    borderColor: string
  ): ChartData<'line'> {
    return {
      labels,
      datasets: [
        {
          data,
          label,
          fill: false,
          borderColor,
          tension: 0.1,
        },
      ],
    };
  }

  /**
   * Mendapatkan opsi chart untuk MOST
   */
  getMostChartOptions(): ChartOptions {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'top',
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          min: 0,
          max: 20,
          ticks: {
            stepSize: 2,
          },
        },
      },
    };
  }

  /**
   * Mendapatkan opsi chart untuk LEAST
   */
  getLeastChartOptions(): ChartOptions {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'top',
        },
      },
      scales: {
        y: {
          reverse: true,
          min: 0,
          max: 20,
          ticks: {
            stepSize: 2,
          },
        },
      },
    };
  }

  /**
   * Mendapatkan opsi chart untuk DIFFERENCE
   */
  getDifferenceChartOptions(): ChartOptions {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'top',
        },
      },
      scales: {
        y: {
          min: -20,
          max: 20,
          ticks: {
            stepSize: 2,
          },
        },
      },
    };
  }

  /**
   * Mendapatkan data analyzer untuk pattern tertentu
   * @param pattern Pattern DISC (contoh: "D", "DI", "DCS", dll)
   * @returns Observable berisi data analyzer atau null jika tidak ditemukan
   */
  private getAnalyzerData(pattern: string): Observable<{
    pattern: string;
    patternName: string;
    patternSlogan: string;
    description: string;
  } | null> {
    if (!pattern || pattern.length === 0) {
      return of(null);
    }

    return this.discResultService.getStringAnalyzer(pattern).pipe(
      map((res: any) => {
        if (res.data && res.data.length > 0) {
          const item = res.data[0].attributes;
          return {
            pattern: item.pattern,
            patternName: item.patternName,
            patternSlogan: item.patternSlogan,
            description: item.description,
          };
        }
        return null;
      }),
      catchError(() => of(null))
    );
  }

  /**
   * Mendapatkan data laporan kosong
   */
  private getEmptyReportData(): DiscReportData {
    return {
      dataSource: [],
      mostString: '',
      leastString: '',
      differenceString: '',
      mostChartData: this.createChartData(
        this.labels,
        [0, 0, 0, 0],
        'MOST',
        '#42A5F5'
      ),
      leastChartData: this.createChartData(
        this.labels,
        [0, 0, 0, 0],
        'LEAST',
        '#FFA726'
      ),
      differenceChartData: this.createChartData(
        this.labels,
        [0, 0, 0, 0],
        'DIFFERENCE',
        '#66BB6A'
      ),
      mostChartOptions: this.getMostChartOptions(),
      leastChartOptions: this.getLeastChartOptions(),
      differenceChartOptions: this.getDifferenceChartOptions(),
      chartType: 'line' as ChartType,
      mostAnalyzer: null,
      leastAnalyzer: null,
      differenceAnalyzer: null,
    };
  }
}
