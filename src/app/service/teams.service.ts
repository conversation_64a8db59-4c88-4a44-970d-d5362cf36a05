import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of, map, catchError, switchMap } from 'rxjs';
import { environment } from 'src/environments/environment.development';
import { ChartData, ChartOptions, ChartType } from 'chart.js';

// Tipe data untuk kategori TEAMS
type Category = 'T' | 'E' | 'A' | 'M' | 'S';

// Interface untuk jawaban
interface Answer {
  category: Category;
  value: number;
}

// Interface untuk hasil kategori
interface CategoryResult {
  category: Category;
  value: number;
}

// Interface untuk hasil perhitungan TEAMS
export interface TeamsReport {
  categoryTotals: Record<Category, number>;
  highestCategories: CategoryResult[];
  chartData: ChartData<'line'>;
}

// Interface untuk hasil analisis TEAMS
export interface TeamsAnalysis {
  mainCategory: string;
  mainCategoryName: string;
  mainCategoryValue: number;
  secondCategory?: string;
  secondCategoryName?: string;
  secondCategoryValue?: number;
  combinationName?: string;
  analysisTitle: string;
  analysisDetail: string;
  combinationDetail?: string;
}

@Injectable({
  providedIn: 'root',
})
export class TeamsService {
  url = `${environment.url}` + 'team-questions?populate=*&pagination[limit]=-1';
  urlPost = `${environment.url}` + 'teams-answers';
  urlAnalyzer = `${environment.url}` + 'teams-analyzers';

  filterMap: Record<string, string> = {
    T: 'Theorist',
    E: 'Executor',
    A: 'Analyzer',
    M: 'Manager',
    S: 'Strategist',
  };

  constructor(private http: HttpClient) {}

  getQuestion() {
    return this.http.get(this.url);
  }

  saveAnswer(userId: number, answers: any[], testManagerId?: number) {
    const payload: any = {
      data: {
        answer: answers,
        user: userId,
      },
    };

    // Tambahkan testManagerId ke payload jika ada
    if (testManagerId) {
      payload.data.testManagerId = testManagerId;
    }

    return this.http.post(this.urlPost, payload, {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  getResult(userId: number) {
    return this.http.get(`${this.urlPost}?filters[user][id][$eq]=${userId}`);
  }

  getResultById(answerId: number) {
    return this.http.get(`${this.urlPost}/${answerId}?populate=*`);
  }

  /**
   * Menghitung total nilai untuk setiap kategori TEAMS dari jawaban
   * @param answers Array jawaban atau objek jawaban dari API
   * @returns Record berisi total nilai untuk setiap kategori
   */
  calculateReport(answers: any): Record<Category, number> {
    const categoryTotals: Record<Category, number> = {
      T: 0,
      E: 0,
      A: 0,
      M: 0,
      S: 0,
    };

    const answersArray: Answer[] = Array.isArray(answers)
      ? answers
      : answers.data || [];

    for (const item of answersArray) {
      if (item.category && categoryTotals.hasOwnProperty(item.category)) {
        categoryTotals[item.category] += Number(item.value);
      }
    }

    return categoryTotals;
  }

  /**
   * Mendapatkan data chart untuk TEAMS berdasarkan hasil perhitungan
   * @param categoryTotals Hasil perhitungan kategori
   * @returns Data chart yang siap digunakan
   */
  getTeamsChartData(
    categoryTotals: Record<Category, number>
  ): ChartData<'line'> {
    const labels = ['T', 'E', 'A', 'M', 'S'];

    return {
      labels,
      datasets: [
        {
          data: Object.values(categoryTotals),
          fill: false,
          borderColor: '#FFA726',
          tension: 0.1,
        },
      ],
    };
  }

  /**
   * Mendapatkan opsi chart default untuk TEAMS
   * @returns Opsi chart yang siap digunakan
   */
  getTeamsChartOptions(): ChartOptions {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        y: {
          beginAtZero: false,
          min: 10,
          max: 50,
          ticks: {
            stepSize: 5,
          },
          grid: {
            color: (context) => {
              // Ubah warna garis grid pada nilai tertentu
              if (context.tick.value === 30) {
                return 'rgba(0, 0, 0, 1)'; // Hitam untuk garis tebal
              }
              return 'rgba(0, 0, 0, 0.1)'; // Abu-abu untuk garis biasa
            },
            lineWidth: (context) => {
              // Ubah ketebalan garis grid pada nilai tertentu
              if (context.tick.value === 30) {
                return 2; // Ketebalan garis tebal
              }
              return 1; // Ketebalan garis biasa
            },
          },
        },
      },
    };
  }

  /**
   * Mendapatkan kategori dengan nilai tertinggi di atas threshold
   * @param categoryTotals Hasil perhitungan kategori
   * @param threshold Nilai threshold (default: 30)
   * @param maxResults Jumlah maksimum hasil (default: 2)
   * @returns Array berisi kategori dengan nilai tertinggi
   */
  getHighestCategory(
    categoryTotals: Record<Category, number>,
    threshold: number = 30,
    maxResults: number = 2
  ): CategoryResult[] {
    const sortedCategories = Object.entries(categoryTotals)
      .sort(([, a], [, b]) => {
        return b - a;
      })
      .filter(([, value]) => value >= threshold)
      .map(([category, value]) => ({
        category: category as Category,
        value: value,
      }));

    if (sortedCategories.length >= 1) {
      return sortedCategories.slice(0, maxResults);
    }

    // Jika tidak ada kategori di atas threshold, ambil yang tertinggi
    const highest = Object.entries(categoryTotals).reduce(
      (highest, current) => {
        return current[1] > highest[1] ? current : highest;
      }
    );

    return [
      {
        category: highest[0] as Category,
        value: highest[1],
      },
    ];
  }

  /**
   * Mendapatkan analisis untuk kategori tertentu
   * @param category Kode kategori (T, E, A, M, S)
   * @returns Observable berisi data analisis
   */
  getAnalyzer(category: string): Observable<any> {
    const name = this.filterMap[category];
    if (name) {
      return this.http
        .get(`${this.urlAnalyzer}?filters[name][$eq]=${name}`)
        .pipe(
          catchError((error) => {
            console.error('Error fetching analyzer data:', error);
            return of({ data: [] });
          })
        );
    }

    // Jika nama tidak valid, kembalikan Observable kosong
    return of({ data: [] });
  }

  /**
   * Mendapatkan analisis kombinasi untuk dua kategori
   * @param keyword Kata kunci kombinasi
   * @returns Observable berisi data analisis kombinasi
   */
  getCombinedAnalyzer(keyword: string): Observable<any> {
    if (!keyword) {
      console.warn('Keyword is empty or invalid');
      return of({ data: [] });
    }

    // Gunakan filter like ($contains) untuk kombinasi
    return this.http
      .get(`${this.urlAnalyzer}?filters[name][$contains]=${keyword}`)
      .pipe(
        catchError((error) => {
          console.error('Error fetching combined analyzer data:', error);
          return of({ data: [] });
        })
      );
  }

  getCombinationName(category1: string, category2: string): string {
    const name1 = this.filterMap[category1];
    const name2 = this.filterMap[category2];

    if (!name1 || !name2) {
      console.warn('Invalid categories for combination:', category1, category2);
      return '';
    }

    // Pastikan urutan alfabetis
    return name1 < name2 ? `${name1} - ${name2}` : `${name2} - ${name1}`;
  }

  // Ambil semua hasil Teams (untuk admin)
  getAllResults(): Observable<any> {
    return this.http.get(`${this.urlPost}?populate=user`);
  }

  // Tambahkan method untuk mendapatkan hasil berdasarkan test manager ID
  getResultByTestManagerId(testManagerId: number): Observable<any> {
    return this.http.get(
      `${this.urlPost}?filters[testManagerId][$eq]=${testManagerId}`
    );
  }

  /**
   * Mendapatkan laporan TEAMS lengkap berdasarkan ID jawaban
   * @param answerId ID jawaban
   * @returns Observable berisi laporan TEAMS lengkap
   */
  getTeamsReportById(answerId: number): Observable<TeamsReport> {
    return this.getResultById(answerId).pipe(
      map((answers: any) => {
        const teamsAnswers = answers?.data?.attributes?.answer || [];
        const categoryTotals = this.calculateReport(teamsAnswers);
        const highestCategories = this.getHighestCategory(categoryTotals);
        const chartData = this.getTeamsChartData(categoryTotals);

        return {
          categoryTotals,
          highestCategories,
          chartData,
        };
      }),
      catchError((error) => {
        console.error('Error generating TEAMS report:', error);
        // Return empty report
        const emptyCategoryTotals = { T: 0, E: 0, A: 0, M: 0, S: 0 };
        return of({
          categoryTotals: emptyCategoryTotals,
          highestCategories: [],
          chartData: this.getTeamsChartData(emptyCategoryTotals),
        });
      })
    );
  }

  /**
   * Mendapatkan analisis TEAMS lengkap berdasarkan kategori tertinggi
   * @param highestCategories Array kategori tertinggi
   * @returns Observable berisi analisis TEAMS lengkap
   */
  getTeamsAnalysis(
    highestCategories: CategoryResult[]
  ): Observable<TeamsAnalysis> {
    if (highestCategories.length === 0) {
      return of({
        mainCategory: '',
        mainCategoryName: '',
        mainCategoryValue: 0,
        analysisTitle: 'Tidak Ada Data',
        analysisDetail: 'Tidak ada data kategori yang ditemukan.',
      });
    }

    const firstCategory = highestCategories[0].category;
    const firstCategoryName = this.filterMap[firstCategory];
    const firstCategoryValue = highestCategories[0].value;

    // Ambil analisis untuk kategori utama
    return this.getAnalyzer(firstCategory).pipe(
      switchMap((analyzer: any) => {
        const analysisTitle =
          analyzer?.data[0]?.attributes?.name || 'Tidak Ditemukan';
        const analysisDetail =
          analyzer?.data[0]?.attributes?.detail || 'Tidak Ditemukan';

        // Jika hanya ada satu kategori, kembalikan analisis sederhana
        if (highestCategories.length === 1) {
          return of({
            mainCategory: firstCategory,
            mainCategoryName: firstCategoryName,
            mainCategoryValue: firstCategoryValue,
            analysisTitle,
            analysisDetail,
          });
        }

        // Jika ada dua kategori, tambahkan analisis kombinasi
        const secondCategory = highestCategories[1].category;
        const secondCategoryName = this.filterMap[secondCategory];
        const secondCategoryValue = highestCategories[1].value;
        const combinationName = this.getCombinationName(
          firstCategory,
          secondCategory
        );

        // Ambil analisis kombinasi
        return this.getCombinedAnalyzer(combinationName).pipe(
          map((combineAnalyzer: any) => {
            let combinationDetail = '';

            if (combineAnalyzer?.data?.length) {
              combinationDetail = combineAnalyzer.data
                .map((item: any) => item.attributes.detail)
                .join('\n\n');
            } else {
              combinationDetail = `Tidak ada data kombinasi yang ditemukan untuk ${combinationName}.`;
            }

            return {
              mainCategory: firstCategory,
              mainCategoryName: firstCategoryName,
              mainCategoryValue: firstCategoryValue,
              secondCategory,
              secondCategoryName,
              secondCategoryValue,
              combinationName,
              analysisTitle,
              analysisDetail,
              combinationDetail,
            };
          })
        );
      }),
      catchError((error) => {
        console.error('Error generating TEAMS analysis:', error);
        return of({
          mainCategory: firstCategory,
          mainCategoryName: firstCategoryName || '',
          mainCategoryValue: firstCategoryValue,
          analysisTitle: 'Error',
          analysisDetail: 'Terjadi kesalahan saat mengambil data analisis.',
        });
      })
    );
  }

  /**
   * Mendapatkan laporan dan analisis TEAMS lengkap berdasarkan ID jawaban
   * @param answerId ID jawaban
   * @returns Observable berisi laporan dan analisis TEAMS lengkap
   */
  getCompleteTeamsReport(
    answerId: number
  ): Observable<{ report: TeamsReport; analysis: TeamsAnalysis }> {
    return this.getTeamsReportById(answerId).pipe(
      switchMap((report: TeamsReport) => {
        return this.getTeamsAnalysis(report.highestCategories).pipe(
          map((analysis: TeamsAnalysis) => ({
            report,
            analysis,
          }))
        );
      })
    );
  }
}
