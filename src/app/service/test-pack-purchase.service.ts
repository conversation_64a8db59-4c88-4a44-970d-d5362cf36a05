import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { catchError, map } from 'rxjs/operators';

interface TestPackOption {
  id: number;
  name: string;
  description: string;
  price: number;
  tests: string[];
  imageUrl?: string;
}

@Injectable({
  providedIn: 'root',
})
export class TestPackPurchaseService {
  private apiUrl = `${environment.url}test-pack-options`;
  private selectedTestPack: TestPackOption | null = null;
  private purchaseData: any = null;

  constructor(private http: HttpClient) {}

  // Get available test packs from the API
  getAvailableTestPacks(): Observable<TestPackOption[]> {
    // For development, return mock data if API is not ready
    if (environment.useMockData) {
      return of(this.getMockTestPacks());
    }

    return this.http.get<any>(`${this.apiUrl}?populate=*`).pipe(
      map((response: any) => {
        if (!response.data) {
          return [];
        }

        return response.data.map((item: any) => ({
          id: item.id,
          name: item.attributes.name,
          description: item.attributes.description,
          price: item.attributes.price,
          tests: item.attributes.tests,
          imageUrl: item.attributes.image?.data?.attributes?.url
            ? `${environment.url.replace('/api/', '')}${
                item.attributes.image.data.attributes.url
              }`
            : undefined,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching test packs:', error);
        return of(this.getMockTestPacks()); // Fallback to mock data on error
      })
    );
  }

  // Store the selected test pack
  setSelectedTestPack(testPack: TestPackOption): void {
    this.selectedTestPack = testPack;
    // Store in session storage for persistence across page refreshes
    sessionStorage.setItem('selectedTestPack', JSON.stringify(testPack));
  }

  // Get the selected test pack
  getSelectedTestPack(): TestPackOption | null {
    if (!this.selectedTestPack) {
      // Try to retrieve from session storage
      const stored = sessionStorage.getItem('selectedTestPack');
      if (stored) {
        this.selectedTestPack = JSON.parse(stored);
      }
    }
    return this.selectedTestPack;
  }

  // Clear the selected test pack
  clearSelectedTestPack(): void {
    this.selectedTestPack = null;
    sessionStorage.removeItem('selectedTestPack');
  }

  // Store purchase data
  setPurchaseData(data: any): void {
    console.log('Storing purchase data:', data);
    this.purchaseData = data;
    sessionStorage.setItem('purchaseData', JSON.stringify(data));
    // Also store in localStorage as backup
    localStorage.setItem('purchaseDataBackup', JSON.stringify(data));
  }

  // Get purchase data
  getPurchaseData(): any {
    if (!this.purchaseData) {
      // Try sessionStorage first
      const stored = sessionStorage.getItem('purchaseData');
      if (stored) {
        this.purchaseData = JSON.parse(stored);
        console.log(
          'Retrieved purchase data from sessionStorage:',
          this.purchaseData
        );
      } else {
        // Fallback to localStorage
        const backup = localStorage.getItem('purchaseDataBackup');
        if (backup) {
          this.purchaseData = JSON.parse(backup);
          console.log(
            'Retrieved purchase data from localStorage backup:',
            this.purchaseData
          );
        }
      }
    }
    return this.purchaseData;
  }

  // Clear purchase data
  clearPurchaseData(): void {
    this.purchaseData = null;
    sessionStorage.removeItem('purchaseData');
    localStorage.removeItem('purchaseDataBackup');
    console.log('Purchase data cleared');
  }

  // Clear all stored data including selected test pack (for preventing duplication)
  clearAllData(): void {
    this.clearSelectedTestPack();
    this.clearPurchaseData();
    console.log('All test pack purchase data cleared');
  }

  // Create a test pack for a user after successful payment
  createTestPackForUser(userId: number): Observable<any> {
    if (!this.selectedTestPack) {
      return of({ error: 'No test pack selected' });
    }

    // This would be implemented to call the backend API
    // For now, we'll return a mock response
    return of({
      success: true,
      testPackId: Math.floor(Math.random() * 1000) + 1,
    });
  }

  // Mock data for development
  private getMockTestPacks(): TestPackOption[] {
    return [
      {
        id: 1,
        name: 'Paket Dasar',
        description: 'Paket tes dasar untuk mengenal kepribadian Anda',
        price: 150000,
        tests: ['DISC', 'MBTI'],
        imageUrl:
          'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
      },
      {
        id: 2,
        name: 'Paket Profesional',
        description: 'Paket tes lengkap untuk pengembangan karir profesional',
        price: 250000,
        tests: ['DISC', 'MBTI', 'TEAMS', 'VALUES'],
        imageUrl:
          'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
      },
      {
        id: 3,
        name: 'Paket Bisnis',
        description: 'Paket tes khusus untuk pemilik bisnis dan entrepreneur',
        price: 350000,
        tests: ['DISC', 'MBTI', 'TEAMS', 'VALUES'],
        imageUrl:
          'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
      },
    ];
  }
}
