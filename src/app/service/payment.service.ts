import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, switchMap, tap, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment.development';
import { PaymentLoggingService } from './payment-logging.service';
import { AuthService } from './auth.service';
import * as CryptoJS from 'crypto-js';

export interface PaymentRequest {
  amount: number;
  description: string;
  externalId: string;
  customerName: string;
  customerEmail: string;
  successRedirectUrl: string;
  failureRedirectUrl: string;
}

export interface PaymentResponse {
  id: string;
  externalId: string;
  status: 'PENDING' | 'PAID' | 'EXPIRED' | 'FAILED';
  paymentUrl: string;
  amount: number;
  createdAt: string;
  updatedAt: string;
}

export interface StrapiPayment {
  externalId: string;
  tripayId?: string;
  amount: number;
  status: 'PENDING' | 'PAID' | 'FAILED' | 'EXPIRED';
  paymentMethod?: string;
  paymentUrl: string;
  description: string;
  paidAt?: string;
  user?: number;
  testPack?: number;
}

// Interface untuk request Tripay
export interface TripayRequest {
  method: string;
  merchant_ref: string;
  amount: number;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  order_items: Array<{
    sku?: string;
    name: string;
    price: number;
    quantity: number;
    product_url?: string;
    image_url?: string;
  }>;
  return_url?: string;
  expired_time?: number;
  signature: string;
}

// Interface untuk response Tripay
export interface TripayResponse {
  success: boolean;
  message: string;
  data: {
    reference: string;
    merchant_ref: string;
    payment_selection_type: string;
    payment_method: string;
    payment_name: string;
    customer_name: string;
    customer_email: string;
    customer_phone: string;
    callback_url: string;
    return_url: string;
    amount: number;
    fee_merchant: number;
    fee_customer: number;
    total_fee: number;
    amount_received: number;
    pay_code: string;
    pay_url?: string;
    checkout_url: string;
    status: string;
    expired_time: number;
    order_items: Array<any>;
    created_at: number;
    updated_at: number;
  };
}

@Injectable({
  providedIn: 'root',
})
export class PaymentService {
  private apiUrl = `${environment.url}payments`;
  private tripayApiUrl =
    environment.tripay?.apiUrl || 'https://tripay.co.id/api-sandbox';

  // In a real implementation, these would be handled securely on the server
  // This is just for demonstration purposes
  private tripayMerchantCode = environment.tripay?.merchantCode || 'T12345';
  private tripayApiKey = environment.tripay?.apiKey || 'DEV-123456789';
  private tripayPrivateKey =
    environment.tripay?.privateKey || 'your-private-key';

  constructor(
    private http: HttpClient,
    private paymentLoggingService: PaymentLoggingService,
    private authService: AuthService
  ) {}

  private getHeaders(): HttpHeaders {
    const token = this.authService.getToken();
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return new HttpHeaders(headers);
  }

  // Create a payment invoice with Tripay
  createPayment(paymentRequest: PaymentRequest): Observable<PaymentResponse> {
    // Get current user ID
    const currentUser = this.authService.getCurrentUser();
    const userId = currentUser?.id;

    console.log('Creating payment for user:', {
      userId: userId,
      email: currentUser?.email,
      username: currentUser?.username,
      externalId: paymentRequest.externalId,
    });

    // For development, return mock data if API is not ready
    if (environment.useMockData) {
      const response = this.getMockPaymentResponse(paymentRequest);
      // Save payment to Strapi with current user ID
      this.savePaymentToStrapi({
        externalId: response.externalId,
        tripayId: response.id,
        amount: response.amount,
        status: response.status,
        paymentUrl: response.paymentUrl,
        description: paymentRequest.description,
        user: userId, // Use current user ID
      }).subscribe((paymentResponse) => {
        // Log payment creation
        this.paymentLoggingService
          .logPaymentAction({
            action: 'PAYMENT_CREATED',
            newStatus: response.status,
            metadata: {
              amount: response.amount,
              externalId: response.externalId,
            },
            payment: paymentResponse.data?.id,
            user: userId,
          })
          .subscribe();
      });
      return of(response);
    }

    // Create Tripay transaction request
    const tripayRequest = this.createTripayRequest(paymentRequest);

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.tripayApiKey}`,
    });

    return this.http
      .post<TripayResponse>(
        `${this.tripayApiUrl}/transaction/create`,
        tripayRequest,
        { headers }
      )
      .pipe(
        map((tripayResponse) => {
          if (tripayResponse.success) {
            // Save payment to Strapi with current user ID
            this.savePaymentToStrapi({
              externalId: tripayResponse.data.merchant_ref,
              tripayId: tripayResponse.data.reference,
              amount: tripayResponse.data.amount,
              status: this.mapTripayStatus(tripayResponse.data.status),
              paymentUrl: tripayResponse.data.checkout_url,
              description: paymentRequest.description,
              paymentMethod: tripayResponse.data.payment_method,
              user: userId, // Use current user ID
            }).subscribe((paymentResponse) => {
              // Log payment creation
              this.paymentLoggingService
                .logPaymentAction({
                  action: 'PAYMENT_CREATED',
                  newStatus: this.mapTripayStatus(tripayResponse.data.status),
                  metadata: {
                    amount: tripayResponse.data.amount,
                    externalId: tripayResponse.data.merchant_ref,
                    tripayRef: tripayResponse.data.reference,
                  },
                  payment: paymentResponse.data?.id,
                  user: userId,
                })
                .subscribe();
            });

            // Convert and return Tripay response to our format
            return this.convertTripayResponse(tripayResponse);
          } else {
            throw new Error(
              tripayResponse.message || 'Payment creation failed'
            );
          }
        }),
        catchError((error) => {
          console.error('Error creating payment with Tripay:', error);
          const response = this.getMockPaymentResponse(paymentRequest);
          // Save payment to Strapi even in error case with current user ID
          this.savePaymentToStrapi({
            externalId: response.externalId,
            tripayId: response.id,
            amount: response.amount,
            status: response.status,
            paymentUrl: response.paymentUrl,
            description: paymentRequest.description,
            user: userId, // Use current user ID instead of undefined
          }).subscribe();
          return of(response);
        })
      );
  }

  // Check payment status with Tripay
  checkPaymentStatus(paymentId: string): Observable<PaymentResponse> {
    // For development, return mock data if API is not ready
    if (environment.useMockData) {
      return of(this.getMockPaymentStatus(paymentId));
    }

    const headers = new HttpHeaders({
      Authorization: `Bearer ${this.tripayApiKey}`,
    });

    return this.http
      .get<TripayResponse>(
        `${this.tripayApiUrl}/transaction/detail?reference=${paymentId}`,
        { headers }
      )
      .pipe(
        map((tripayResponse) => {
          if (tripayResponse.success) {
            return this.convertTripayResponse(tripayResponse);
          } else {
            throw new Error(
              tripayResponse.message || 'Failed to get payment status'
            );
          }
        }),
        catchError((error) => {
          console.error('Error checking payment status with Tripay:', error);
          return of(this.getMockPaymentStatus(paymentId));
        })
      );
  }

  // Mock payment response for development
  private getMockPaymentResponse(request: PaymentRequest): PaymentResponse {
    const paymentId = 'mock_payment_' + Math.floor(Math.random() * 1000000);

    return {
      id: paymentId,
      externalId: request.externalId,
      status: 'PENDING',
      paymentUrl: window.location.origin + `/payment-simulator/${paymentId}`,
      amount: request.amount,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  // Mock payment status for development
  private getMockPaymentStatus(paymentId: string): PaymentResponse {
    // Randomly determine payment status for testing
    const statuses: Array<'PENDING' | 'PAID' | 'EXPIRED' | 'FAILED'> = [
      'PENDING',
      'PAID',
      'EXPIRED',
      'FAILED',
    ];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

    return {
      id: paymentId,
      externalId: 'mock_external_id',
      status: randomStatus,
      paymentUrl: window.location.origin + `/payment-simulator/${paymentId}`,
      amount: 150000,
      createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
      updatedAt: new Date().toISOString(),
    };
  }
  // Save payment to Strapi
  savePaymentToStrapi(payment: StrapiPayment): Observable<any> {
    console.log('Saving payment to Strapi with user ID:', payment.user);

    if (environment.useMockData) {
      return of({ success: true, id: Math.floor(Math.random() * 1000) + 1 });
    }

    const strapiPaymentUrl = `${environment.url}payments`;

    // Prepare payment data with proper user relation format for Strapi v4
    // Try using the direct array format instead of connect
    const paymentData = {
      ...payment,
      users: payment.user ? [payment.user] : undefined,
    };

    // Remove the old user field to avoid conflicts
    delete paymentData.user;

    console.log('Payment data being sent to Strapi:', paymentData);
    const data = { data: paymentData };

    // Get headers with authentication
    const headers = this.getHeaders();

    return this.http.post(strapiPaymentUrl, data, { headers }).pipe(
      tap((response) => {
        console.log('Payment saved to Strapi successfully:', response);
      }),
      catchError((error) => {
        console.error('Error saving payment to Strapi:', error);
        return of({ success: false, error: error.message });
      })
    );
  }

  // Update payment status in Strapi
  updatePaymentStatus(
    externalId: string,
    status: 'PENDING' | 'PAID' | 'FAILED' | 'EXPIRED',
    userId?: number,
    testPackId?: number
  ): Observable<any> {
    if (environment.useMockData) {
      return of({ success: true, externalId, status });
    }

    const strapiPaymentUrl = `${environment.url}payments`;
    const query = `?filters[externalId][$eq]=${externalId}`;

    // First, find the payment by externalId
    return this.http.get(`${strapiPaymentUrl}${query}`).pipe(
      catchError((error) => {
        console.error('Error finding payment in Strapi:', error);
        // Log error
        this.paymentLoggingService
          .logPaymentAction({
            action: 'ERROR',
            metadata: {
              error: error.message,
              externalId: externalId,
            },
          })
          .subscribe();
        return of({ data: [] });
      }),
      switchMap((response: any) => {
        if (!response.data || response.data.length === 0) {
          return of({ success: false, error: 'Payment not found' });
        }

        const paymentId = response.data[0].id;
        const updateData = {
          data: {
            status,
            paidAt: status === 'PAID' ? new Date().toISOString() : undefined,
            users: userId ? [userId] : undefined,
            testPack: testPackId ? [testPackId] : undefined,
          },
        };

        // Then update the payment
        return this.http
          .put(`${strapiPaymentUrl}/${paymentId}`, updateData)
          .pipe(
            tap(() => {
              // If payment status is PAID and we have a testPackId, update test pack status
              if (status === 'PAID' && testPackId) {
                // Import TestManagerService di constructor jika belum ada
                const testManagerUrl = `${environment.url}test-packs/${testPackId}`;
                this.http
                  .put(testManagerUrl, {
                    data: { paymentStatus: 'PAID' },
                  })
                  .subscribe({
                    next: () =>
                      console.log('Test pack payment status updated to PAID'),
                    error: (err: any) =>
                      console.error(
                        'Error updating test pack payment status:',
                        err
                      ),
                  });
              }

              // Log status change
              this.paymentLoggingService
                .logPaymentAction({
                  action: 'STATUS_CHANGED',
                  previousStatus: 'UNKNOWN', // We don't know the previous status here
                  newStatus: status,
                  payment: paymentId,
                  user: userId,
                  metadata: {
                    testPackId: testPackId,
                  },
                })
                .subscribe();
            }),
            catchError((error) => {
              console.error('Error updating payment in Strapi:', error);
              // Log error
              this.paymentLoggingService
                .logPaymentAction({
                  action: 'ERROR',
                  metadata: {
                    error: error.message,
                    externalId: externalId,
                  },
                })
                .subscribe();
              return of({ success: false, error: error.message });
            })
          );
      })
    );
  }

  // Link test pack to payment
  linkTestPackToPayment(
    testPackId: number,
    paymentExternalId: string,
    userId?: number
  ): Observable<any> {
    return this.updatePaymentStatus(
      paymentExternalId,
      'PAID',
      userId,
      testPackId
    );
  }

  // Retry a failed or expired payment
  retryPayment(paymentId: number): Observable<any> {
    if (environment.useMockData) {
      // Generate a new mock payment with the same details
      const mockPaymentId =
        'mock_payment_' + Math.floor(Math.random() * 1000000);
      return of({
        id: mockPaymentId,
        externalId: 'ext_' + mockPaymentId,
        status: 'PENDING',
        paymentUrl:
          window.location.origin + `/payment-simulator/${mockPaymentId}`,
        amount: 150000,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }

    // In a real implementation, we would:
    // 1. Get the original payment details from Strapi
    // 2. Create a new payment with the same details
    // 3. Update the original payment status to CANCELLED or link it to the new payment

    // For now, we'll just return a mock response
    return this.http
      .get<any>(`${environment.url}payments/${paymentId}?populate=user`)
      .pipe(
        switchMap((response) => {
          if (!response.data) {
            throw new Error('Payment not found');
          }

          const originalPayment = response.data;
          const paymentRequest: PaymentRequest = {
            amount: originalPayment.attributes.amount,
            description: originalPayment.attributes.description,
            externalId: 'retry_' + originalPayment.attributes.externalId,
            customerName:
              originalPayment.attributes.user?.data?.attributes?.username ||
              'Customer',
            customerEmail:
              originalPayment.attributes.user?.data?.attributes?.email ||
              '<EMAIL>',
            successRedirectUrl: `${window.location.origin}/payment-success`,
            failureRedirectUrl: `${window.location.origin}/payment-error`,
          };

          return this.createPayment(paymentRequest);
        }),
        catchError((error) => {
          console.error('Error retrying payment:', error);
          // Return mock data on error for development
          if (environment.useMockData) {
            const mockPaymentId =
              'mock_payment_' + Math.floor(Math.random() * 1000000);
            return of({
              id: mockPaymentId,
              externalId: 'ext_' + mockPaymentId,
              status: 'PENDING',
              paymentUrl:
                window.location.origin + `/payment-simulator/${mockPaymentId}`,
              amount: 150000,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            });
          }
          throw error;
        })
      );
  }

  // Helper method to create Tripay request
  private createTripayRequest(paymentRequest: PaymentRequest): TripayRequest {
    const orderItems = [
      {
        name: paymentRequest.description,
        price: paymentRequest.amount,
        quantity: 1,
      },
    ];

    // Create signature for Tripay
    const signature = this.createTripaySignature(
      paymentRequest.externalId,
      paymentRequest.amount
    );

    return {
      method: 'BRIVA', // Default payment method, bisa diganti sesuai kebutuhan
      merchant_ref: paymentRequest.externalId,
      amount: paymentRequest.amount,
      customer_name: paymentRequest.customerName,
      customer_email: paymentRequest.customerEmail,
      order_items: orderItems,
      return_url: paymentRequest.successRedirectUrl,
      expired_time: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24 hours from now
      signature: signature,
    };
  }

  // Helper method to create Tripay signature
  private createTripaySignature(merchantRef: string, amount: number): string {
    // Tripay signature formula: hmac_sha256(private_key, merchant_code + merchant_ref + amount)
    const data = this.tripayMerchantCode + merchantRef + amount;

    try {
      // Use crypto-js to create HMAC SHA256 signature
      const signature = CryptoJS.HmacSHA256(
        data,
        this.tripayPrivateKey
      ).toString();
      return signature;
    } catch (error) {
      console.error('Error creating signature:', error);
      // Fallback to dummy signature for development
      return 'dummy_signature_' + btoa(data);
    }
  }

  // Helper method to map Tripay status to our internal status
  private mapTripayStatus(
    tripayStatus: string
  ): 'PENDING' | 'PAID' | 'EXPIRED' | 'FAILED' {
    switch (tripayStatus.toLowerCase()) {
      case 'paid':
        return 'PAID';
      case 'expired':
        return 'EXPIRED';
      case 'failed':
      case 'canceled':
        return 'FAILED';
      case 'unpaid':
      default:
        return 'PENDING';
    }
  }

  // Convert Tripay response to our PaymentResponse format
  private convertTripayResponse(
    tripayResponse: TripayResponse
  ): PaymentResponse {
    return {
      id: tripayResponse.data.reference,
      externalId: tripayResponse.data.merchant_ref,
      status: this.mapTripayStatus(tripayResponse.data.status),
      paymentUrl: tripayResponse.data.checkout_url,
      amount: tripayResponse.data.amount,
      createdAt: new Date(tripayResponse.data.created_at * 1000).toISOString(),
      updatedAt: new Date(tripayResponse.data.updated_at * 1000).toISOString(),
    };
  }
}
