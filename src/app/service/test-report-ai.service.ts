import { Injectable } from '@angular/core';
import { Observable, of, forkJoin } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { DiscReportService, DiscReportData } from './disc-report.service';
import { MbtiService, MbtiReport, MbtiAnalysis } from './mbti.service';
import { TeamsService, TeamsReport, TeamsAnalysis } from './teams.service';
import { ValuesService, ValuesReport, ValuesAnalysis } from './values.service';
import { TestManagerService } from './test-manager.service';
import { ChartType, ChartData } from 'chart.js';
import { environment } from 'src/environments/environment.development';

/**
 * Interface for AI analysis result
 */
export interface AiAnalysisResult {
  testSummary: string;
  personalityOverview: string;
  strengths: string[];
  weaknesses: string[];
  workStyle: string;
  teamStrengths: string[];
  teamWeaknesses: string[];
  similarFigures: {
    name: string;
    description: string;
    similarity: string;
  }[];
  suitableJobs: string[];
}

/**
 * Interface for AI test report data
 */
export interface TestReportAiData {
  testPackId: number;
  userId: number;
  username: string;
  createdAt: string;
  hasDiscReport?: boolean;
  hasTeamsReport?: boolean;
  hasMbtiReport?: boolean;
  hasValuesReport?: boolean;
  discReport?: {
    answerId: number;
    reportData: DiscReportData;
  };
  mbtiReport?: {
    answerId: number;
    report: MbtiReport;
    analysis: MbtiAnalysis;
  };
  teamsReport?: {
    answerId: number;
    report: TeamsReport;
    analysis: TeamsAnalysis;
  };
  valuesReport?: {
    answerId: number;
    report: ValuesReport;
    analysis: ValuesAnalysis;
  };
  aiAnalysis?: AiAnalysisResult;
}

/**
 * Service for generating AI-powered test reports
 */
@Injectable({
  providedIn: 'root',
})
export class TestReportAiService {
  // URL for the AI engine JSON file
  private aiEngineUrl = './assets/data/ai-analysis-engine.json';
  // Flag to use mock data for development
  private useMockData = environment.useMockData || false;

  constructor(
    private http: HttpClient,
    private testManager: TestManagerService,
    private discReportService: DiscReportService,
    private mbtiService: MbtiService,
    private teamsService: TeamsService,
    private valuesService: ValuesService
  ) {}

  /**
   * Mendapatkan data laporan AI berdasarkan ID test pack
   * @param testPackId ID test pack
   * @returns Observable berisi data laporan AI
   */
  /**
   * Get AI test report data by test pack ID
   * @param testPackId The ID of the test pack
   * @returns Observable with the AI test report data
   */
  getTestReportAi(testPackId: number): Observable<TestReportAiData> {
    // If mock data is enabled and we're in development, return mock data
    if (this.useMockData) {
      return this.getMockTestReportData(testPackId);
    }

    return this.testManager.getTestPackById(testPackId).pipe(
      switchMap((testPack) => {
        if (!testPack.data) {
          throw new Error('Test pack tidak ditemukan');
        }

        const testPackData = testPack.data;
        const userId = testPackData.attributes.users.data[0]?.id;
        const username =
          testPackData.attributes.users.data[0]?.attributes.username ||
          'Unknown';
        const createdAt = testPackData.attributes.createdAt;

        const reportData: TestReportAiData = {
          testPackId,
          userId,
          username,
          createdAt,
        };

        // Handle both tests and testManager properties for backward compatibility
        const testManagers =
          testPackData.attributes.tests?.data ||
          testPackData.attributes.testManager?.data ||
          [];
        const reportObservables: Observable<any>[] = [];

        // Define the test manager interface to include both resultId and answer properties
        interface TestManagerAttributes {
          type: string;
          isCompleted: boolean;
          order: number;
          route: string;
          resultId?: number;
          answer?: {
            data?: {
              id: number;
            };
          };
        }

        // Cari test manager untuk setiap jenis tes
        const discTestManager = testManagers.find(
          (tm: any) => tm.attributes.type === 'DISC'
        ) as { attributes: TestManagerAttributes } | undefined;

        const mbtiTestManager = testManagers.find(
          (tm: any) => tm.attributes.type === 'MBTI'
        ) as { attributes: TestManagerAttributes } | undefined;

        const teamsTestManager = testManagers.find(
          (tm: any) => tm.attributes.type === 'TEAMS'
        ) as { attributes: TestManagerAttributes } | undefined;

        const valuesTestManager = testManagers.find(
          (tm: any) => tm.attributes.type === 'VALUES'
        ) as { attributes: TestManagerAttributes } | undefined;

        // Tambahkan observable untuk laporan DISC jika ada
        if (discTestManager && discTestManager.attributes.isCompleted) {
          // Use resultId if available, otherwise try to get from answer relationship
          const discAnswerId =
            discTestManager.attributes.resultId ||
            discTestManager.attributes.answer?.data?.id;
          if (discAnswerId) {
            reportObservables.push(
              this.discReportService.getDiscReportByAnswerId(discAnswerId).pipe(
                map((discReportData) => {
                  reportData.hasDiscReport = true;
                  reportData.discReport = {
                    answerId: discAnswerId,
                    reportData: discReportData,
                  };
                  return reportData;
                })
              )
            );
          }
        }

        // Tambahkan observable untuk laporan MBTI jika ada
        if (mbtiTestManager && mbtiTestManager.attributes.isCompleted) {
          // Use resultId if available, otherwise try to get from answer relationship
          const mbtiAnswerId =
            mbtiTestManager.attributes.resultId ||
            mbtiTestManager.attributes.answer?.data?.id;
          if (mbtiAnswerId) {
            reportObservables.push(
              forkJoin({
                report: this.mbtiService.getMbtiReportById(mbtiAnswerId),
                analysis: this.mbtiService
                  .getMbtiReportById(mbtiAnswerId)
                  .pipe(
                    switchMap((report) =>
                      this.mbtiService.getMbtiAnalysis(report.mbtiResult)
                    )
                  ),
              }).pipe(
                map((result) => {
                  reportData.hasMbtiReport = true;
                  reportData.mbtiReport = {
                    answerId: mbtiAnswerId,
                    report: result.report,
                    analysis: result.analysis as MbtiAnalysis,
                  };
                  return reportData;
                })
              )
            );
          }
        }

        // Tambahkan observable untuk laporan TEAMS jika ada
        if (teamsTestManager && teamsTestManager.attributes.isCompleted) {
          // Use resultId if available, otherwise try to get from answer relationship
          const teamsAnswerId =
            teamsTestManager.attributes.resultId ||
            teamsTestManager.attributes.answer?.data?.id;
          if (teamsAnswerId) {
            reportObservables.push(
              forkJoin({
                report: this.teamsService.getTeamsReportById(teamsAnswerId),
                analysis: this.teamsService
                  .getTeamsReportById(teamsAnswerId)
                  .pipe(
                    switchMap((report) =>
                      this.teamsService.getTeamsAnalysis(
                        report.highestCategories
                      )
                    )
                  ),
              }).pipe(
                map((result) => {
                  reportData.hasTeamsReport = true;
                  reportData.teamsReport = {
                    answerId: teamsAnswerId,
                    report: result.report,
                    analysis: result.analysis as TeamsAnalysis,
                  };
                  return reportData;
                })
              )
            );
          }
        }

        // Tambahkan observable untuk laporan VALUES jika ada
        if (valuesTestManager && valuesTestManager.attributes.isCompleted) {
          // Use resultId if available, otherwise try to get from answer relationship
          const valuesAnswerId =
            valuesTestManager.attributes.resultId ||
            valuesTestManager.attributes.answer?.data?.id;
          if (valuesAnswerId) {
            reportObservables.push(
              forkJoin({
                report: this.valuesService.getValuesReportById(valuesAnswerId),
                analysis: this.valuesService
                  .getValuesReportById(valuesAnswerId)
                  .pipe(
                    switchMap((report) =>
                      this.valuesService.getValuesAnalysis(
                        report.highestCategories
                      )
                    )
                  ),
              }).pipe(
                map((result) => {
                  reportData.hasValuesReport = true;
                  reportData.valuesReport = {
                    answerId: valuesAnswerId,
                    report: result.report,
                    analysis: result.analysis as ValuesAnalysis,
                  };
                  return reportData;
                })
              )
            );
          }
        }

        // Jika tidak ada laporan yang tersedia, tambahkan data dummy dan hasilkan analisis AI default
        if (reportObservables.length === 0) {
          // Tidak ada laporan yang tersedia, tambahkan data dummy

          // Tambahkan data dummy untuk DISC
          reportData.hasDiscReport = true;
          // @ts-ignore - Ini hanya data dummy untuk demonstrasi
          reportData.discReport = {
            answerId: 0,
            reportData: {
              // Tambahkan properti yang diperlukan untuk DiscReportData
              dataSource: [
                { category: 'D', most: 15, least: 5, difference: 10 },
                { category: 'I', most: 8, least: 12, difference: -4 },
                { category: 'S', most: 5, least: 15, difference: -10 },
                { category: 'C', most: 12, least: 8, difference: 4 },
              ],
              chartType: 'line' as ChartType,
              mostString: 'D',
              leastString: 'S',
              differenceString: 'D',
              mostChartData: {
                // @ts-ignore - Ini hanya data dummy untuk demonstrasi
                labels: ['D', 'I', 'S', 'C'],
                datasets: [
                  {
                    data: [15, 8, 5, 12],
                    backgroundColor: [
                      '#FF6384',
                      '#36A2EB',
                      '#FFCE56',
                      '#4BC0C0',
                    ],
                  },
                ],
              },
              mostChartOptions: {},
              leastChartData: {
                // @ts-ignore - Ini hanya data dummy untuk demonstrasi
                labels: ['D', 'I', 'S', 'C'],
                datasets: [
                  {
                    data: [5, 12, 15, 8],
                    backgroundColor: [
                      '#FF6384',
                      '#36A2EB',
                      '#FFCE56',
                      '#4BC0C0',
                    ],
                  },
                ],
              },
              leastChartOptions: {},
              differenceChartData: {
                // @ts-ignore - Ini hanya data dummy untuk demonstrasi
                labels: ['D', 'I', 'S', 'C'],
                datasets: [
                  {
                    data: [10, -4, -10, 4],
                    backgroundColor: [
                      '#FF6384',
                      '#36A2EB',
                      '#FFCE56',
                      '#4BC0C0',
                    ],
                  },
                ],
              },
              differenceChartOptions: {},
            },
          };

          // Tambahkan data dummy untuk MBTI
          reportData.hasMbtiReport = true;
          // @ts-ignore - Ini hanya data dummy untuk demonstrasi
          reportData.mbtiReport = {
            answerId: 0,
            // @ts-ignore - Ini hanya data dummy untuk demonstrasi
            report: {
              mbtiResult: 'INTJ',
              chartData: {
                labels: ['E-I', 'S-N', 'T-F', 'J-P'],
                datasets: [
                  {
                    data: [-30, 40, 25, 15],
                    backgroundColor: [
                      '#FF6384',
                      '#36A2EB',
                      '#FFCE56',
                      '#4BC0C0',
                    ],
                  },
                ],
              },
            },
            // @ts-ignore - Ini hanya data dummy untuk demonstrasi
            analysis: {
              patternName: 'The Architect',
              patternSlogan:
                'Imaginative and strategic thinkers, with a plan for everything',
            },
          };

          // Tambahkan data dummy untuk TEAMS
          reportData.hasTeamsReport = true;
          // @ts-ignore - Ini hanya data dummy untuk demonstrasi
          reportData.teamsReport = {
            answerId: 0,
            // @ts-ignore - Ini hanya data dummy untuk demonstrasi
            report: {
              highestCategories: [
                { category: 'T', value: 18 },
                { category: 'A', value: 15 },
              ],
              chartData: {
                labels: ['T', 'E', 'A', 'M', 'S'],
                datasets: [
                  {
                    data: [18, 10, 15, 8, 9],
                    backgroundColor: [
                      '#FF6384',
                      '#36A2EB',
                      '#FFCE56',
                      '#4BC0C0',
                      '#9966FF',
                    ],
                  },
                ],
              },
            },
            // @ts-ignore - Ini hanya data dummy untuk demonstrasi
            analysis: {
              mainCategory: 'T',
              mainCategoryName: 'Theorist',
            },
          };

          // Tambahkan data dummy untuk VALUES
          reportData.hasValuesReport = true;
          // @ts-ignore - Ini hanya data dummy untuk demonstrasi
          reportData.valuesReport = {
            answerId: 0,
            // @ts-ignore - Ini hanya data dummy untuk demonstrasi
            report: {
              highestCategories: [
                // @ts-ignore - Ini hanya data dummy untuk demonstrasi
                { category: 'I', value: 20 },
                // @ts-ignore - Ini hanya data dummy untuk demonstrasi
                { category: 'T', value: 18 },
              ],
              chartData: {
                labels: ['I', 'T', 'R', 'S', 'P', 'A'],
                datasets: [
                  {
                    data: [20, 18, 12, 10, 8, 7],
                    backgroundColor: [
                      '#FF6384',
                      '#36A2EB',
                      '#FFCE56',
                      '#4BC0C0',
                      '#9966FF',
                      '#FF9F40',
                    ],
                  },
                ],
              },
            },
            // @ts-ignore - Ini hanya data dummy untuk demonstrasi
            analysis: {
              mainCategory: 'I',
              mainCategoryName: 'Independence',
              mainCategoryValue: 20,
            },
          };

          return this.generateAiAnalysis(reportData).pipe(
            map((aiAnalysis) => {
              reportData.aiAnalysis = aiAnalysis;

              return reportData;
            })
          );
        }

        // Jalankan semua observable dan gabungkan hasilnya
        return forkJoin(reportObservables).pipe(
          switchMap(() => {
            // Setelah mendapatkan semua data tes, lakukan analisis AI
            return this.generateAiAnalysis(reportData).pipe(
              map((aiAnalysis) => {
                reportData.aiAnalysis = aiAnalysis;
                return reportData;
              })
            );
          })
        );
      }),
      catchError((error) => {
        console.error('Error loading test report AI:', error);
        throw new Error(
          'Gagal memuat data test report AI: ' +
            (error.message || 'Unknown error')
        );
      })
    );
  }

  /**
   * Menghasilkan analisis AI berdasarkan data laporan
   * @param reportData Data laporan dari berbagai tes
   * @returns Observable berisi hasil analisis AI
   */
  private generateAiAnalysis(
    reportData: TestReportAiData
  ): Observable<AiAnalysisResult> {
    // Untuk sementara, gunakan file JSON sebagai engine AI
    return this.http.get<any>(this.aiEngineUrl).pipe(
      catchError((_) => {
        return of({
          defaultAnalysis: {
            testSummary: 'Tidak dapat memuat engine AI.',
            personalityOverview: 'Terjadi kesalahan saat memuat data analisis.',
            strengths: ['Data tidak tersedia'],
            weaknesses: ['Data tidak tersedia'],
            workStyle: 'Data tidak tersedia',
            teamStrengths: ['Data tidak tersedia'],
            teamWeaknesses: ['Data tidak tersedia'],
            similarFigures: [
              { name: 'Data tidak tersedia', description: '', similarity: '' },
            ],
            suitableJobs: ['Data tidak tersedia'],
          },
        });
      }),
      map((aiEngine) => {
        // Dapatkan pola dari setiap tes
        const discPattern =
          reportData.discReport?.reportData.differenceString || '';
        const mbtiPattern = reportData.mbtiReport?.report.mbtiResult || '';

        // Pola TEAMS dan VALUES bisa digunakan untuk pengembangan lebih lanjut
        // const teamsPattern =
        //   reportData.teamsReport?.report.highestCategories
        //     ?.map((c) => c.category)
        //     .join('') || '';
        // const valuesPattern =
        //   reportData.valuesReport?.report.highestCategories
        //     ?.map((c) => c.category)
        //     .join('') || '';

        // Cari analisis yang paling cocok dari engine AI
        // Ini hanya implementasi sederhana, bisa dikembangkan lebih lanjut
        let bestMatch = aiEngine.defaultAnalysis;

        // Coba cari kecocokan berdasarkan MBTI (sebagai contoh)
        if (
          mbtiPattern &&
          aiEngine.mbtiPatterns &&
          aiEngine.mbtiPatterns[mbtiPattern]
        ) {
          bestMatch = aiEngine.mbtiPatterns[mbtiPattern];
        } else if (
          discPattern &&
          aiEngine.discPatterns &&
          aiEngine.discPatterns[discPattern]
        ) {
          bestMatch = aiEngine.discPatterns[discPattern];
        }

        // Tambahkan logika untuk menggabungkan hasil dari berbagai tes
        // Ini bisa dikembangkan lebih lanjut sesuai kebutuhan

        // Pastikan semua properti yang diperlukan ada
        const result: AiAnalysisResult = {
          testSummary: bestMatch.testSummary || 'Data tidak tersedia',
          personalityOverview:
            bestMatch.personalityOverview || 'Data tidak tersedia',
          strengths: bestMatch.strengths || ['Data tidak tersedia'],
          weaknesses: bestMatch.weaknesses || ['Data tidak tersedia'],
          workStyle: bestMatch.workStyle || 'Data tidak tersedia',
          teamStrengths: bestMatch.teamStrengths || ['Data tidak tersedia'],
          teamWeaknesses: bestMatch.teamWeaknesses || ['Data tidak tersedia'],
          similarFigures: bestMatch.similarFigures || [
            { name: 'Data tidak tersedia', description: '', similarity: '' },
          ],
          suitableJobs: bestMatch.suitableJobs || ['Data tidak tersedia'],
        };

        return result;
      }),
      catchError((error) => {
        console.error('Error generating AI analysis:', error);
        // Kembalikan analisis default jika terjadi kesalahan
        return of({
          testSummary:
            'Tidak dapat menghasilkan analisis karena terjadi kesalahan.',
          personalityOverview: 'Data tidak tersedia.',
          strengths: ['Data tidak tersedia'],
          weaknesses: ['Data tidak tersedia'],
          workStyle: 'Data tidak tersedia',
          teamStrengths: ['Data tidak tersedia'],
          teamWeaknesses: ['Data tidak tersedia'],
          similarFigures: [
            {
              name: 'Data tidak tersedia',
              description: '',
              similarity: '',
            },
          ],
          suitableJobs: ['Data tidak tersedia'],
        });
      })
    );
  }

  /**
   * Generate mock test report data for development
   * @param testPackId The ID of the test pack
   * @returns Observable with mock test report data
   */
  private getMockTestReportData(
    testPackId: number
  ): Observable<TestReportAiData> {
    // Create mock report data
    const reportData: TestReportAiData = {
      testPackId,
      userId: 1,
      username: 'Test User',
      createdAt: new Date().toISOString(),
      hasDiscReport: true,
      hasMbtiReport: true,
      hasTeamsReport: true,
      hasValuesReport: true,
    };

    // Add mock DISC report data
    reportData.discReport = {
      answerId: 1,
      reportData: {
        dataSource: [
          { category: 'D', most: 15, least: 5, difference: 10 },
          { category: 'I', most: 8, least: 12, difference: -4 },
          { category: 'S', most: 5, least: 15, difference: -10 },
          { category: 'C', most: 12, least: 8, difference: 4 },
        ],
        chartType: 'bar' as ChartType,
        mostString: 'D',
        leastString: 'S',
        differenceString: 'D',
        mostChartData: {
          labels: ['D', 'I', 'S', 'C'],
          datasets: [
            {
              data: [15, 8, 5, 12],
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
            },
          ],
        },
        mostChartOptions: {},
        leastChartData: {
          labels: ['D', 'I', 'S', 'C'],
          datasets: [
            {
              data: [5, 12, 15, 8],
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
            },
          ],
        },
        leastChartOptions: {},
        differenceChartData: {
          labels: ['D', 'I', 'S', 'C'],
          datasets: [
            {
              data: [10, -4, -10, 4],
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
            },
          ],
        },
        differenceChartOptions: {},
      },
    };

    // Add mock MBTI report data
    reportData.mbtiReport = {
      answerId: 2,
      report: {
        mbtiResult: 'INTJ',
        categoryTotals: {
          E: 10,
          I: 40,
          S: 15,
          N: 55,
          T: 60,
          F: 35,
          J: 50,
          P: 35,
        },
        chartData: {
          labels: ['E-I', 'S-N', 'T-F', 'J-P'],
          datasets: [
            {
              data: [-30, 40, 25, 15],
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
            },
          ],
        } as ChartData<'bar'>,
      },
      analysis: {
        pattern: 'INTJ',
        patternName: 'The Architect',
        patternSlogan:
          'Imaginative and strategic thinkers, with a plan for everything',
        analysisDetail:
          'INTJs are analytical problem-solvers, eager to improve systems and processes. They are strategic, independent, and determined, but can sometimes be overly critical, dismissive of emotions, and arrogant.',
      },
    };

    // Add mock TEAMS report data
    reportData.teamsReport = {
      answerId: 3,
      report: {
        categoryTotals: {
          T: 18,
          E: 10,
          A: 15,
          M: 8,
          S: 9,
        },
        highestCategories: [
          { category: 'T' as 'T', value: 18 },
          { category: 'A' as 'A', value: 15 },
        ],
        chartData: {
          labels: ['T', 'E', 'A', 'M', 'S'],
          datasets: [
            {
              data: [18, 10, 15, 8, 9],
              backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
              ],
            },
          ],
        } as ChartData<'line'>,
      },
      analysis: {
        mainCategory: 'T',
        mainCategoryName: 'Theorist',
        mainCategoryValue: 18,
        secondCategory: 'A',
        secondCategoryName: 'Analyzer',
        secondCategoryValue: 15,
        analysisTitle: 'Theorist-Analyzer',
        analysisDetail:
          'Theorists are analytical and logical thinkers who excel at problem-solving. Analyzers are detail-oriented and methodical in their approach.',
        combinationName: 'Analytical Thinker',
        combinationDetail:
          'Combines theoretical thinking with detailed analysis.',
      },
    };

    // Add mock VALUES report data
    reportData.valuesReport = {
      answerId: 4,
      report: {
        categoryTotals: {
          L: 20,
          E: 18,
          P: 12,
          J: 10,
        },
        highestCategories: [
          { category: 'L' as 'L', value: 20 },
          { category: 'E' as 'E', value: 18 },
        ],
        chartData: {
          labels: ['L', 'E', 'P', 'J'],
          datasets: [
            {
              data: [20, 18, 12, 10],
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
            },
          ],
        } as ChartData<'line'>,
      },
      analysis: {
        mainCategory: 'L',
        mainCategoryName: 'Loyalty',
        mainCategoryValue: 20,
        analysisDetail:
          'People who value loyalty prioritize commitment and dedication to relationships, groups, and causes. They are reliable, trustworthy, and value long-term connections.',
      },
    };

    // Generate AI analysis for the mock data
    return this.generateAiAnalysis(reportData).pipe(
      map((aiAnalysis) => {
        reportData.aiAnalysis = aiAnalysis;
        return reportData;
      })
    );
  }
}
