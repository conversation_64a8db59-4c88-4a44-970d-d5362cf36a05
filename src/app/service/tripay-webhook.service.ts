import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment.development';
import {
  TripayWebhookPayload,
  TripayWebhookRequest,
} from '../interfaces/tripay-webhook';
import { PaymentService } from './payment.service';
import { PaymentLoggingService } from './payment-logging.service';
import * as CryptoJS from 'crypto-js';

@Injectable({
  providedIn: 'root',
})
export class TripayWebhookService {
  private tripayCallbackToken =
    environment.tripay?.callbackToken || 'your-callback-token';

  constructor(
    private http: HttpClient,
    private paymentService: PaymentService,
    private paymentLoggingService: PaymentLoggingService
  ) {}

  /**
   * Verify webhook signature from Tripay
   * @param json The JSON payload as string
   * @param signature The signature from header
   * @returns boolean indicating if signature is valid
   */
  verifySignature(json: string, signature: string): boolean {
    try {
      const computedSignature = CryptoJS.HmacSHA256(
        json,
        this.tripayCallbackToken
      ).toString();
      return computedSignature === signature;
    } catch (error) {
      console.error('Error verifying signature:', error);
      return false;
    }
  }

  /**
   * Process incoming webhook from Tripay
   * @param webhookRequest The webhook request payload
   * @returns Observable with processing result
   */
  processWebhook(webhookRequest: TripayWebhookRequest): Observable<any> {
    const { event, data } = webhookRequest;

    // Log the webhook event
    this.paymentLoggingService
      .logPaymentAction({
        action: 'WEBHOOK_RECEIVED',
        metadata: {
          event: event,
          reference: data.reference,
          merchant_ref: data.merchant_ref,
          status: data.status,
          amount: data.total_amount,
        },
      })
      .subscribe();

    // Process based on event type
    switch (event) {
      case 'payment_status':
        return this.handlePaymentStatusUpdate(data);
      default:
        console.log(`Unhandled webhook event: ${event}`);
        return of({ success: true, message: 'Event ignored' });
    }
  }

  /**
   * Handle payment status update webhook
   * @param data Webhook payload data
   * @returns Observable with processing result
   */
  private handlePaymentStatusUpdate(
    data: TripayWebhookPayload
  ): Observable<any> {
    const status = this.mapTripayStatus(data.status);

    return this.paymentService.updatePaymentStatus(
      data.merchant_ref,
      status,
      undefined, // userId will be determined from payment record
      undefined // testPackId will be determined from payment record
    );
  }

  /**
   * Map Tripay status to internal status
   * @param tripayStatus Status from Tripay
   * @returns Internal payment status
   */
  private mapTripayStatus(
    tripayStatus: string
  ): 'PENDING' | 'PAID' | 'EXPIRED' | 'FAILED' {
    switch (tripayStatus.toLowerCase()) {
      case 'paid':
        return 'PAID';
      case 'expired':
        return 'EXPIRED';
      case 'failed':
      case 'canceled':
        return 'FAILED';
      case 'unpaid':
      default:
        return 'PENDING';
    }
  }
}
