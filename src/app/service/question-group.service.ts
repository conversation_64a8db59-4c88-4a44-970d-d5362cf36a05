// QUESTIN GROUP UNTUK MENGAMBIL QUESTION GROUP DISC

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment.development';

@Injectable({
  providedIn: 'root',
})
export class QuestionGroupService {
  slug = 'question-groups?populate=questions';
  UrlSlug = `${environment.url}` + this.slug;

  constructor(private http: HttpClient) {}

  getQuestionGroups(): Observable<any> {
    return this.http.get(this.UrlSlug);
  }
}
