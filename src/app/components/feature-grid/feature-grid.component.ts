import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Feature {
  icon: string;
  title: string;
  description: string;
}

@Component({
  selector: 'app-feature-grid',
  templateUrl: './feature-grid.component.html',
  styleUrls: ['./feature-grid.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class FeatureGridComponent {
  @Input() title!: string;
  @Input() subtitle!: string;
  @Input() features!: Feature[];
}
