<div class="feature-grid-container">
  <div class="feature-grid-content">
    <div class="feature-grid-header">
      <h2 *ngIf="title" class="feature-grid-title">
        {{ title }}
      </h2>
      <p *ngIf="subtitle" class="feature-grid-subtitle">{{ subtitle }}</p>
    </div>

    <div class="features-grid">
      <div *ngFor="let feature of features" class="feature-card">
        <div class="feature-icon-container">
          <div [innerHTML]="feature.icon" class="feature-icon"></div>
        </div>
        <h3 class="feature-title">
          {{ feature.title }}
        </h3>
        <p class="feature-description">{{ feature.description }}</p>
      </div>
    </div>
  </div>
</div>
