import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FeatureGridComponent } from './feature-grid.component';

describe('FeatureGridComponent', () => {
  let component: FeatureGridComponent;
  let fixture: ComponentFixture<FeatureGridComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FeatureGridComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(FeatureGridComponent);
    component = fixture.componentInstance;

    // Set required inputs
    component.title = 'Test Features';
    component.subtitle = 'Test Subtitle';
    component.features = [
      {
        icon: '<svg>test</svg>',
        title: 'Feature 1',
        description: 'Description 1',
      },
      {
        icon: '<svg>test2</svg>',
        title: 'Feature 2',
        description: 'Description 2',
      },
    ];

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display title and subtitle', () => {
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('h2').textContent).toContain('Test Features');
    expect(compiled.querySelector('p').textContent).toContain('Test Subtitle');
  });

  it('should render all features', () => {
    const compiled = fixture.nativeElement;
    const featureCards = compiled.querySelectorAll('.bg-white');
    expect(featureCards.length).toBe(2);
  });
});
