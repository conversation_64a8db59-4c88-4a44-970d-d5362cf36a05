/* Modern Feature Grid Component */

.feature-grid-container {
  background: var(--color-white);
  padding: var(--spacing-16) var(--spacing-4);
  position: relative;
}

.feature-grid-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-gray-25) 0%, var(--color-white) 100%);
  opacity: 0.5;
  pointer-events: none;
}

.feature-grid-content {
  max-width: 1280px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.feature-grid-header {
  text-align: center;
  margin-bottom: var(--spacing-16);
}

.feature-grid-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
  letter-spacing: -0.025em;
}

.feature-grid-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
  max-width: 700px;
  margin: 0 auto;
  line-height: var(--line-height-relaxed);
  font-weight: var(--font-weight-normal);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-8);
}

.feature-card {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  border: 1px solid var(--color-gray-200);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  transform: scaleX(0);
  transition: transform var(--transition-base);
  transform-origin: left;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-200);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-icon-container {
  width: var(--spacing-16);
  height: var(--spacing-16);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
}

.feature-card:hover .feature-icon-container {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  width: var(--spacing-8);
  height: var(--spacing-8);
  color: var(--color-white);
}

.feature-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-3);
  line-height: var(--line-height-snug);
}

.feature-description {
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-base);
  margin: 0;
}

/* Enhanced Animations */
.feature-card {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .feature-grid-container {
    padding: var(--spacing-12) var(--spacing-4);
  }

  .feature-grid-header {
    margin-bottom: var(--spacing-12);
  }

  .feature-grid-title {
    font-size: var(--font-size-3xl);
  }

  .feature-grid-subtitle {
    font-size: var(--font-size-lg);
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }

  .feature-card {
    padding: var(--spacing-6);
  }

  .feature-icon-container {
    width: var(--spacing-12);
    height: var(--spacing-12);
  }

  .feature-icon {
    width: var(--spacing-6);
    height: var(--spacing-6);
  }
}

@media (max-width: 640px) {
  .feature-grid-container {
    padding: var(--spacing-10) var(--spacing-4);
  }
}
