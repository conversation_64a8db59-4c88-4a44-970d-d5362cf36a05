import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CustomerTestSelectionComponent } from '../customer-test-selection/customer-test-selection.component';

@Component({
  selector: 'app-test-selection-section',
  templateUrl: './test-selection-section.component.html',
  styleUrls: ['./test-selection-section.component.css'],
  standalone: true,
  imports: [CommonModule, CustomerTestSelectionComponent],
})
export class TestSelectionSectionComponent {
  @Input() title!: string;
  @Input() subtitle!: string;
  @Input() showOnHomePage: boolean = true;
}
