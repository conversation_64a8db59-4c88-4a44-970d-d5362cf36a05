import { Component, Input } from '@angular/core';
import { ChartData, ChartOptions, ChartType } from 'chart.js';

@Component({
  selector: 'app-disc-chart',
  templateUrl: './disc-chart.component.html',
  styleUrls: ['./disc-chart.component.css'],
})
export class DiscChartComponent {
  @Input() chartData!: ChartData; // Data chart dari parent
  @Input() chartOptions!: ChartOptions; // Opsi chart dari parent
  @Input() chartType!: ChartType; // Tipe chart dari parent (dapat diubah)
}
