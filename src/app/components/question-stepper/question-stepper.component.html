<div
  *ngIf="questionsToShow.length > 0"
  class="max-w-3xl mx-auto px-4 sm:px-6 py-4 sm:py-6"
>
  <!-- Progress Bar -->
  <div class="mb-6">
    <div class="flex justify-between mb-2 text-sm text-gray-600">
      <span>Progress</span>
      <span>{{ currentStep + 1 }}/{{ totalStep }}</span>
    </div>
    <div class="h-2 bg-gray-200 rounded-full">
      <div
        class="h-2 bg-blue-600 rounded-full transition-all duration-300"
        [style.width]="((currentStep + 1) / totalStep) * 100 + '%'"
      ></div>
    </div>
  </div>

  <!-- Questions -->
  <div class="space-y-6">
    <div *ngFor="let ques of questionsToShow">
      <app-questionaire-with-radio
        [textPertanyaan]="ques.attributes.question"
        [jawaban]="ques.attributes.answer_type.data.attributes.AnswerOption"
        [selectedValue]="getSelectedValue(ques.id)"
        (onAnswerSelected)="selectAnswer(ques.id, $event)"
        [category]="ques.attributes.category"
      ></app-questionaire-with-radio>
    </div>
  </div>

  <!-- Navigation -->
  <div class="flex justify-between mt-8">
    <button
      (click)="previous()"
      [disabled]="currentStep === 0"
      class="px-4 sm:px-6 py-2 bg-gray-500 text-white rounded-lg disabled:bg-gray-300 transition-colors hover:bg-gray-600"
    >
      Kembali
    </button>

    <button
      *ngIf="currentStep !== totalStep - 1"
      (click)="next()"
      [disabled]="currentStep === totalStep - 1"
      class="px-4 sm:px-6 py-2 bg-blue-500 text-white rounded-lg disabled:bg-blue-300 transition-colors hover:bg-blue-600"
    >
      Lanjut
    </button>

    <!-- Tombol Submit Jawaban -->
    <button
      *ngIf="currentStep === totalStep - 1"
      (click)="submitAnswers()"
      [disabled]="!isFormComplete()"
      class="bg-green-500 text-white px-4 py-2 rounded disabled:bg-gray-300"
    >
      Submit Jawaban
    </button>
  </div>
</div>
