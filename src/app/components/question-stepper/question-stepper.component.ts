import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { Questions } from 'src/app/interfaces/questions';

@Component({
  selector: 'app-question-stepper',
  templateUrl: './question-stepper.component.html',
  styleUrls: ['./question-stepper.component.css'],
})
export class QuestionStepperComponent implements OnInit, OnChanges {
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['dataSoal']) {
      //

      this.totalStep = Math.ceil(this.dataSoal.length / this.jumlahTotalSoal);
      this.updateQuestionsToShow();
    }
  }

  @Input() dataSoal: Questions[] = [];
  @Input() jumlahTotalSoal: number = 10;
  @Input() answers: {
    id: number;
    value: number;
    answer: string;
    category: string;
  }[] = [];
  @Output() answersChange = new EventEmitter<any[]>();

  currentStep: number = 0;
  totalStep: number = 0;
  questionsToShow: Questions[] = [];

  ngOnInit(): void {
    this.totalStep = Math.ceil(this.dataSoal.length / this.jumlahTotalSoal);
    this.updateQuestionsToShow();
  }

  updateQuestionsToShow() {
    const start = this.currentStep * this.jumlahTotalSoal;
    const end = start + this.jumlahTotalSoal;
    this.questionsToShow = this.dataSoal.slice(start, end);
  }

  selectAnswer(
    questionId: number,
    event: { value: number; answer: string; category: string }
  ) {
    //
    //
    //
    //  // Pastikan ini tidak undefined

    const existingAnswer = this.answers.find((a) => a.id === questionId);
    if (existingAnswer) {
      existingAnswer.value = event.value;
      existingAnswer.answer = event.answer;
      existingAnswer.category = event.category;
    } else {
      this.answers.push({
        id: questionId,
        value: event.value,
        answer: event.answer,
        category: event.category,
      });
    }
    // this.answersChange.emit(this.answers);
  }

  next() {
    if (this.currentStep < this.totalStep - 1) {
      this.currentStep++;
      this.updateQuestionsToShow();

      // Scroll ke atas halaman dengan animasi yang halus
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }

  previous() {
    if (this.currentStep > 0) {
      this.currentStep--;
      this.updateQuestionsToShow();
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }

  getSelectedValue(questionId: number): number | undefined {
    return this.answers.find((answer) => answer.id === questionId)?.value;
  }

  isFormComplete(): boolean {
    // Pastikan setiap pertanyaan memiliki jawaban yang valid
    return this.answers.length === this.dataSoal.length;
  }

  submitAnswers() {
    // Kirimkan jawaban ke komponen induk
    this.answersChange.emit(this.answers); // Emit jawaban
    //
    // Jika ingin memproses jawaban, seperti menyimpannya ke server, lakukan di sini
  }
}
