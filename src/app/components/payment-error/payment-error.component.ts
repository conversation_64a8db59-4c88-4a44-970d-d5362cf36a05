import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TestPackPurchaseService } from 'src/app/service/test-pack-purchase.service';

@Component({
  selector: 'app-payment-error',
  templateUrl: './payment-error.component.html',
  styleUrls: ['./payment-error.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class PaymentErrorComponent implements OnInit {
  errorMessage: string = 'Te<PERSON><PERSON><PERSON> kesalahan saat memproses pembayaran Anda.';
  testPackName: string = '';

  constructor(
    private router: Router,
    private testPackPurchaseService: TestPackPurchaseService
  ) {}

  ngOnInit(): void {
    // Get selected test pack name if available
    const selectedTestPack = this.testPackPurchaseService.getSelectedTestPack();
    if (selectedTestPack) {
      this.testPackName = selectedTestPack.name;
    }
  }

  tryAgain(): void {
    this.router.navigate(['/payment']);
  }

  goHome(): void {
    // Clear any stored payment data
    this.testPackPurchaseService.clearPurchaseData();
    this.router.navigate(['/']);
  }
}
