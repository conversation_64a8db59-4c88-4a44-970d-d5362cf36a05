<div class="min-h-screen bg-gray-100 flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
  <div class="max-w-md w-full bg-white rounded-lg shadow-lg overflow-hidden">
    <div class="p-8">
      <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
          <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-800 mb-2">Pembayaran Gagal</h2>
        <p class="text-gray-600 mb-4">
          {{ errorMessage }}
        </p>
        
        <div *ngIf="testPackName" class="bg-gray-50 p-4 rounded-lg mb-6 text-left">
          <p class="text-gray-700 mb-2">Detail pesanan:</p>
          <p class="font-medium">{{ testPackName }}</p>
        </div>
        
        <div class="space-y-3">
          <button 
            (click)="tryAgain()" 
            class="w-full bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 transition-colors font-semibold">
            Coba Lagi
          </button>
          <button 
            (click)="goHome()" 
            class="w-full bg-gray-200 text-gray-700 py-2 rounded-lg hover:bg-gray-300 transition-colors">
            Kembali ke Beranda
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
