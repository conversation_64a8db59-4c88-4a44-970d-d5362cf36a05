/* Modern CTA Section Component */

.cta-container {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-700) 50%, var(--color-secondary) 100%);
  padding: var(--spacing-20) var(--spacing-4);
  color: var(--color-white);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.cta-container::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  background-size: 50px 50px;
  animation: float 20s linear infinite;
  pointer-events: none;
}

@keyframes float {
  0% { transform: translateX(-50px) translateY(-50px); }
  100% { transform: translateX(0) translateY(0); }
}

.cta-content {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.cta-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-white);
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

.cta-description {
  font-size: var(--font-size-xl);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-8);
  line-height: var(--line-height-relaxed);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: var(--spacing-8);
}

.cta-button {
  background: var(--color-white);
  color: var(--color-primary);
  border: none;
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-xl);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.cta-button:hover::before {
  left: 100%;
}

.cta-button:hover {
  background: var(--color-gray-50);
  transform: translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

.cta-button:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .cta-container {
    padding: var(--spacing-16) var(--spacing-4);
  }

  .cta-title {
    font-size: var(--font-size-3xl);
  }

  .cta-description {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-6);
  }

  .cta-button {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
  }
}

@media (max-width: 640px) {
  .cta-container {
    padding: var(--spacing-12) var(--spacing-4);
  }

  .cta-title {
    font-size: var(--font-size-2xl);
  }
}
