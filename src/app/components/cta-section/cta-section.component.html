<div class="cta-container">
  <div class="cta-content">
    <h2 *ngIf="title" class="cta-title">
      {{ title }}
    </h2>
    <div
      *ngIf="description"
      class="cta-description"
      [innerHTML]="description"
    ></div>
    <button
      *ngIf="buttonText && buttonUrl"
      (click)="onButtonClick()"
      class="cta-button"
    >
      {{ buttonText }}
      <svg
        class="cta-icon"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M17 8l4 4m0 0l-4 4m4-4H3"
        />
      </svg>
    </button>
  </div>
</div>
