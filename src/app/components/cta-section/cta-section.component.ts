import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-cta-section',
  templateUrl: './cta-section.component.html',
  styleUrls: ['./cta-section.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class CtaSectionComponent {
  @Input() title!: string;
  @Input() description!: string;
  @Input() buttonText!: string;
  @Input() buttonUrl!: string;

  constructor(private router: Router) {}

  onButtonClick() {
    if (this.buttonUrl.startsWith('#')) {
      // Special handling for test-packages section - redirect to dedicated page
      if (this.buttonUrl === '#test-packages') {
        this.router.navigate(['/test-packages']);
        return;
      }
      // Scroll to section for other hash links
      const element = document.querySelector(this.buttonUrl);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else if (this.buttonUrl.startsWith('http')) {
      // External URL
      window.open(this.buttonUrl, '_blank');
    } else {
      // Internal navigation
      this.router.navigate([this.buttonUrl]);
    }
  }
}
