<div class="report-container">
  <h1>Input Data</h1>
  <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
    <!-- Category Column -->
    <ng-container matColumnDef="category">
      <th mat-header-cell *matHeaderCellDef>Category</th>
      <td mat-cell *matCellDef="let element">{{ element.category }}</td>
    </ng-container>

    <!-- Most Column -->
    <ng-container matColumnDef="most">
      <th mat-header-cell *matHeaderCellDef>Most</th>
      <td mat-cell *matCellDef="let element">
        <input
          type="number"
          [(ngModel)]="element.most"
          placeholder="Most"
          class="border p-2"
        />
      </td>
    </ng-container>

    <!-- Least Column -->
    <ng-container matColumnDef="least">
      <th mat-header-cell *matHeaderCellDef>Least</th>
      <td mat-cell *matCellDef="let element">
        <input
          type="number"
          [(ngModel)]="element.least"
          placeholder="Least"
          class="border p-2"
        />
      </td>
    </ng-container>

    <!-- Difference Column -->
    <ng-container matColumnDef="difference">
      <th mat-header-cell *matHeaderCellDef>Difference</th>
      <td mat-cell *matCellDef="let element">{{ element.difference }}</td>
    </ng-container>

    <!-- Header and Row Declarations -->
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>

  <div class="flex flex-row-reverse items-end mr-6 mb-4">
    <button
      (click)="calculateDifference()"
      class="bg-blue-500 text-white p-2 mt-4"
    >
      Hitung Difference
    </button>
  </div>
</div>

<div class="report-container">
  <h2>DISC Detail Report</h2>

  <div class="flex justify-around mt-8">
    <div class="w-1/3 h-full">
      <app-disc-chart
        [chartData]="mostChartData"
        [chartOptions]="mostChartOptions"
        [chartType]="chartType"
      ></app-disc-chart>
    </div>
    <div class="w-1/3 h-full">
      <app-disc-chart
        [chartData]="leastChartData"
        [chartOptions]="leastChartOptions"
        [chartType]="chartType"
      ></app-disc-chart>
    </div>
    <div class="w-1/3 h-full">
      <app-disc-chart
        [chartData]="differenceChartData"
        [chartOptions]="differenceChartOptions"
        [chartType]="chartType"
      ></app-disc-chart>
    </div>
  </div>
</div>
