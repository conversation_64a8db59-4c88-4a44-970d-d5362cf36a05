import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ChartData, ChartOptions, ChartType } from 'chart.js';
import { DiscResultService } from 'src/app/service/disc-result.service';
import { ReportService } from 'src/app/service/report.service';

@Component({
  selector: 'app-disc-input',
  templateUrl: './disc-input.component.html',
  styleUrls: ['./disc-input.component.css'],
})
export class DiscInputComponent {
  displayedColumns: string[] = ['category', 'most', 'least', 'difference'];
  dataSource: {
    category: string;
    most: number | null;
    least: number | null;
    difference: number;
  }[] = [];

  public labels: string[] = ['D', 'I', 'S', 'C'];

  public mostChartData: ChartData<'line'> = {
    labels: ['D', 'I', 'S', 'C'],
    datasets: [
      {
        data: [0, 0, 0, 0], // Nilai default
        label: 'MOST',
        fill: false,
        borderColor: '#42A5F5',
        tension: 0.1,
      },
    ],
  };

  public leastChartData: ChartData<'line'> = {
    labels: ['D', 'I', 'S', 'C'],
    datasets: [
      {
        data: [0, 0, 0, 0], // Nilai default
        label: 'LEAST',
        fill: false,
        borderColor: '#FFA726',
        tension: 0.1,
      },
    ],
  };

  public differenceChartData: ChartData<'line'> = {
    labels: ['D', 'I', 'S', 'C'],
    datasets: [
      {
        data: [0, 0, 0, 0], // Nilai default
        label: 'DIFFERENCE',
        fill: false,
        borderColor: '#66BB6A',
        tension: 0.1,
      },
    ],
  };

  public mostChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        min: 0,
        max: 20,
        ticks: {
          stepSize: 2,
        },
      },
    },
  };

  public leastChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    },
    scales: {
      y: {
        reverse: true,
        min: 0,
        max: 20,
        ticks: {
          stepSize: 2,
        },
      },
    },
  };

  public differenceChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    },
    scales: {
      y: {
        min: -20,
        max: 20,
        ticks: {
          stepSize: 2,
        },
      },
    },
  };

  public chartType: ChartType = 'line';
  public chartLegend = true;

  constructor(
    private reportService: ReportService,
    private route: ActivatedRoute,
    private discResult: DiscResultService
  ) {}

  ngOnInit(): void {
    // Initialize data source

    this.dataSource = [
      { category: 'D', most: null, least: null, difference: 0 },
      { category: 'I', most: null, least: null, difference: 0 },
      { category: 'S', most: null, least: null, difference: 0 },
      { category: 'C', most: null, least: null, difference: 0 },
    ];
  }

  // Function to calculate difference and update charts
  calculateDifference() {
    this.dataSource.forEach((item) => {
      if (item.most !== null && item.least !== null) {
        item.difference = item.most - item.least;
      } else {
        item.difference = 0; // Reset difference if data is incomplete
      }
    });

    this.updateChartData();
  }

  // Function to update chart data
  private updateChartData() {
    const mostCounts = this.dataSource.map((item) => item.most ?? 0);
    const leastCounts = this.dataSource.map((item) => item.least ?? 0);
    const differenceCounts = this.dataSource.map((item) => item.difference);

    // Normalization criteria
    const normalizedCriteriaMost = {
      D: this.discResult.normalizeValue(7, 0, 20, 28),
      I: this.discResult.normalizeValue(5, 0, 17, 28),
      S: this.discResult.normalizeValue(5, 0, 19, 28),
      C: this.discResult.normalizeValue(4, 0, 15, 28),
    };

    const normalizedCriteriaLeast = {
      D: this.discResult.normalizeValue(4, 0, 21, 28),
      I: this.discResult.normalizeValue(3, 0, 10, 28),
      S: this.discResult.normalizeValue(5, 0, 19, 27),
      C: this.discResult.normalizeValue(6, 0, 18, 27),
    };

    const normalizedMostCounts = {
      D: this.discResult.normalizeValue(mostCounts[0], 0, 20, 28),
      I: this.discResult.normalizeValue(mostCounts[1], 0, 17, 28),
      S: this.discResult.normalizeValue(mostCounts[2], 0, 19, 28),
      C: this.discResult.normalizeValue(mostCounts[3], 0, 15, 28),
    };

    const normalizedLeastCounts = {
      D: this.discResult.normalizeValue(leastCounts[0], 0, 21, 28),
      I: this.discResult.normalizeValue(leastCounts[1], 0, 10, 28),
      S: this.discResult.normalizeValue(leastCounts[2], 0, 19, 27),
      C: this.discResult.normalizeValue(leastCounts[3], 0, 18, 27),
    };

    // Generating DISC string
    const mostString = this.discResult.generateDISCString(
      normalizedMostCounts,
      normalizedCriteriaMost
    );
    const leastString = this.discResult.generateDISCString(
      normalizedLeastCounts,
      normalizedCriteriaLeast,
      true
    );
    const differenceString = this.discResult.generateDISCString(
      {
        D: differenceCounts[0],
        I: differenceCounts[1],
        S: differenceCounts[2],
        C: differenceCounts[3],
      },
      { D: 1, I: 0, S: -1, C: -2 } // This assumes the difference string generation takes the same structure
    );

    this.mostChartData = {
      labels: this.labels,
      datasets: [
        {
          data: Object.values(normalizedMostCounts),
          label: 'MOST',
          fill: false,
          borderColor: '#42A5F5',
          tension: 0.1,
        },
      ],
    };

    this.leastChartData = {
      labels: this.labels,
      datasets: [
        {
          data: Object.values(normalizedLeastCounts),
          label: 'LEAST',
          fill: false,
          borderColor: '#FFA726',
          tension: 0.1,
        },
      ],
    };

    this.differenceChartData = {
      labels: this.labels,
      datasets: [
        {
          data: Object.values(differenceCounts),
          label: 'DIFFERENCE',
          fill: false,
          borderColor: '#66BB6A',
          tension: 0.1,
        },
      ],
    };
  }
}
