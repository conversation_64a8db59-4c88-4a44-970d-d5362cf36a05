import { ComponentFixture, TestBed } from '@angular/core/testing';

import { QuestionaireWithRadioComponent } from './questionaire-with-radio.component';

describe('QuestionaireWithRadioComponent', () => {
  let component: QuestionaireWithRadioComponent;
  let fixture: ComponentFixture<QuestionaireWithRadioComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ QuestionaireWithRadioComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(QuestionaireWithRadioComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
