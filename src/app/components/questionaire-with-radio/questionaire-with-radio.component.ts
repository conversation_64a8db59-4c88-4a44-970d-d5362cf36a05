// questionaire-with-radio.component.ts
import { Component, EventEmitter, Input, Output } from '@angular/core';

interface Radio {
  text: string;
  value: number;
  category: string;
}

@Component({
  selector: 'app-questionaire-with-radio',
  templateUrl: './questionaire-with-radio.component.html',
  styleUrls: ['./questionaire-with-radio.component.css'],
})
export class QuestionaireWithRadioComponent {
  @Input() textPertanyaan!: string;
  @Input() jawaban!: Radio[];
  @Input() selectedValue: number | undefined;
  @Input() category!: string;
  @Output() onAnswerSelected = new EventEmitter<{
    value: number;
    answer: string;
    category: string;
  }>();

  selectAnswer(answer: string, value: number) {
    this.onAnswerSelected.emit({ answer, value, category: this.category });
  }
}
