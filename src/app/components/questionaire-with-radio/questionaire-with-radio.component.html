<!-- questionaire-with-radio.component.html -->
<div class="max-w-3xl mx-auto my-6">
  <div
    class="p-6 bg-white rounded-xl shadow-md border border-gray-200 hover:shadow-lg transition-shadow duration-300"
  >
    <!-- Bagian Pertanyaan -->
    <div class="mb-4">
      <h2 class="text-xl font-semibold text-gray-800 leading-tight">
        {{ textPertanyaan }}
      </h2>
    </div>

    <!-- Bagian Jawaban -->
    <div class="space-y-3 mt-4">
      <div
        *ngFor="let jawaban of jawaban"
        class="transform transition-transform duration-200 hover:translate-x-2"
      >
        <label
          class="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer"
        >
          <input
            type="radio"
            name="question{{ textPertanyaan }}"
            [value]="jawaban.value"
            [checked]="jawaban.value === selectedValue"
            (change)="selectAnswer(jawaban.text, jawaban.value)"
            class="form-radio h-5 w-5 text-blue-600 focus:ring-blue-500"
          />
          <span class="ml-3 text-gray-700 text-lg">{{ jawaban.text }}</span>
        </label>
      </div>
    </div>
  </div>
</div>
