import { Component, OnInit, inject } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TestPackPurchaseService } from 'src/app/service/test-pack-purchase.service';
import { AuthService } from 'src/app/service/auth.service';

interface TestPackOption {
  id: number;
  name: string;
  description: string;
  price: number;
  tests: string[];
  imageUrl?: string;
}

@Component({
  selector: 'app-member-test-pack-selection',
  templateUrl: './member-test-pack-selection.component.html',
  styleUrls: ['./member-test-pack-selection.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
  ],
})
export class MemberTestPackSelectionComponent implements OnInit {
  testPackOptions: TestPackOption[] = [];
  selectedTestPack: TestPackOption | null = null;
  isLoading: boolean = true;
  error: string | null = null;

  private testPackPurchaseService = inject(TestPackPurchaseService);
  private router = inject(Router);
  private authService = inject(AuthService);

  ngOnInit(): void {
    this.loadTestPackOptions();
  }

  loadTestPackOptions(): void {
    this.isLoading = true;
    this.testPackPurchaseService.getAvailableTestPacks().subscribe({
      next: (testPacks) => {
        this.testPackOptions = testPacks;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error loading test packs:', err);
        this.error =
          'Gagal memuat paket tes yang tersedia. Silakan coba lagi nanti.';
        this.isLoading = false;
      },
    });
  }

  selectTestPack(testPack: TestPackOption): void {
    this.selectedTestPack = testPack;

    // Store the selected test pack in the service for later use
    this.testPackPurchaseService.setSelectedTestPack(testPack);

    // Since user is already logged in (this is member area), proceed directly to payment
    this.router.navigate(['/payment']);
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  }

  goBack(): void {
    this.router.navigate(['/home']);
  }
}
