.test-pack-card {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.test-pack-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.test-pack-card.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.purchase-btn {
  transition: all 0.3s ease;
}

.purchase-btn.selected {
  background-color: #10b981 !important;
  color: white !important;
}

.purchase-btn:hover {
  transform: scale(1.05);
}

/* Custom styling for Material components */
::ng-deep .mat-card {
  border-radius: 12px !important;
  overflow: hidden;
}

::ng-deep .mat-raised-button {
  border-radius: 8px !important;
  font-weight: 600 !important;
  text-transform: none !important;
}

::ng-deep .mat-icon {
  vertical-align: middle;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .test-pack-card {
    margin-bottom: 1rem;
  }
  
  .purchase-btn {
    width: 100%;
    margin-top: 1rem;
  }
}
