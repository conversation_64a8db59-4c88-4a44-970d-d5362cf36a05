<div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
  <!-- Header Section -->
  <div class="text-center mb-12">
    <h1 class="text-4xl font-bold text-gray-900 mb-4">
      Pilih Test Pack Baru
    </h1>
    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
      Pilih paket tes yang sesuai dengan kebutuhan Anda untuk mengembangkan
      potensi diri dan karir profesional.
    </p>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-12">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <!-- Error State -->
  <div
    *ngIf="error"
    class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8"
  >
    <div class="flex items-center">
      <mat-icon class="text-red-500 mr-3">error</mat-icon>
      <p class="text-red-700">{{ error }}</p>
    </div>
    <button
      mat-raised-button
      color="primary"
      (click)="loadTestPackOptions()"
      class="mt-4"
    >
      <mat-icon>refresh</mat-icon>
      Coba Lagi
    </button>
  </div>

  <!-- Test Pack Options -->
  <div
    *ngIf="!isLoading && !error && testPackOptions.length > 0"
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
  >
    <mat-card
      *ngFor="let testPack of testPackOptions"
      class="test-pack-card hover:shadow-xl transition-all duration-300 cursor-pointer"
      [class.selected]="selectedTestPack?.id === testPack.id"
      (click)="selectTestPack(testPack)"
    >
      <!-- Image -->
      <div class="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600">
        <img
          *ngIf="testPack.imageUrl"
          [src]="testPack.imageUrl"
          [alt]="testPack.name"
          class="w-full h-full object-cover"
        />
        <div
          class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center"
        >
          <h3 class="text-white text-2xl font-bold text-center px-4">
            {{ testPack.name }}
          </h3>
        </div>
      </div>

      <mat-card-content class="p-6">
        <!-- Description -->
        <p class="text-gray-600 mb-4 leading-relaxed">
          {{ testPack.description }}
        </p>

        <!-- Tests Included -->
        <div class="mb-6">
          <h4 class="font-semibold text-gray-800 mb-3">Tes yang Termasuk:</h4>
          <div class="flex flex-wrap gap-2">
            <span
              *ngFor="let test of testPack.tests"
              class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
            >
              <mat-icon class="w-4 h-4 mr-1" style="font-size: 16px">
                psychology
              </mat-icon>
              {{ test }}
            </span>
          </div>
        </div>

        <!-- Price and Action -->
        <div class="flex justify-between items-center">
          <div class="flex flex-col">
            <span class="text-sm text-gray-500">Harga</span>
            <span class="text-2xl font-bold text-blue-600">
              {{ formatPrice(testPack.price) }}
            </span>
          </div>
          <button
            mat-raised-button
            color="primary"
            class="purchase-btn"
            [class.selected]="selectedTestPack?.id === testPack.id"
          >
            <mat-icon>shopping_cart</mat-icon>
            <span *ngIf="selectedTestPack?.id !== testPack.id">
              Beli Sekarang
            </span>
            <span *ngIf="selectedTestPack?.id === testPack.id">
              ✓ Dipilih
            </span>
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Empty State -->
  <div
    *ngIf="!isLoading && !error && testPackOptions.length === 0"
    class="text-center py-12"
  >
    <mat-icon class="text-gray-400 text-6xl mb-4">inventory_2</mat-icon>
    <h3 class="text-xl font-semibold text-gray-600 mb-2">
      Tidak Ada Test Pack Tersedia
    </h3>
    <p class="text-gray-500 mb-6">
      Saat ini tidak ada paket tes yang tersedia. Silakan coba lagi nanti.
    </p>
    <button mat-raised-button color="primary" (click)="loadTestPackOptions()">
      <mat-icon>refresh</mat-icon>
      Muat Ulang
    </button>
  </div>

  <!-- Back Button -->
  <div class="text-center mt-12">
    <button mat-stroked-button (click)="goBack()" class="mr-4">
      <mat-icon>arrow_back</mat-icon>
      Kembali ke Dashboard
    </button>
  </div>
</div>
