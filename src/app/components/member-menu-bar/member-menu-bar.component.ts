import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/service/auth.service';

@Component({
  selector: 'app-member-menu-bar',
  templateUrl: './member-menu-bar.component.html',
  styleUrls: ['./member-menu-bar.component.css'],
})
export class MemberMenuBarComponent implements OnInit {
  // Menu states
  isMobileMenuOpen = false;
  activeMenu: string | null = null;
  activeMenuMobile: string | null = null;

  constructor(private router: Router, private authService: AuthService) {}

  ngOnInit(): void {
    // No longer need to load test access since we removed the assessment menus
  }

  // Handle navigation
  navigateTo(route: string) {
    this.router.navigate([route]);
    // Tutup menu mobile jika sedang terbuka
    this.isMobileMenuOpen = false;
    // Reset semua menu dropdown
    this.activeMenu = null;
    this.activeMenuMobile = null;
  }

  // Toggle untuk desktop dan mobile menu
  toggleMenu(menuName: string, isMobile = false) {
    // Jika mengklik menu yang sudah aktif, tutup menu tersebut
    if (
      (isMobile && this.activeMenuMobile === menuName) ||
      (!isMobile && this.activeMenu === menuName)
    ) {
      if (isMobile) {
        this.activeMenuMobile = null;
      } else {
        this.activeMenu = null;
      }
      return;
    }

    // Jika mengklik menu baru, tutup menu yang lama dan buka yang baru
    if (isMobile) {
      this.activeMenuMobile = menuName;
    } else {
      this.activeMenu = menuName;
    }
  }

  // Toggle khusus untuk burger menu
  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
    // Optional: tutup submenu yang terbuka saat menutup burger menu
    if (!this.isMobileMenuOpen) {
      this.activeMenuMobile = null;
    }
  }

  // Helper method untuk mengecek status menu
  isMenuOpen(menuName: string, isMobile = false): boolean {
    return isMobile
      ? this.activeMenuMobile === menuName
      : this.activeMenu === menuName;
  }

  // Close all menus
  private closeAllMenus() {
    this.isMobileMenuOpen = false;
    this.activeMenuMobile = null;
  }

  // Method untuk cek apakah user adalah admin
  isAdmin(): boolean {
    const isAdmin = this.authService.isAdmin();
    return isAdmin;
  }

  // Method debugRole() telah dihapus

  // Pastikan menu admin ditambahkan ke openMenus
  openMenus: { [key: string]: boolean } = {
    disc: false,
    teams: false,
    values: false,
    mbti: false,
    admin: false, // Tambahkan menu admin
  };

  mobileOpenMenus: { [key: string]: boolean } = {
    disc: false,
    teams: false,
    values: false,
    mbti: false,
    admin: false, // Tambahkan menu admin untuk mobile
  };
}
