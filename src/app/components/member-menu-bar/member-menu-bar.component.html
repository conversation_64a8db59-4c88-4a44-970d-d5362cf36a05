<nav class="bg-blue-900">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-20">
    <div class="flex justify-between h-16 items-center">
      <!-- Logo -->
      <div class="flex items-center">
        <img
          class="h-20 w-auto mt-5 mb-5"
          src="../../../assets/logo-maxima.png"
          alt="Maxima Potential"
        />
      </div>

      <!-- Desktop menu - Centered -->
      <div class="hidden sm:flex sm:space-x-4 flex-1 justify-center">
        <a
          (click)="navigateTo('home')"
          class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium cursor-pointer"
        >
          Home
        </a>

        <!-- Hasil Test Saya - For all users -->
        <a
          (click)="navigateTo('home/test-results')"
          class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium cursor-pointer"
        >
          Hasil Test Saya
        </a>

        <!-- Beli Test Pack - For all users -->
        <a
          (click)="navigateTo('home/buy-test-pack')"
          class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium cursor-pointer"
        >
          Beli Test Pack
        </a>

        <a
          (click)="navigateTo('home/payment-history')"
          class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium cursor-pointer"
        >
          Riwayat Pembayaran
        </a>

        <!-- ADMIN Dropdown - Only for admin users -->
        <div class="relative" *ngIf="isAdmin()">
          <button
            (click)="toggleMenu('admin')"
            class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium flex items-center"
          >
            ADMIN
            <svg
              class="w-4 h-4 ml-1"
              [class.rotate-180]="isMenuOpen('admin')"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          <div
            *ngIf="isMenuOpen('admin')"
            class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10"
          >
            <a
              (click)="navigateTo('home/admin/manage-access')"
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
              >Manage Test Access</a
            >
            <a
              (click)="navigateTo('home/admin/test-pack-results')"
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
              >Test Pack Results</a
            >
            <a
              (click)="navigateTo('home/admin/payment-management')"
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
              >Payment Management</a
            >
          </div>
        </div>
      </div>

      <!-- Logout button for desktop -->
      <div class="hidden sm:block">
        <app-logout></app-logout>
      </div>

      <!-- Burger menu button - Explicitly visible on mobile -->
      <div class="block sm:hidden">
        <button
          (click)="toggleMobileMenu()"
          class="text-gray-400 hover:text-white hover:bg-gray-700 p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
        >
          <span class="sr-only">Open main menu</span>
          <!-- Hamburger icon -->
          <svg
            *ngIf="!isMobileMenuOpen"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16m-7 6h7"
            />
          </svg>
          <!-- X icon -->
          <svg
            *ngIf="isMobileMenuOpen"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile menu - Explicitly controlled by isMobileMenuOpen -->
  <div
    [ngClass]="{ hidden: !isMobileMenuOpen, block: isMobileMenuOpen }"
    class="sm:hidden"
  >
    <div class="px-2 pt-2 pb-3 space-y-1">
      <a
        (click)="navigateTo('home')"
        class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium cursor-pointer"
      >
        Home
      </a>

      <!-- Hasil Test Saya - For all users -->
      <a
        (click)="navigateTo('home/test-results')"
        class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium cursor-pointer"
      >
        Hasil Test Saya
      </a>

      <!-- Beli Test Pack - For all users -->
      <a
        (click)="navigateTo('home/buy-test-pack')"
        class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium cursor-pointer"
      >
        Beli Test Pack
      </a>

      <a
        (click)="navigateTo('home/payment-history')"
        class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium cursor-pointer"
      >
        Riwayat Pembayaran
      </a>

      <!-- Mobile Admin Menu - Only for admin users -->
      <div *ngIf="isAdmin()">
        <button
          (click)="toggleMenu('admin', true)"
          class="text-gray-300 hover:bg-gray-700 hover:text-white w-full px-3 py-2 rounded-md text-base font-medium flex justify-between items-center"
        >
          ADMIN
          <svg
            class="w-4 h-4"
            [class.rotate-180]="isMenuOpen('admin', true)"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>

        <div *ngIf="isMenuOpen('admin', true)" class="pl-4">
          <a
            (click)="navigateTo('home/admin/manage-access')"
            class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
          >
            Manage Test Access
          </a>
          <a
            (click)="navigateTo('home/admin/test-pack-results')"
            class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
          >
            Test Pack Results
          </a>
          <a
            (click)="navigateTo('home/admin/payment-management')"
            class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium"
          >
            Payment Management
          </a>
        </div>
      </div>

      <!-- Logout button for mobile -->
      <div class="mt-4">
        <app-logout></app-logout>
      </div>
    </div>
  </div>
</nav>
