import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Answer } from 'src/app/interfaces/answer';
import { QuestionGroupService } from 'src/app/service/question-group.service';

@Component({
  selector: 'app-disc-questionaire',
  templateUrl: './disc-questionaire.component.html',
  styleUrls: ['./disc-questionaire.component.css'],
})
export class DiscQuestionaireComponent implements OnInit {
  @Output() formSubmitted: EventEmitter<any[]> = new EventEmitter();
  answers: any[] = [];
  isFormValid: boolean = false;
  @Input() questionGroups: any[] = [];

  constructor(private data: QuestionGroupService) {
    this.checkFormValidity(); // Mengecek validitas pada inisialisasi
  }

  ngOnInit(): void {}

  // Fungsi untuk menangani perubahan radio button
  handleSelection(group: any, type: any, questionId: any) {
    if (type === 'most') {
      group.selectedMost = questionId;
    } else if (type === 'least') {
      group.selectedLeast = questionId;
    }

    // Validasi apakah user memilih Most dan Least dalam satu row
    if (group.selectedMost === group.selectedLeast) {
      group.warning =
        'Tidak dapat memilih Most dan Least pada pertanyaan yang sama!';
      if (type === 'most') group.selectedMost = null; // Reset pilihan Most
      else group.selectedLeast = null; // Reset pilihan Least
    } else {
      group.warning = ''; // Tidak ada konflik
    }

    this.checkFormValidity(); // Cek validitas form setelah setiap perubahan
  }

  // Fungsi untuk mengecek apakah semua pertanyaan telah dijawab
  checkFormValidity() {
    this.isFormValid = this.questionGroups.every(
      (group) =>
        group.selectedMost !== null &&
        group.selectedLeast !== null &&
        group.warning === ''
    );
  }

  // Fungsi untuk menyimpan jawaban
  submitAnswers() {
    if (!this.isFormValid) return; // Mencegah submit jika form tidak valid

    this.answers = [];
    for (let group of this.questionGroups) {
      const mostQuestion = group.questions.find(
        (q: any) => q.id === group.selectedMost
      );
      const leastQuestion = group.questions.find(
        (q: any) => q.id === group.selectedLeast
      );

      if (mostQuestion && leastQuestion) {
        // Validasi nilai mostValue dan leastValue
        const mostValue = mostQuestion.mostValue || '';
        const leastValue = leastQuestion.leastValue || '';

        // Tambahkan semua jawaban, termasuk yang memiliki nilai V
        this.answers.push({
          mostId: mostQuestion.id,
          leastId: leastQuestion.id,
          mostQuestionText: mostQuestion.text,
          leastQuestionText: leastQuestion.text,
          mostAnswer: mostValue,
          leastAnswer: leastValue,
        });

        // Log peringatan hanya untuk tujuan debugging jika nilai bukan D, I, S, C, atau V
        if (
          !['D', 'I', 'S', 'C', 'V'].includes(mostValue) ||
          !['D', 'I', 'S', 'C', 'V'].includes(leastValue)
        ) {
          console.warn('Unexpected DISC value detected:', {
            mostValue,
            leastValue,
            mostId: mostQuestion.id,
            leastId: leastQuestion.id,
          });
        }
      }
    }

    this.formSubmitted.emit(this.answers);
  }
}
