<div class="max-w-4xl mx-auto p-6">
  <!-- Card Container -->
  <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
    <!-- Header untuk setiap kolom -->
    <div class="grid grid-cols-6 items-center mb-8 bg-gray-50 p-4 rounded-lg">
      <!-- Header untuk kolom Pertanyaan -->
      <p class="text-xl font-bold col-span-4 text-gray-800">Pertanyaan</p>

      <!-- Header untuk Most dan Least -->
      <div class="grid grid-cols-2 col-span-2">
        <p class="text-xl font-bold text-center text-blue-600">Most</p>
        <p class="text-xl font-bold text-center text-red-600">Least</p>
      </div>
    </div>

    <!-- Grup Pertanyaan -->
    <div
      *ngFor="let group of questionGroups"
      class="mb-6 p-6 border border-gray-200 rounded-xl bg-white shadow-sm hover:shadow-md transition-shadow duration-300"
    >
      <!-- Iterasi setiap pertanyaan dalam grup -->
      <div
        *ngFor="let question of group.questions"
        class="grid grid-cols-6 items-center py-4 border-b border-gray-100 last:border-0 hover:bg-gray-50 transition-colors duration-200"
      >
        <!-- Teks pertanyaan -->
        <p class="text-lg col-span-4 px-4 text-gray-700">{{ question.text }}</p>

        <!-- Most Radio Button -->
        <div class="flex justify-center items-center">
          <label class="relative cursor-pointer">
            <input
              type="radio"
              name="most-{{ group.id }}"
              [value]="question.mostValue"
              (change)="handleSelection(group, 'most', question.id)"
              [checked]="group.selectedMost === question.id"
              class="appearance-none w-6 h-6 border-2 border-blue-500 rounded-full checked:bg-blue-500 transition-all duration-200"
            />
            <span
              class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white opacity-0 pointer-events-none checked:opacity-100"
            >
              ●
            </span>
          </label>
        </div>

        <!-- Least Radio Button -->
        <div class="flex justify-center items-center">
          <label class="relative cursor-pointer">
            <input
              type="radio"
              name="least-{{ group.id }}"
              [value]="question.leastValue"
              (change)="handleSelection(group, 'least', question.id)"
              [checked]="group.selectedLeast === question.id"
              class="appearance-none w-6 h-6 border-2 border-red-500 rounded-full checked:bg-red-500 transition-all duration-200"
            />
            <span
              class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white opacity-0 pointer-events-none checked:opacity-100"
            >
              ●
            </span>
          </label>
        </div>
      </div>

      <!-- Warning message dengan animasi -->
      <div
        *ngIf="group.warning"
        class="mt-4 px-4 py-2 bg-red-50 border-l-4 border-red-500 text-red-700 text-sm rounded animate-pulse"
      >
        {{ group.warning }}
      </div>
    </div>
  </div>

  <!-- Tombol Submit -->
  <div class="flex justify-end">
    <button
      (click)="submitAnswers()"
      [disabled]="!isFormValid"
      class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed shadow-md hover:shadow-lg"
    >
      Submit Jawaban
    </button>
  </div>
</div>
