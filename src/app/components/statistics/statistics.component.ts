import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Statistic {
  number: string;
  label: string;
  description: string;
}

@Component({
  selector: 'app-statistics',
  templateUrl: './statistics.component.html',
  styleUrls: ['./statistics.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class StatisticsComponent {
  @Input() title!: string;
  @Input() subtitle?: string;
  @Input() stats!: Statistic[];
}
