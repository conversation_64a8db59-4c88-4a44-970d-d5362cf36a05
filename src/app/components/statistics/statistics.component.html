<div class="statistics-container">
  <div class="statistics-content">
    <div class="statistics-header">
      <h2 *ngIf="title" class="statistics-title">
        {{ title }}
      </h2>
      <p *ngIf="subtitle" class="statistics-subtitle">{{ subtitle }}</p>
    </div>

    <div class="statistics-grid">
      <div *ngFor="let stat of stats" class="stat-card">
        <div class="stat-number">
          {{ stat.number }}
        </div>
        <h3 class="stat-label">
          {{ stat.label }}
        </h3>
        <p class="stat-description">{{ stat.description }}</p>
      </div>
    </div>
  </div>
</div>
