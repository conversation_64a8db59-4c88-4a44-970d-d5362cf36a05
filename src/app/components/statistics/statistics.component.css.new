/* Clean Statistics Component */

.statistics-container {
  background: #f8fafc;
  padding: 4rem 2rem;
}

.statistics-content {
  max-width: 1200px;
  margin: 0 auto;
}

.statistics-header {
  text-align: center;
  margin-bottom: 3rem;
}

.statistics-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.statistics-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #6366f1;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #6366f1;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .statistics-container {
    padding: 3rem 1rem;
  }
  
  .statistics-title {
    font-size: 2rem;
  }
  
  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .statistics-grid {
    grid-template-columns: 1fr;
  }
}
