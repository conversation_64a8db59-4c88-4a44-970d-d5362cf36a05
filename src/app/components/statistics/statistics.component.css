/* Modern Statistics Component */

.statistics-container {
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 100%);
  padding: var(--spacing-20) var(--spacing-4);
  position: relative;
  overflow: hidden;
}

.statistics-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 80% 50%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.statistics-content {
  max-width: 1280px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.statistics-header {
  text-align: center;
  margin-bottom: var(--spacing-16);
}

.statistics-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
  letter-spacing: -0.025em;
}

.statistics-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
  max-width: 700px;
  margin: 0 auto;
  line-height: var(--line-height-relaxed);
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  margin-top: var(--spacing-16);
}

.stat-card {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  transform: scaleX(0);
  transition: transform var(--transition-base);
  transform-origin: left;
}

.stat-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-200);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-number {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-2);
  line-height: 1;
  display: block;
}

.stat-label {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

/* Enhanced Animations */
.stat-card {
  animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* Number Counter Animation */
.stat-number {
  animation: countUp 1.5s ease-out 0.5s both;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .statistics-container {
    padding: var(--spacing-16) var(--spacing-4);
  }

  .statistics-header {
    margin-bottom: var(--spacing-12);
  }

  .statistics-title {
    font-size: var(--font-size-3xl);
  }

  .statistics-subtitle {
    font-size: var(--font-size-lg);
  }

  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
    margin-top: var(--spacing-12);
  }

  .stat-card {
    padding: var(--spacing-6);
  }

  .stat-number {
    font-size: var(--font-size-4xl);
  }
}

@media (max-width: 640px) {
  .statistics-container {
    padding: var(--spacing-12) var(--spacing-4);
  }

  .statistics-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .stat-number {
    font-size: var(--font-size-3xl);
  }
}
