<div class="container mx-auto p-4">
  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="flex justify-center my-8">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Error Message -->
  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
  >
    {{ error }}
    <div class="mt-2">
      <button mat-button color="primary" (click)="goBack()">
        <mat-icon>arrow_back</mat-icon>
        Kembali ke Riwayat Pembayaran
      </button>
    </div>
  </div>

  <!-- Payment Detail -->
  <div *ngIf="!isLoading && !error && payment" class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h1 class="text-2xl font-bold text-gray-800">Detail Pembayaran</h1>
        <button mat-button color="primary" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
          Kembali
        </button>
      </div>

      <div class="flex items-center space-x-4">
        <mat-chip [ngClass]="getStatusClass(payment.attributes.status)">
          {{ getStatusText(payment.attributes.status) }}
        </mat-chip>
        <span class="text-gray-600"
          >ID: {{ payment.attributes.externalId }}</span
        >
      </div>
    </div>

    <!-- Payment Information -->
    <mat-card>
      <mat-card-header>
        <mat-card-title>Informasi Pembayaran</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <mat-list>
          <mat-list-item>
            <mat-icon matListItemIcon>receipt</mat-icon>
            <div matListItemTitle>ID Pembayaran</div>
            <div matListItemLine class="font-mono">
              {{ payment.attributes.externalId }}
            </div>
          </mat-list-item>

          <mat-divider></mat-divider>

          <mat-list-item>
            <mat-icon matListItemIcon>description</mat-icon>
            <div matListItemTitle>Deskripsi</div>
            <div matListItemLine>{{ payment.attributes.description }}</div>
          </mat-list-item>

          <mat-divider></mat-divider>

          <mat-list-item>
            <mat-icon matListItemIcon>attach_money</mat-icon>
            <div matListItemTitle>Jumlah</div>
            <div matListItemLine class="font-semibold text-green-600">
              {{ formatAmount(payment.attributes.amount) }}
            </div>
          </mat-list-item>

          <mat-divider></mat-divider>

          <mat-list-item>
            <mat-icon matListItemIcon>schedule</mat-icon>
            <div matListItemTitle>Tanggal Dibuat</div>
            <div matListItemLine>
              {{ formatDate(payment.attributes.createdAt) }}
            </div>
          </mat-list-item>

          <mat-divider *ngIf="payment.attributes.updatedAt"></mat-divider>

          <mat-list-item *ngIf="payment.attributes.updatedAt">
            <mat-icon matListItemIcon>update</mat-icon>
            <div matListItemTitle>Terakhir Diperbarui</div>
            <div matListItemLine>
              {{ formatDate(payment.attributes.updatedAt) }}
            </div>
          </mat-list-item>

          <mat-divider *ngIf="payment.attributes.paidAt"></mat-divider>

          <mat-list-item *ngIf="payment.attributes.paidAt">
            <mat-icon matListItemIcon>check_circle</mat-icon>
            <div matListItemTitle>Tanggal Pembayaran</div>
            <div matListItemLine class="text-green-600">
              {{ formatDate(payment.attributes.paidAt) }}
            </div>
          </mat-list-item>

          <mat-divider *ngIf="payment.attributes.paymentMethod"></mat-divider>

          <mat-list-item *ngIf="payment.attributes.paymentMethod">
            <mat-icon matListItemIcon>payment</mat-icon>
            <div matListItemTitle>Metode Pembayaran</div>
            <div matListItemLine>{{ payment.attributes.paymentMethod }}</div>
          </mat-list-item>

          <mat-divider *ngIf="payment.attributes.paymentChannel"></mat-divider>

          <mat-list-item *ngIf="payment.attributes.paymentChannel">
            <mat-icon matListItemIcon>account_balance</mat-icon>
            <div matListItemTitle>Channel Pembayaran</div>
            <div matListItemLine>{{ payment.attributes.paymentChannel }}</div>
          </mat-list-item>

          <mat-divider *ngIf="payment.attributes.failureReason"></mat-divider>

          <mat-list-item *ngIf="payment.attributes.failureReason">
            <mat-icon matListItemIcon class="text-red-500">error</mat-icon>
            <div matListItemTitle>Alasan Gagal</div>
            <div matListItemLine class="text-red-600">
              {{ payment.attributes.failureReason }}
            </div>
          </mat-list-item>
        </mat-list>
      </mat-card-content>
    </mat-card>

    <!-- User Information (Admin Only) -->
    <mat-card *ngIf="isAdmin() && payment.attributes.users?.data">
      <mat-card-header>
        <mat-card-title>Informasi Pengguna</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div
          *ngFor="let user of payment.attributes.users?.data || []"
          class="mb-4 last:mb-0"
        >
          <mat-list>
            <mat-list-item>
              <mat-icon matListItemIcon>person</mat-icon>
              <div matListItemTitle>Username</div>
              <div matListItemLine>{{ user.attributes.username }}</div>
            </mat-list-item>

            <mat-divider></mat-divider>

            <mat-list-item>
              <mat-icon matListItemIcon>email</mat-icon>
              <div matListItemTitle>Email</div>
              <div matListItemLine>{{ user.attributes.email }}</div>
            </mat-list-item>

            <mat-divider></mat-divider>

            <mat-list-item>
              <mat-icon matListItemIcon>schedule</mat-icon>
              <div matListItemTitle>Tanggal Daftar</div>
              <div matListItemLine>
                {{ formatDate(user.attributes.createdAt) }}
              </div>
            </mat-list-item>
          </mat-list>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Test Pack Information -->
    <mat-card *ngIf="payment.attributes.testPack?.data">
      <mat-card-header>
        <mat-card-title>Informasi Test Pack</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <mat-list>
          <mat-list-item>
            <mat-icon matListItemIcon>assignment</mat-icon>
            <div matListItemTitle>ID Test Pack</div>
            <div matListItemLine class="font-mono">
              {{ payment.attributes.testPack?.data?.id }}
            </div>
          </mat-list-item>

          <mat-divider></mat-divider>

          <mat-list-item>
            <mat-icon matListItemIcon>label</mat-icon>
            <div matListItemTitle>Nama</div>
            <div matListItemLine>
              {{ payment.attributes.testPack?.data?.attributes?.name }}
            </div>
          </mat-list-item>

          <mat-divider></mat-divider>

          <mat-list-item>
            <mat-icon matListItemIcon>description</mat-icon>
            <div matListItemTitle>Deskripsi</div>
            <div matListItemLine>
              {{ payment.attributes.testPack?.data?.attributes?.description }}
            </div>
          </mat-list-item>

          <mat-divider></mat-divider>

          <mat-list-item>
            <mat-icon matListItemIcon>attach_money</mat-icon>
            <div matListItemTitle>Harga</div>
            <div matListItemLine class="font-semibold">
              {{ getTestPackPrice() }}
            </div>
          </mat-list-item>

          <mat-divider></mat-divider>

          <mat-list-item>
            <mat-icon matListItemIcon>info</mat-icon>
            <div matListItemTitle>Status Test Pack</div>
            <div matListItemLine>
              <mat-chip class="status-chip">
                {{ getTestPackStatus() }}
              </mat-chip>
            </div>
          </mat-list-item>

          <mat-divider></mat-divider>

          <mat-list-item>
            <mat-icon matListItemIcon>schedule</mat-icon>
            <div matListItemTitle>Tanggal Dibuat</div>
            <div matListItemLine>
              {{ getTestPackCreatedAt() }}
            </div>
          </mat-list-item>
        </mat-list>
      </mat-card-content>

      <mat-card-actions
        *ngIf="payment.attributes.status === 'PAID' && !isAdmin()"
      >
        <button mat-raised-button color="primary" routerLink="/home/<USER>">
          <mat-icon>play_arrow</mat-icon>
          Mulai Test
        </button>
      </mat-card-actions>
    </mat-card>

    <!-- Action Buttons -->
    <div class="flex space-x-4 justify-center mt-6">
      <button mat-raised-button (click)="goBack()">
        <mat-icon>arrow_back</mat-icon>
        Kembali ke Riwayat
      </button>

      <button
        *ngIf="payment.attributes.status === 'PAID'"
        mat-raised-button
        color="primary"
        disabled
      >
        <mat-icon>download</mat-icon>
        Unduh Invoice (Coming Soon)
      </button>
    </div>
  </div>
</div>
