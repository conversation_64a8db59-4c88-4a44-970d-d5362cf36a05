import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatListModule } from '@angular/material/list';
import { environment } from 'src/environments/environment.development';
import { AuthService } from 'src/app/service/auth.service';
import { catchError, finalize } from 'rxjs/operators';
import { of } from 'rxjs';

interface PaymentDetail {
  id: number;
  attributes: {
    externalId: string;
    amount: number;
    status: 'PENDING' | 'PAID' | 'FAILED' | 'EXPIRED';
    description: string;
    createdAt: string;
    updatedAt: string;
    paidAt?: string;
    paymentMethod?: string;
    paymentChannel?: string;
    failureReason?: string;
    testPack?: {
      data: {
        id: number;
        attributes: {
          name: string;
          description: string;
          price: number;
          status: string;
          createdAt: string;
        };
      };
    };
    users?: {
      data: Array<{
        id: number;
        attributes: {
          username: string;
          email: string;
          createdAt: string;
        };
      }>;
    };
  };
}

interface PaymentDetailResponse {
  data: PaymentDetail;
}

@Component({
  selector: 'app-payment-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatListModule,
  ],
  templateUrl: './payment-detail.component.html',
  styleUrls: ['./payment-detail.component.css'],
})
export class PaymentDetailComponent implements OnInit {
  payment: PaymentDetail | null = null;
  isLoading = true;
  error: string | null = null;
  paymentId: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private http: HttpClient,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.paymentId = params['id'];
      if (this.paymentId) {
        this.loadPaymentDetail();
      }
    });
  }

  loadPaymentDetail(): void {
    if (!this.paymentId) return;

    this.isLoading = true;
    this.error = null;

    const user = this.authService.getCurrentUser();
    if (!user || !user.id) {
      this.error = 'User not authenticated';
      this.isLoading = false;
      return;
    }

    const isAdmin = this.authService.isAdmin();

    // Build query with all related data
    const queryParams = `?populate=testPack,users`;

    this.http
      .get<PaymentDetailResponse>(
        `${environment.url}payments/${this.paymentId}${queryParams}`
      )
      .pipe(
        catchError((error) => {
          console.error('Error loading payment detail:', error);
          this.error = 'Failed to load payment details. Please try again.';
          return of(null);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((response) => {
        if (response) {
          this.payment = response.data;

          // Check access permissions
          if (!this.hasAccess()) {
            this.error = 'You do not have permission to view this payment';
            this.payment = null;
          }
        }
      });
  }

  hasAccess(): boolean {
    if (!this.payment) return false;

    const isAdmin = this.authService.isAdmin();
    if (isAdmin) return true;

    const user = this.authService.getCurrentUser();
    if (!user) return false;

    // Check if the payment belongs to the current user
    const paymentUsers = this.payment.attributes.users?.data || [];
    return paymentUsers.some((u) => u.id === user.id);
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PAID':
        return 'status-paid';
      case 'PENDING':
        return 'status-pending';
      case 'FAILED':
        return 'status-failed';
      case 'EXPIRED':
        return 'status-expired';
      default:
        return '';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'PAID':
        return 'Pembayaran Berhasil';
      case 'PENDING':
        return 'Menunggu Pembayaran';
      case 'FAILED':
        return 'Pembayaran Gagal';
      case 'EXPIRED':
        return 'Pembayaran Kedaluwarsa';
      default:
        return status;
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  formatAmount(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  getPaymentUser(): string {
    if (
      this.payment?.attributes.users?.data &&
      this.payment.attributes.users.data.length > 0
    ) {
      const user = this.payment.attributes.users.data[0];
      return user.attributes.username || user.attributes.email;
    }
    return 'Unknown User';
  }

  goBack(): void {
    this.router.navigate(['/home/<USER>']);
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  getTestPackPrice(): string {
    if (
      this.payment?.attributes.testPack?.data?.attributes?.price !== undefined
    ) {
      return this.formatAmount(
        this.payment.attributes.testPack.data.attributes.price
      );
    }
    return 'N/A';
  }

  getTestPackStatus(): string {
    return this.payment?.attributes.testPack?.data?.attributes?.status || 'N/A';
  }

  getTestPackCreatedAt(): string {
    const createdAt =
      this.payment?.attributes.testPack?.data?.attributes?.createdAt;
    return createdAt ? this.formatDate(createdAt) : 'N/A';
  }
}
