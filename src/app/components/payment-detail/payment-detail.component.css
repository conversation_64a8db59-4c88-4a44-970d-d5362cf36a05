.status-paid {
  background-color: #d1fae5;
  color: #065f46;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-failed {
  background-color: #fee2e2;
  color: #b91c1c;
}

.status-expired {
  background-color: #e5e7eb;
  color: #4b5563;
}

.status-chip {
  background-color: #e0e7ff;
  color: #3730a3;
}

/* Card styling */
.mat-mdc-card {
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.mat-mdc-card-header {
  background-color: #f8fafc;
  border-radius: 12px 12px 0 0;
}

/* List styling */
.mat-mdc-list-item {
  min-height: 56px;
}

.mat-mdc-list-item-icon {
  margin-right: 16px;
  color: #6b7280;
}

/* Chip styling */
.mat-mdc-chip {
  font-weight: 500;
  border-radius: 20px;
}

/* Button styling */
.mat-mdc-raised-button {
  border-radius: 8px;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .mat-mdc-card {
    margin-bottom: 1rem;
  }
  
  .flex.space-x-4 {
    flex-direction: column;
    space-x: 0;
    gap: 0.5rem;
  }
}
