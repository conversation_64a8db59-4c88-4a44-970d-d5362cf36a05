<div
  *ngIf="pertanyaan.length > 0"
  class="max-w-3xl mx-auto px-4 sm:px-6 py-4 sm:py-6"
>
  <!-- Progress Bar -->
  <div class="mb-6">
    <div class="flex justify-between mb-2 text-sm text-gray-600">
      <span>Progress</span>
      <span>{{ currentStep + 1 }}/{{ totalStep }}</span>
    </div>
    <div class="h-2 bg-gray-200 rounded-full">
      <div
        class="h-2 bg-blue-600 rounded-full transition-all duration-300"
        [style.width]="((currentStep + 1) / totalStep) * 100 + '%'"
      ></div>
    </div>
  </div>

  <div class="space-y-6">
    <div class="max-w-3xl mx-auto my-6" *ngFor="let quest of questionsToShow">
      <div
        class="p-6 bg-white rounded-xl shadow-md border border-gray-200 hover:shadow-lg transition-shadow duration-300"
      >
        <div class="space-y-8">
          <!-- Ganti slider dengan pilihan radio button -->
          <div class="flex justify-between items-center gap-4">
            <!-- <PERSON><PERSON><PERSON> -->
            <div
              class="w-1/2 p-4 rounded-lg cursor-pointer transition-all duration-200"
              [class.bg-purple-100]="values[quest.id] === -1"
              [class.border-purple-500]="values[quest.id] === -1"
              [class.border-2]="values[quest.id] === -1"
              [class.border-gray-200]="values[quest.id] !== -1"
              (click)="selectValue(quest.id, -1)"
            >
              <div
                class="text-center font-medium"
                [class.text-purple-600]="values[quest.id] === -1"
              >
                {{ quest.attributes.leftText }}
              </div>
            </div>

            <!-- Pilihan Kanan -->
            <div
              class="w-1/2 p-4 rounded-lg cursor-pointer transition-all duration-200"
              [class.bg-green-100]="values[quest.id] === 1"
              [class.border-green-500]="values[quest.id] === 1"
              [class.border-2]="values[quest.id] === 1"
              [class.border-gray-200]="values[quest.id] !== 1"
              (click)="selectValue(quest.id, 1)"
            >
              <div
                class="text-center font-medium"
                [class.text-green-600]="values[quest.id] === 1"
              >
                {{ quest.attributes.rightText }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Navigation -->
  <div class="flex justify-between mt-8">
    <button
      (click)="previous()"
      [disabled]="currentStep === 0"
      class="px-4 sm:px-6 py-2 bg-gray-500 text-white rounded-lg disabled:bg-gray-300 transition-colors hover:bg-gray-600"
    >
      Kembali
    </button>

    <button
      *ngIf="currentStep !== totalStep - 1"
      (click)="next()"
      [disabled]="!isStepComplete()"
      class="px-4 sm:px-6 py-2 bg-blue-500 text-white rounded-lg disabled:bg-blue-300 transition-colors hover:bg-blue-600"
    >
      Lanjut
    </button>

    <!-- Tombol Submit Jawaban -->
    <button
      *ngIf="currentStep === totalStep - 1"
      (click)="submitAnswers()"
      [disabled]="!isFormComplete()"
      class="bg-green-500 text-white px-4 py-2 rounded disabled:bg-gray-300"
    >
      Submit Jawaban
    </button>
  </div>
</div>
