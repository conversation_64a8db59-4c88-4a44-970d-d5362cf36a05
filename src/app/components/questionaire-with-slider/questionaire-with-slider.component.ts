import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
  OnInit,
} from '@angular/core';
import { ViewportScroller } from '@angular/common';
import { MBTIQuestion } from 'src/app/interfaces/mbti-question';

@Component({
  selector: 'app-questionaire-with-slider',
  templateUrl: './questionaire-with-slider.component.html',
})
export class QuestionaireWithSliderComponent implements OnInit, OnChanges {
  @Input() pertanyaan: MBTIQuestion[] = [];
  @Input() jumlahTotalSoal: number = 10;
  @Output() onAnswerSelected = new EventEmitter<any[]>();
  @Output() onSubmit = new EventEmitter<any[]>();

  answers: { id: number; value: number; category: string }[] = [];
  values: { [key: number]: number } = {};

  currentStep: number = 0;
  totalStep: number = 0;
  questionsToShow: MBTIQuestion[] = [];

  constructor(private viewportScroller: ViewportScroller) {}

  ngOnInit(): void {
    this.totalStep = Math.ceil(this.pertanyaan.length / this.jumlahTotalSoal);
    this.updateQuestionsToShow();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['pertanyaan']) {
      this.totalStep = Math.ceil(this.pertanyaan.length / this.jumlahTotalSoal);
      this.updateQuestionsToShow();
    }
  }

  updateQuestionsToShow() {
    const start = this.currentStep * this.jumlahTotalSoal;
    const end = start + this.jumlahTotalSoal;
    this.questionsToShow = this.pertanyaan.slice(start, end);
  }

  getValue(id: number): string {
    const question = this.pertanyaan.find((q) => q.id === id);
    if (!question) return 'Netral';

    switch (this.values[id]) {
      case -1:
        return question.attributes.leftText;
      case 1:
        return question.attributes.rightText;
      default:
        return 'Netral';
    }
  }

  onSliderChange(id: number) {
    const question = this.pertanyaan.find((q) => q.id === id);
    if (!question) return;

    const existingAnswerIndex = this.answers.findIndex((a) => a.id === id);
    const newAnswer = {
      id: id,
      value: this.values[id],
      category: question.attributes.category,
    };

    if (existingAnswerIndex !== -1) {
      this.answers[existingAnswerIndex] = newAnswer;
    } else {
      this.answers.push(newAnswer);
    }
  }

  isStepComplete(): boolean {
    return this.questionsToShow.every((q) => this.values[q.id] !== undefined);
  }

  next() {
    if (this.currentStep < this.totalStep - 1 && this.isStepComplete()) {
      this.currentStep++;
      this.updateQuestionsToShow();
      setTimeout(() => {
        this.viewportScroller.scrollToPosition([0, 0]);
      }, 100);
    }
  }

  previous() {
    if (this.currentStep > 0) {
      this.currentStep--;
      this.updateQuestionsToShow();
      setTimeout(() => {
        this.viewportScroller.scrollToPosition([0, 0]);
      }, 100);
    }
  }

  isFormComplete(): boolean {
    return this.answers.length === this.pertanyaan.length;
  }

  submitAnswers() {
    if (this.isFormComplete()) {
      this.onSubmit.emit(this.answers);
    } else {
      console.warn('Form not complete');
    }
  }

  selectValue(id: number, value: number) {
    this.values[id] = value;
    this.onSliderChange(id);
  }
}
