import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-text-block',
  templateUrl: './text-block.component.html',
  styleUrls: ['./text-block.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class TextBlockComponent {
  @Input() title!: string;
  @Input() content!: string;
  @Input() alignment: 'left' | 'center' | 'right' = 'left';
}
