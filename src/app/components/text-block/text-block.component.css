/* Professional Text Block Component Styles */

.text-block-container {
  background: white;
  border-radius: 1.5rem;
  margin: 2rem auto;
  max-width: 1200px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  overflow: visible;
  position: relative;
  min-height: auto;
}

.text-block-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.text-block-content {
  padding: 3rem;
  padding-bottom: 4rem;
  min-height: auto;
  height: auto;
}

.text-block-title {
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 2rem;
  font-size: 2.25rem;
  line-height: 1.3;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  padding-bottom: 0.5rem;
}

.prose {
  max-width: none;
  font-size: 1.125rem;
  line-height: 1.8;
  color: #374151;
  height: auto;
  overflow: visible;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: #1f2937;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.prose p {
  margin-bottom: 2rem;
  line-height: 1.8;
}

.prose h1 {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.prose h2 {
  font-size: 2rem;
  color: #1f2937;
}

.prose h3 {
  font-size: 1.5rem;
  color: #374151;
}

.prose p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  color: #374151;
}

.prose ul, .prose ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.prose li {
  margin-bottom: 0.75rem;
  line-height: 1.7;
  position: relative;
}

.prose ul li::before {
  content: '•';
  color: #667eea;
  font-size: 1.2em;
  position: absolute;
  left: -1.5rem;
  top: 0;
}

.prose ol li {
  counter-increment: list-counter;
}

.prose ol li::before {
  content: counter(list-counter) '.';
  color: #667eea;
  font-weight: 600;
  position: absolute;
  left: -1.5rem;
  top: 0;
}

.prose ol {
  counter-reset: list-counter;
}

.prose strong {
  font-weight: 700;
  color: #1f2937;
}

.prose em {
  font-style: italic;
  color: #4b5563;
}

.prose a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

.prose a:hover {
  color: #764ba2;
  border-bottom-color: #764ba2;
}

.prose blockquote {
  border-left: 4px solid #667eea;
  padding-left: 1.5rem;
  margin: 2rem 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  padding: 1.5rem;
  border-radius: 0.75rem;
  font-style: italic;
  color: #4b5563;
}

.prose code {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875em;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.prose pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
  overflow-x: auto;
  margin: 2rem 0;
}

.prose pre code {
  background: transparent;
  color: inherit;
  padding: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .text-block-content {
    padding: 2rem 1.5rem;
  }
  
  .text-block-title {
    font-size: 1.875rem;
    margin-bottom: 1.5rem;
  }
  
  .prose {
    font-size: 1rem;
  }
  
  .prose h1 {
    font-size: 2rem;
  }
  
  .prose h2 {
    font-size: 1.5rem;
  }
  
  .prose h3 {
    font-size: 1.25rem;
  }
  
  .prose ul, .prose ol {
    padding-left: 1.5rem;
  }
}
