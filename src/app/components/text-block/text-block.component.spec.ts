import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TextBlockComponent } from './text-block.component';

describe('TextBlockComponent', () => {
  let component: TextBlockComponent;
  let fixture: ComponentFixture<TextBlockComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TextBlockComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(TextBlockComponent);
    component = fixture.componentInstance;

    // Set required inputs
    component.title = 'Test Title';
    component.content = 'Test Content';
    component.alignment = 'center';

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display title and content', () => {
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('h2').textContent).toContain('Test Title');
    expect(compiled.querySelector('.prose').innerHTML).toContain(
      'Test Content'
    );
  });

  it('should apply correct alignment class', () => {
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('div').classList).toContain('text-center');
  });
});
