<div
  class="flex justify-center items-center h-screen bg-gradient-to-r from-pink-300 via-red-300 to-pink-400 max-h-[300px] mb-5"
>
  <div class="flex space-x-4">
    <button
      (click)="navigateTo(button1Url)"
      class="rounded-lg bg-red-300 text-black font-bold py-4 px-8 shadow-lg hover:bg-red-400 transition cursor-pointer"
    >
      {{ button1Title }}
    </button>
    <button
      (click)="navigateTo(button2Url)"
      class="rounded-lg bg-red-500 text-white font-bold py-4 px-8 shadow-lg hover:bg-red-600 transition cursor-pointer"
    >
      {{ button2Title }}
    </button>
  </div>
</div>
