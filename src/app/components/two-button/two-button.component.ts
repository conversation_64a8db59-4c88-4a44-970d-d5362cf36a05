import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-two-button',
  templateUrl: './two-button.component.html',
  styleUrls: ['./two-button.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class TwoButtonComponent {
  @Input() button1Title!: string;
  @Input() button1Url!: string;

  @Input() button2Title!: string;
  @Input() button2Url!: string;

  constructor(private router: Router) {}

  navigateTo(url: string) {
    // Check if it's an external URL or hash link
    if (url.startsWith('http') || url.startsWith('#')) {
      if (url.startsWith('#')) {
        // Special handling for test-packages section - redirect to dedicated page
        if (url === '#test-packages') {
          this.router.navigate(['/test-packages']);
          return;
        }
        // Handle other hash navigation for sections
        const element = document.getElementById(url.substring(1));
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      } else {
        // Handle external URLs
        window.open(url, '_blank');
      }
    } else {
      // Handle internal navigation
      this.router.navigate([url]);
    }
  }
}
