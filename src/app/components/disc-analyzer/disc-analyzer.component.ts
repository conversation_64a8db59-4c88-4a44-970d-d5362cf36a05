import {
  Component,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { DiscResultService } from 'src/app/service/disc-result.service';

@Component({
  selector: 'app-disc-analyzer',
  templateUrl: './disc-analyzer.component.html',
  styleUrls: ['./disc-analyzer.component.css'],
})
export class DiscAnalyzerComponent implements OnInit, OnChanges {
  @Input() discString: string = '';
  pattern: string = '';
  patternName: string = '';
  patternSlogan: string = '';
  patternAnalyzer: string = '';

  constructor(private discResultService: DiscResultService) {}

  ngOnInit(): void {
    // Optional: initial fetch if discString is already available on component init
    if (this.discString) {
      this.fetchAnalyzerData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['discString'] && changes['discString'].currentValue) {
      this.fetchAnalyzerData();
    }
  }

  fetchAnalyzerData(): void {
    this.discResultService
      .getStringAnalyzer(this.discString)
      .subscribe((res: any) => {
        if (res.data && res.data.length > 0) {
          const item = res.data[0].attributes;
          this.pattern = item.pattern;
          this.patternName = item.patternName;
          this.patternSlogan = item.patternSlogan;
          this.patternAnalyzer = item.description; // Sesuai JSON, `description` ada di dalam `attributes`
        }
      });
  }
}
