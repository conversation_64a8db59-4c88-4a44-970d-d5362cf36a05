import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { AuthService } from 'src/app/service/auth.service';
import { TestManagerService } from 'src/app/service/test-manager.service';
import { environment } from 'src/environments/environment';

interface TestPackRecord {
  id: number;
  createdAt: string;
  status: 'open' | 'finish';
  paymentStatus: 'UNPAID' | 'PAID' | 'EXPIRED';
  users?: {
    data: Array<{
      id: number;
      attributes: {
        username: string;
        email: string;
      };
    }>;
  };
  tests: {
    data: Array<any>;
  };
}

@Component({
  selector: 'app-user-test-results',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './user-test-results.component.html',
  styleUrls: ['./user-test-results.component.css'],
})
export class UserTestResultsComponent implements OnInit {
  displayedColumns: string[] = [
    'createdAt',
    'status',
    'paymentStatus',
    'actions',
  ];
  dataSource = new MatTableDataSource<TestPackRecord>();
  isLoading = true;
  error: string | null = null;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private testManager: TestManagerService,
    private authService: AuthService,
    public router: Router,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.loadUserTestResults();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  loadUserTestResults() {
    this.isLoading = true;
    const user = this.authService.getCurrentUser();

    if (!user || !user.id) {
      this.error = 'User tidak ditemukan';
      this.isLoading = false;
      return;
    }

    // Get test packs for current user only - simplified query
    const url = `${this.testManager.getTestPackUrl()}?populate=users&filters[users][id][$eq]=${
      user.id
    }&sort[0]=createdAt:desc`;

    this.http.get<any>(url).subscribe({
      next: (results: any) => {
        if (results.data && Array.isArray(results.data)) {
          const testPackRecords: TestPackRecord[] = results.data.map(
            (item: any) => ({
              id: item.id,
              createdAt: item.attributes.createdAt,
              status: item.attributes.status,
              paymentStatus: item.attributes.paymentStatus || 'UNPAID',
              users: item.attributes.users,
              tests: { data: [] }, // Simplified - we don't need test details for this view
            })
          );

          this.dataSource.data = testPackRecords;
        } else {
          this.dataSource.data = [];
        }
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading user test results:', error);
        this.error = 'Gagal memuat data hasil test';
        this.isLoading = false;
      },
    });
  }

  viewReport(testPackId: number) {
    console.log('Navigating to test pack report:', testPackId);
    console.log('Route:', ['/home/<USER>', testPackId]);
    // Navigate to the report page
    this.router.navigate(['/home/<USER>', testPackId]).then(
      (success) => {
        console.log('Navigation success:', success);
      },
      (error) => {
        console.error('Navigation error:', error);
      }
    );
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'open':
        return 'bg-yellow-100 text-yellow-800';
      case 'finish':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'open':
        return 'Dalam Proses';
      case 'finish':
        return 'Selesai';
      default:
        return status;
    }
  }

  getPaymentStatusBadgeClass(status: string): string {
    switch (status) {
      case 'PAID':
        return 'bg-green-100 text-green-800';
      case 'UNPAID':
        return 'bg-red-100 text-red-800';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getPaymentStatusText(status: string): string {
    switch (status) {
      case 'PAID':
        return 'Lunas';
      case 'UNPAID':
        return 'Belum Bayar';
      case 'EXPIRED':
        return 'Kadaluarsa';
      default:
        return status;
    }
  }

  canViewReport(record: TestPackRecord): boolean {
    return record.status === 'finish' && record.paymentStatus === 'PAID';
  }

  continueTest(testPackId: number) {
    // Navigate to start test page for this specific test pack
    this.router.navigate(['/home/<USER>'], {
      queryParams: { testPackId: testPackId },
    });
  }

  canContinueTest(record: TestPackRecord): boolean {
    return record.status === 'open' && record.paymentStatus === 'PAID';
  }
}
