<div class="max-w-6xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
  <!-- Header Section -->
  <div class="mb-10">
    <h1 class="text-3xl font-bold text-gray-900 mb-4">Hasil Test Saya</h1>
    <p class="text-lg text-gray-600">Daftar hasil test pack yang telah Anda ikuti</p>
  </div>

  <!-- Table Card Container -->
  <div class="bg-white rounded-xl shadow-lg overflow-hidden">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex justify-center items-center py-12">
      <mat-spinner diameter="40"></mat-spinner>
    </div>

    <!-- Error State -->
    <div *ngIf="error" class="p-4 text-red-700 bg-red-100">
      {{ error }}
    </div>

    <!-- Table Section -->
    <div *ngIf="!isLoading && !error" class="overflow-x-auto">
      <table mat-table [dataSource]="dataSource" class="w-full">
        <!-- <PERSON>lo<PERSON> -->
        <ng-container matColumnDef="createdAt">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-4 bg-gray-50 text-left text-sm font-semibold text-gray-900"
          >
            Tanggal Test
          </th>
          <td
            mat-cell
            *matCellDef="let element"
            class="px-6 py-4 text-sm text-gray-700 border-b border-gray-200"
          >
            {{ element.createdAt | date : "d MMMM y, HH:mm" }}
          </td>
        </ng-container>

        <!-- Kolom Status Test -->
        <ng-container matColumnDef="status">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-4 bg-gray-50 text-left text-sm font-semibold text-gray-900"
          >
            Status Test
          </th>
          <td
            mat-cell
            *matCellDef="let element"
            class="px-6 py-4 text-sm border-b border-gray-200"
          >
            <span
              class="px-2 py-1 rounded-full text-xs font-medium"
              [class]="getStatusBadgeClass(element.status)"
            >
              {{ getStatusText(element.status) }}
            </span>
          </td>
        </ng-container>

        <!-- Kolom Status Pembayaran -->
        <ng-container matColumnDef="paymentStatus">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-4 bg-gray-50 text-left text-sm font-semibold text-gray-900"
          >
            Status Pembayaran
          </th>
          <td
            mat-cell
            *matCellDef="let element"
            class="px-6 py-4 text-sm border-b border-gray-200"
          >
            <span
              class="px-2 py-1 rounded-full text-xs font-medium"
              [class]="getPaymentStatusBadgeClass(element.paymentStatus)"
            >
              {{ getPaymentStatusText(element.paymentStatus) }}
            </span>
          </td>
        </ng-container>

        <!-- Kolom Actions -->
        <ng-container matColumnDef="actions">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-4 bg-gray-50 text-left text-sm font-semibold text-gray-900"
          >
            Aksi
          </th>
          <td
            mat-cell
            *matCellDef="let element"
            class="px-6 py-4 text-sm border-b border-gray-200"
          >
            <div class="flex space-x-2">
              <!-- View Report Button -->
              <button
                *ngIf="canViewReport(element)"
                mat-raised-button
                color="primary"
                (click)="viewReport(element.id)"
                class="text-xs"
              >
                <mat-icon class="text-sm">visibility</mat-icon>
                Lihat Hasil
              </button>

              <!-- Continue Test Button -->
              <button
                *ngIf="canContinueTest(element)"
                mat-raised-button
                color="accent"
                (click)="continueTest(element.id)"
                class="text-xs"
              >
                <mat-icon class="text-sm">play_arrow</mat-icon>
                Lanjutkan Test
              </button>

              <!-- Payment Required Message -->
              <span
                *ngIf="element.paymentStatus === 'UNPAID'"
                class="text-xs text-red-600 flex items-center"
              >
                <mat-icon class="text-sm mr-1">payment</mat-icon>
                Pembayaran diperlukan
              </span>

              <!-- Expired Message -->
              <span
                *ngIf="element.paymentStatus === 'EXPIRED'"
                class="text-xs text-gray-600 flex items-center"
              >
                <mat-icon class="text-sm mr-1">schedule</mat-icon>
                Kadaluarsa
              </span>
            </div>
          </td>
        </ng-container>

        <tr
          mat-header-row
          *matHeaderRowDef="displayedColumns"
          class="border-b border-gray-200"
        ></tr>
        <tr
          mat-row
          *matRowDef="let row; columns: displayedColumns"
          class="hover:bg-gray-50 transition-colors duration-200"
        ></tr>
      </table>
    </div>

    <!-- Pagination Section -->
    <div class="border-t border-gray-200 bg-gray-50">
      <mat-paginator
        [pageSize]="10"
        [pageSizeOptions]="[5, 10, 25, 100]"
        class="bg-transparent"
        aria-label="Pilih halaman hasil test"
      >
      </mat-paginator>
    </div>
  </div>

  <!-- Empty State -->
  <div
    *ngIf="!isLoading && !error && dataSource?.data?.length === 0"
    class="text-center py-12"
  >
    <mat-icon class="text-gray-400 text-6xl mb-4">assignment</mat-icon>
    <p class="text-gray-500 text-lg mb-4">Anda belum memiliki hasil test</p>
    <button
      mat-raised-button
      color="primary"
      (click)="router.navigate(['/test-packages'])"
    >
      <mat-icon>shopping_cart</mat-icon>
      Beli Test Pack
    </button>
  </div>

  <!-- Quick Actions Card -->
  <div class="mt-8 bg-blue-50 rounded-lg p-6">
    <h3 class="text-lg font-semibold text-blue-900 mb-4">Aksi Cepat</h3>
    <div class="flex flex-wrap gap-4">
      <button
        mat-raised-button
        color="primary"
        (click)="router.navigate(['/test-packages'])"
      >
        <mat-icon>add_shopping_cart</mat-icon>
        Beli Test Pack Baru
      </button>
      <button
        mat-raised-button
        (click)="router.navigate(['/home/<USER>'])"
      >
        <mat-icon>receipt_long</mat-icon>
        Riwayat Pembayaran
      </button>
    </div>
  </div>
</div>
