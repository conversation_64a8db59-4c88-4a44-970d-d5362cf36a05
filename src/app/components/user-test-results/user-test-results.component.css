/* Custom styles for user test results component */

.mat-mdc-table {
  background: transparent;
}

.mat-mdc-header-cell {
  font-weight: 600;
  color: #374151;
}

.mat-mdc-cell {
  border-bottom: 1px solid #e5e7eb;
}

.mat-mdc-row:hover {
  background-color: #f9fafb;
}

/* Status badge styles */
.bg-yellow-100 {
  background-color: #fef3c7;
}

.text-yellow-800 {
  color: #92400e;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.text-green-800 {
  color: #166534;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.text-red-800 {
  color: #991b1b;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.text-gray-800 {
  color: #1f2937;
}

/* Button spacing */
.mat-mdc-raised-button + .mat-mdc-raised-button {
  margin-left: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mat-mdc-table {
    font-size: 14px;
  }
  
  .mat-mdc-header-cell,
  .mat-mdc-cell {
    padding: 8px 12px;
  }
}
