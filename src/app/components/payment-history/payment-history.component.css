.status-paid {
  background-color: #d1fae5;
  color: #065f46;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-failed {
  background-color: #fee2e2;
  color: #b91c1c;
}

.status-expired {
  background-color: #e5e7eb;
  color: #4b5563;
}

.success-snackbar {
  background-color: #10b981;
  color: white;
}

.error-snackbar {
  background-color: #ef4444;
  color: white;
}

/* Table Styling */
.mat-mdc-table {
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.mat-mdc-header-row {
  background-color: #f9fafb;
}

.mat-mdc-header-cell {
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

.mat-mdc-cell {
  border-bottom: 1px solid #f3f4f6;
}

.mat-mdc-row:hover {
  background-color: #f9fafb;
}

.mat-mdc-row:last-child .mat-mdc-cell {
  border-bottom: none;
}

/* Paginator Styling */
.mat-mdc-paginator {
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* Responsive table */
@media (max-width: 768px) {
  .overflow-x-auto {
    overflow-x: auto;
  }
  
  .mat-mdc-table {
    min-width: 600px;
  }
}
