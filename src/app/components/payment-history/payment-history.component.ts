import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule, Router } from '@angular/router';
import { environment } from 'src/environments/environment.development';
import { AuthService } from 'src/app/service/auth.service';
import { PaymentService } from 'src/app/service/payment.service';
import { catchError, finalize } from 'rxjs/operators';
import { of } from 'rxjs';

interface Payment {
  id: number;
  attributes: {
    externalId: string;
    amount: number;
    status: 'PENDING' | 'PAID' | 'FAILED' | 'EXPIRED';
    description: string;
    createdAt: string;
    paidAt?: string;
    testPack?: {
      data: {
        id: number;
        attributes: {
          status: string;
        };
      };
    };
    users?: {
      data: Array<{
        id: number;
        attributes: {
          username: string;
          email: string;
        };
      }>;
    };
  };
}

interface PaymentResponse {
  data: Payment[];
  meta: {
    pagination: {
      total: number;
      page: number;
      pageSize: number;
      pageCount: number;
    };
  };
}

@Component({
  selector: 'app-payment-history',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatTooltipModule,
    RouterModule,
  ],
  templateUrl: './payment-history.component.html',
  styleUrls: ['./payment-history.component.css'],
})
export class PaymentHistoryComponent implements OnInit {
  payments: Payment[] = [];
  isLoading = true;
  error: string | null = null;

  // Pagination properties
  totalItems = 0;
  pageSize = 10;
  currentPage = 0;

  // Table columns for admin
  adminDisplayedColumns: string[] = [
    'user',
    'externalId',
    'description',
    'amount',
    'status',
    'createdAt',
    'actions',
  ];
  userDisplayedColumns: string[] = [
    'externalId',
    'description',
    'amount',
    'status',
    'createdAt',
    'actions',
  ];

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private paymentService: PaymentService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadPaymentHistory();
  }

  loadPaymentHistory(): void {
    this.isLoading = true;

    const user = this.authService.getCurrentUser();
    if (!user || !user.id) {
      this.error = 'User not authenticated';
      this.isLoading = false;
      return;
    }

    console.log(
      'Loading payment history for user:',
      user.id,
      'email:',
      user.email
    );

    // Check if user is admin
    const isAdmin = this.authService.isAdmin();
    console.log('User is admin:', isAdmin);

    // Build query - admin sees all payments with pagination, regular users see only their own
    let queryParams: string;
    if (isAdmin) {
      // Admin can see all payments with pagination
      queryParams = `?populate=testPack,users&sort[0]=createdAt:desc&pagination[page]=${
        this.currentPage + 1
      }&pagination[pageSize]=${this.pageSize}`;
    } else {
      // Regular users see only their own payments - try different filter format
      queryParams = `?populate=testPack,users&filters[users][id][$eq]=${user.id}&sort[0]=createdAt:desc`;
    }

    console.log('Payment history query:', queryParams);

    this.http
      .get<PaymentResponse>(`${environment.url}payments${queryParams}`)
      .pipe(
        catchError((error) => {
          console.error('Error loading payment history:', error);
          this.error = 'Failed to load payment history. Please try again.';
          return of({
            data: [],
            meta: {
              pagination: {
                total: 0,
                page: 1,
                pageSize: this.pageSize,
                pageCount: 0,
              },
            },
          } as PaymentResponse);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((response) => {
        console.log('Payment history response:', response);
        this.payments = response.data;
        this.totalItems = response.meta.pagination.total;
        console.log(
          'Loaded payments:',
          this.payments.length,
          'Total items:',
          this.totalItems
        );
      });
  }
  getStatusClass(status: string): string {
    switch (status) {
      case 'PAID':
        return 'status-paid';
      case 'PENDING':
        return 'status-pending';
      case 'FAILED':
        return 'status-failed';
      case 'EXPIRED':
        return 'status-expired';
      default:
        return '';
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  formatAmount(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  getPaymentStatusText(status: string): string {
    switch (status) {
      case 'PAID':
        return 'Pembayaran Berhasil';
      case 'PENDING':
        return 'Menunggu Pembayaran';
      case 'FAILED':
        return 'Pembayaran Gagal';
      case 'EXPIRED':
        return 'Pembayaran Kedaluwarsa';
      default:
        return status;
    }
  }

  retryPayment(payment: Payment): void {
    this.isLoading = true;

    this.paymentService.retryPayment(payment.id).subscribe({
      next: (response) => {
        this.isLoading = false;

        // Show success message
        this.snackBar.open('Pembayaran berhasil dibuat ulang', 'Tutup', {
          duration: 3000,
          panelClass: ['success-snackbar'],
        });

        // Redirect to payment page
        if (response.paymentUrl) {
          window.location.href = response.paymentUrl;
        } else {
          this.router.navigate(['/payment']);
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error retrying payment:', error);

        // Show error message
        this.snackBar.open(
          'Gagal membuat ulang pembayaran. Silakan coba lagi.',
          'Tutup',
          {
            duration: 5000,
            panelClass: ['error-snackbar'],
          }
        );
      },
    });
  }

  // Check if current user is admin
  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  // Get user information from payment (for admin view)
  getPaymentUser(payment: Payment): string {
    if (
      payment.attributes.users?.data &&
      payment.attributes.users.data.length > 0
    ) {
      const user = payment.attributes.users.data[0];
      return user.attributes.username || user.attributes.email;
    }
    return 'Unknown User';
  }

  // Handle page change
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadPaymentHistory();
  }

  // Get displayed columns based on user role
  getDisplayedColumns(): string[] {
    return this.isAdmin()
      ? this.adminDisplayedColumns
      : this.userDisplayedColumns;
  }
}
