<div class="container mx-auto p-4">
  <div class="bg-white rounded-lg shadow-lg p-6">
    <h1 class="text-2xl font-bold mb-6 text-gray-800">
      {{ isAdmin() ? "Semua Riwayat Pembayaran" : "Riwayat Pembayaran" }}
    </h1>

    <!-- Loading Spinner -->
    <div *ngIf="isLoading" class="flex justify-center my-8">
      <mat-spinner diameter="40"></mat-spinner>
    </div>

    <!-- Error Message -->
    <div
      *ngIf="error"
      class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
    >
      {{ error }}
    </div>

    <!-- No Payments -->
    <div
      *ngIf="!isLoading && !error && payments.length === 0"
      class="text-center py-8"
    >
      <mat-icon class="text-gray-400 text-6xl mb-4">receipt_long</mat-icon>
      <p class="text-gray-500 text-lg">
        {{
          isAdmin()
            ? "Tidak ada riwayat pembayaran dalam sistem"
            : "Anda belum memiliki riwayat pembayaran"
        }}
      </p>
    </div>

    <!-- Admin Table View -->
    <div *ngIf="!isLoading && !error && payments.length > 0 && isAdmin()">
      <div class="overflow-x-auto">
        <table mat-table [dataSource]="payments" class="w-full">
          <!-- User Column -->
          <ng-container matColumnDef="user">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">
              User
            </th>
            <td mat-cell *matCellDef="let payment" class="py-2">
              {{ getPaymentUser(payment) }}
            </td>
          </ng-container>

          <!-- External ID Column -->
          <ng-container matColumnDef="externalId">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">
              ID Pembayaran
            </th>
            <td mat-cell *matCellDef="let payment" class="py-2">
              <span class="font-mono text-sm">{{
                payment.attributes.externalId
              }}</span>
            </td>
          </ng-container>

          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">
              Deskripsi
            </th>
            <td mat-cell *matCellDef="let payment" class="py-2">
              {{ payment.attributes.description }}
            </td>
          </ng-container>

          <!-- Amount Column -->
          <ng-container matColumnDef="amount">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">
              Jumlah
            </th>
            <td mat-cell *matCellDef="let payment" class="py-2">
              <span class="font-semibold">{{
                formatAmount(payment.attributes.amount)
              }}</span>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">
              Status
            </th>
            <td mat-cell *matCellDef="let payment" class="py-2">
              <span
                class="text-xs px-2 py-1 rounded-full font-medium"
                [ngClass]="getStatusClass(payment.attributes.status)"
              >
                {{ getPaymentStatusText(payment.attributes.status) }}
              </span>
            </td>
          </ng-container>

          <!-- Created Date Column -->
          <ng-container matColumnDef="createdAt">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">
              Tanggal
            </th>
            <td mat-cell *matCellDef="let payment" class="py-2">
              <span class="text-sm">{{
                formatDate(payment.attributes.createdAt)
              }}</span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">
              Aksi
            </th>
            <td mat-cell *matCellDef="let payment" class="py-2">
              <button
                mat-icon-button
                color="primary"
                matTooltip="Lihat Detail"
                [routerLink]="['/home/<USER>', payment.id]"
              >
                <mat-icon>visibility</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="getDisplayedColumns()"></tr>
          <tr
            mat-row
            *matRowDef="let row; columns: getDisplayedColumns()"
            class="hover:bg-gray-50"
          ></tr>
        </table>
      </div>

      <!-- Pagination for Admin -->
      <mat-paginator
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]"
        [pageIndex]="currentPage"
        (page)="onPageChange($event)"
        class="mt-4"
        showFirstLastButtons
      >
      </mat-paginator>
    </div>

    <!-- User Card View -->
    <div
      *ngIf="!isLoading && !error && payments.length > 0 && !isAdmin()"
      class="space-y-4"
    >
      <mat-card *ngFor="let payment of payments" class="mb-4">
        <mat-card-header>
          <mat-card-title class="flex items-center justify-between">
            <span>{{ payment.attributes.description }}</span>
            <span
              class="text-sm px-3 py-1 rounded-full"
              [ngClass]="getStatusClass(payment.attributes.status)"
            >
              {{ getPaymentStatusText(payment.attributes.status) }}
            </span>
          </mat-card-title>
          <mat-card-subtitle>
            ID: {{ payment.attributes.externalId }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content class="pt-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p class="text-gray-600 text-sm">Tanggal Pembelian</p>
              <p class="font-medium">
                {{ formatDate(payment.attributes.createdAt) }}
              </p>
            </div>

            <div>
              <p class="text-gray-600 text-sm">Jumlah</p>
              <p class="font-medium">
                {{ formatAmount(payment.attributes.amount) }}
              </p>
            </div>

            <div *ngIf="payment.attributes.status === 'PAID'">
              <p class="text-gray-600 text-sm">Tanggal Pembayaran</p>
              <p class="font-medium">
                {{
                  payment.attributes.paidAt
                    ? formatDate(payment.attributes.paidAt)
                    : "N/A"
                }}
              </p>
            </div>

            <div *ngIf="payment.attributes.testPack?.data">
              <p class="text-gray-600 text-sm">Test Pack</p>
              <p class="font-medium">
                ID: {{ payment.attributes.testPack?.data?.id }}
              </p>
              <p class="text-sm text-gray-500">
                Status:
                {{ payment.attributes.testPack?.data?.attributes?.status }}
              </p>
            </div>
          </div>
        </mat-card-content>

        <mat-divider></mat-divider>

        <mat-card-actions align="end">
          <!-- Lihat Detail Button for all payments -->
          <button
            mat-button
            color="primary"
            [routerLink]="['/home/<USER>', payment.id]"
          >
            <mat-icon>visibility</mat-icon>
            Lihat Detail
          </button>

          <ng-container
            *ngIf="
              payment.attributes.status === 'PAID' &&
              payment.attributes.testPack?.data
            "
          >
            <a mat-button color="primary" [routerLink]="['/home/<USER>']">
              <mat-icon>play_arrow</mat-icon>
              Mulai Test
            </a>
          </ng-container>

          <ng-container *ngIf="payment.attributes.status === 'PENDING'">
            <button mat-button color="warn" (click)="retryPayment(payment)">
              <mat-icon>refresh</mat-icon>
              Bayar Sekarang
            </button>
          </ng-container>

          <ng-container
            *ngIf="
              payment.attributes.status === 'FAILED' ||
              payment.attributes.status === 'EXPIRED'
            "
          >
            <button mat-button color="warn" (click)="retryPayment(payment)">
              <mat-icon>refresh</mat-icon>
              Coba Lagi
            </button>
          </ng-container>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>
