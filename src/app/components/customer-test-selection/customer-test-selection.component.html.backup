<!-- Navigation -->
<app-dynamic-navbar></app-dynamic-navbar>

<!-- Main Content -->
<main class="min-h-screen bg-white">
  <div class="max-w-6xl mx-auto px-4 py-12">
    <div class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-800 mb-4"><PERSON><PERSON><PERSON> Anda</h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        Temukan potensi diri Anda dengan paket tes psikologi kami yang telah dirancang khusus untuk pengembangan personal dan profesional
      </p>
    </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-12">
    <div
      class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"
    ></div>
  </div>

  <!-- Error State -->
  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"
  >
    <p>{{ error }}</p>
  </div>

  <!-- Test Pack Options -->
  <div
    *ngIf="!isLoading && !error"
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
  >
    <div
      *ngFor="let testPack of testPackOptions"
      class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 cursor-pointer"
      [class.border-2]="selectedTestPack?.id === testPack.id"
      [class.border-pink-500]="selectedTestPack?.id === testPack.id"
      [class.ring-4]="selectedTestPack?.id === testPack.id"
      [class.ring-pink-200]="selectedTestPack?.id === testPack.id"
    >
      <!-- Test Pack Image -->
      <div class="h-48 bg-gray-200 overflow-hidden relative">
        <img
          *ngIf="testPack.imageUrl"
          [src]="testPack.imageUrl"
          [alt]="testPack.name"
          class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
          loading="lazy"
        />
        <div
          *ngIf="!testPack.imageUrl"
          class="w-full h-full flex items-center justify-center bg-gradient-to-br from-pink-400 via-purple-500 to-indigo-600"
        >
          <div class="text-center">
            <div class="text-white text-4xl mb-2">🧠</div>
            <span class="text-white text-lg font-bold">{{
              testPack.name
            }}</span>
          </div>
        </div>
        <!-- Price badge overlay -->
        <div
          class="absolute top-4 right-4 bg-pink-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg"
        >
          Rp {{ testPack.price | number }}
        </div>
      </div>

      <!-- Test Pack Info -->
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-2">
          {{ testPack.name }}
        </h3>
        <p class="text-gray-600 mb-4">{{ testPack.description }}</p>

        <!-- Tests Included -->
        <div class="mb-6">
          <h4 class="font-semibold text-gray-700 mb-3 flex items-center">
            <span class="bg-pink-100 text-pink-600 p-1 rounded-full mr-2">
              ✓
            </span>
            Tes yang termasuk:
          </h4>
          <div class="flex flex-wrap gap-2">
            <span
              *ngFor="let test of testPack.tests"
              class="inline-block bg-gradient-to-r from-pink-100 to-purple-100 text-pink-700 px-3 py-1 rounded-full text-sm font-medium border border-pink-200"
            >
              {{ test }}
            </span>
          </div>
        </div>

        <!-- Price and Action -->
        <div class="flex justify-between items-center mt-4">
          <div class="flex flex-col">
            <span class="text-sm text-gray-500">Mulai dari</span>
            <span class="text-2xl font-bold text-pink-600"
              >Rp {{ testPack.price | number }}</span
            >
          </div>
          <button
            (click)="selectTestPack(testPack)"
            class="px-6 py-3 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
            [class.bg-pink-700]="selectedTestPack?.id === testPack.id"
            [class.scale-105]="selectedTestPack?.id === testPack.id"
          >
            <span *ngIf="selectedTestPack?.id !== testPack.id"
              >Pilih & Bayar</span
            >
            <span *ngIf="selectedTestPack?.id === testPack.id">✓ Dipilih</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</main>

<!-- Footer -->
<app-dynamic-footer></app-dynamic-footer>
