/* Modern Test Selection Page Styles */

.test-selection-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-gray-25) 0%, var(--color-white) 100%);
  padding: var(--spacing-8) var(--spacing-4);
}

.test-selection-content {
  max-width: 1280px;
  margin: 0 auto;
}

/* Header Section - Centered */
.test-selection-header {
  text-align: center;
  margin-bottom: var(--spacing-16);
  padding: var(--spacing-12) var(--spacing-8);
  background: var(--color-white);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-gray-200);
  position: relative;
  overflow: hidden;
}

.test-selection-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.test-selection-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-6);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.test-selection-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  text-align: center;
  display: block;
}

/* Package Cards Grid */
.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-16);
}

.package-card {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: 0;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
}

.package-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.package-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.package-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.package-content {
  padding: var(--spacing-8);
}

.package-price {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  color: var(--color-white);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  box-shadow: var(--shadow-md);
  z-index: 2;
}

.package-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
}

.package-description {
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-6);
}

.package-features {
  margin-bottom: var(--spacing-8);
}

.features-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.test-badges {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-6);
}

.test-badge {
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.test-badge.disc {
  background: rgba(37, 99, 235, 0.1);
  color: var(--color-primary);
  border: 1px solid rgba(37, 99, 235, 0.2);
}

.test-badge.mbti {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.test-badge.teams {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.test-badge.values {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.package-cta {
  text-align: center;
}

.package-button {
  width: 100%;
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.package-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-base);
}

.package-button:hover {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.package-button:hover::before {
  left: 100%;
}

/* Popular Badge */
.popular-badge {
  position: absolute;
  top: var(--spacing-6);
  left: var(--spacing-4);
  background: linear-gradient(135deg, var(--color-secondary) 0%, #059669 100%);
  color: var(--color-white);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-md);
  z-index: 2;
}

/* Animations */
.package-card:nth-child(1) { animation-delay: 0.1s; }
.package-card:nth-child(2) { animation-delay: 0.2s; }
.package-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Trust Indicators */
.trust-section {
  text-align: center;
  margin-top: var(--spacing-16);
  padding: var(--spacing-12) var(--spacing-8);
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.trust-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-8);
}

.trust-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-8);
}

.trust-stat {
  text-align: center;
}

.trust-number {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-2);
}

.trust-label {
  color: var(--color-gray-600);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .packages-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-6);
  }
}

@media (max-width: 768px) {
  .test-selection-container {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .test-selection-header {
    padding: var(--spacing-8) var(--spacing-6);
    margin-bottom: var(--spacing-12);
  }

  .test-selection-title {
    font-size: var(--font-size-4xl);
  }

  .test-selection-subtitle {
    font-size: var(--font-size-lg);
    text-align: center;
    max-width: 100%;
    padding: 0 var(--spacing-4);
  }

  .packages-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }

  .package-content {
    padding: var(--spacing-6);
  }

  .trust-section {
    padding: var(--spacing-8) var(--spacing-6);
    margin-top: var(--spacing-12);
  }

  .trust-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
  }
}

@media (max-width: 640px) {
  .test-selection-header {
    padding: var(--spacing-6);
  }

  .test-selection-title {
    font-size: var(--font-size-3xl);
  }

  .package-image {
    height: 160px;
  }

  .package-title {
    font-size: var(--font-size-xl);
  }

  .trust-stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .trust-number {
    font-size: var(--font-size-3xl);
  }
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.package-skeleton {
  height: 400px;
  border-radius: var(--radius-2xl);
}

/* Focus States for Accessibility */
.package-button:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.package-card:focus-within {
  outline: none;
  box-shadow: var(--shadow-lg), 0 0 0 2px rgba(59, 130, 246, 0.2);
}
