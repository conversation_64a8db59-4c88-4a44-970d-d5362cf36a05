import { Component, OnInit, inject } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TestPackPurchaseService } from 'src/app/service/test-pack-purchase.service';
import { AuthService } from 'src/app/service/auth.service';
import { DynamicNavbarComponent } from '../dynamic-navbar/dynamic-navbar.component';
import { DynamicFooterComponent } from '../dynamic-footer/dynamic-footer.component';

interface TestPackOption {
  id: number;
  name: string;
  description: string;
  price: number;
  tests: string[];
  imageUrl?: string;
}

@Component({
  selector: 'app-customer-test-selection',
  templateUrl: './customer-test-selection.component.html',
  styleUrls: ['./customer-test-selection.component.css'],
  standalone: true,
  imports: [CommonModule, DynamicNavbarComponent, DynamicFooterComponent],
})
export class CustomerTestSelectionComponent implements OnInit {
  testPackOptions: TestPackOption[] = [];
  selectedTestPack: TestPackOption | null = null;
  isLoading: boolean = true;
  error: string | null = null;

  private testPackPurchaseService = inject(TestPackPurchaseService);
  private router = inject(Router);
  private authService = inject(AuthService);

  ngOnInit(): void {
    this.loadTestPackOptions();
  }

  loadTestPackOptions(): void {
    this.isLoading = true;
    this.testPackPurchaseService.getAvailableTestPacks().subscribe({
      next: (testPacks) => {
        this.testPackOptions = testPacks;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error loading test packs:', err);
        this.error =
          'Gagal memuat paket tes yang tersedia. Silakan coba lagi nanti.';
        this.isLoading = false;
      },
    });
  }

  selectTestPack(testPack: TestPackOption): void {
    this.selectedTestPack = testPack;

    // Automatically proceed to payment when a test pack is selected
    this.proceedToPayment();
  }

  proceedToPayment(): void {
    if (!this.selectedTestPack) {
      return;
    }

    // Store the selected test pack in the service for later use
    this.testPackPurchaseService.setSelectedTestPack(this.selectedTestPack);

    // Check if user is authenticated
    if (this.authService.isLoggedIn()) {
      // User is already logged in, proceed directly to payment
      this.router.navigate(['/payment']);
    } else {
      // User needs to authenticate first
      this.router.navigate(['/auth-required'], {
        queryParams: { returnUrl: '/payment' },
      });
    }
  }
}
