import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TestPackPurchaseService } from 'src/app/service/test-pack-purchase.service';
import { AuthService } from 'src/app/service/auth.service';

interface TestPackOption {
  id: number;
  name: string;
  description: string;
  price: number;
  tests: string[];
  imageUrl?: string;
}

@Component({
  selector: 'app-auth-required',
  templateUrl: './auth-required.component.html',
  styleUrls: ['./auth-required.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class AuthRequiredComponent implements OnInit {
  selectedTestPack: TestPackOption | null = null;
  returnUrl: string = '/payment';

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private testPackPurchaseService: TestPackPurchaseService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // Check if user is already logged in
    if (this.authService.isLoggedIn()) {
      // User already logged in, redirect to payment
      this.router.navigate(['/payment']);
      return;
    }

    // Get selected test pack from service
    this.selectedTestPack = this.testPackPurchaseService.getSelectedTestPack();

    // Get return URL from query params
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/payment';

    // If no test pack selected, redirect back to test selection
    if (!this.selectedTestPack) {
      this.router.navigate(['/test-packages']);
      return;
    }
  }

  goToLogin(): void {
    this.router.navigate(['/login/login'], {
      queryParams: { returnUrl: this.returnUrl },
    });
  }

  goToRegister(): void {
    this.router.navigate(['/register/register'], {
      queryParams: { returnUrl: this.returnUrl },
    });
  }

  goBack(): void {
    // Clear selected test pack and go back to selection
    this.testPackPurchaseService.clearSelectedTestPack();
    this.router.navigate(['/test-packages']);
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  }
}
