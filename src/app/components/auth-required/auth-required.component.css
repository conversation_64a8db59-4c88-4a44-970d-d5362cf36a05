.auth-required-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 1rem;
}

.test-pack-summary {
  border-left: 4px solid #ec4899;
}

.auth-options button {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-options button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .auth-required-container {
    padding: 1rem 0.5rem;
  }
  
  .max-w-md {
    margin: 0 auto;
    max-width: 95%;
  }
}
