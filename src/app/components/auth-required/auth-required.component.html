<div class="auth-required-container">
  <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6 mt-8">
    <!-- Header -->
    <div class="text-center mb-6">
      <h2 class="text-2xl font-bold text-gray-800 mb-2"><PERSON><PERSON><PERSON> atau Daftar</h2>
      <p class="text-gray-600">
        Untuk melanjutkan pembelian, Anda perlu masuk ke akun atau membuat akun
        baru
      </p>
    </div>

    <!-- Test Pack Summary -->
    <div
      class="test-pack-summary bg-gray-50 rounded-lg p-4 mb-6"
      *ngIf="selectedTestPack"
    >
      <h3 class="font-semibold text-gray-800 mb-2">Paket yang dipilih:</h3>
      <div class="flex justify-between items-center">
        <div>
          <p class="font-medium text-gray-900">{{ selectedTestPack.name }}</p>
          <p class="text-sm text-gray-600">
            {{ selectedTestPack.description }}
          </p>
          <div class="flex flex-wrap gap-1 mt-2">
            <span
              *ngFor="let test of selectedTestPack.tests"
              class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
            >
              {{ test }}
            </span>
          </div>
        </div>
        <div class="text-right">
          <p class="text-lg font-bold text-pink-600">
            {{ formatPrice(selectedTestPack.price) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Auth Options -->
    <div class="auth-options space-y-3">
      <button
        (click)="goToRegister()"
        class="w-full bg-pink-500 text-white py-3 rounded-lg hover:bg-pink-600 transition-colors font-semibold"
      >
        <i class="fas fa-user-plus mr-2"></i>
        Daftar Akun Baru
      </button>

      <button
        (click)="goToLogin()"
        class="w-full bg-white border-2 border-pink-500 text-pink-500 py-3 rounded-lg hover:bg-pink-50 transition-colors font-semibold"
      >
        <i class="fas fa-sign-in-alt mr-2"></i>
        Sudah Punya Akun? Masuk
      </button>
    </div>

    <!-- Back Button -->
    <div class="text-center mt-6">
      <button
        (click)="goBack()"
        class="text-gray-500 hover:text-gray-700 text-sm underline"
      >
        <i class="fas fa-arrow-left mr-1"></i>
        Kembali ke Pilihan Paket
      </button>
    </div>
  </div>
</div>
