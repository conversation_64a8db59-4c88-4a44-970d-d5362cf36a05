<div class="max-w-5xl mx-auto px-4 py-8">
  <!-- Header Section -->
  <div class="text-center mb-12">
    <h1 class="text-3xl font-bold text-gray-900 mb-4">
      {{ judulReport }}
    </h1>

    <div
      *ngIf="subtitle"
      class="bg-gradient-to-r from-sky-100 to-blue-100 p-4 rounded-lg shadow-sm"
    >
      <h2 class="text-xl font-semibold text-gray-800">{{ subtitle }}</h2>
    </div>
  </div>

  <!-- Grafik Section dengan Card -->
  <div class="bg-white rounded-xl shadow-lg p-6 mb-12">
    <div class="flex justify-center">
      <div class="w-3/4 max-w-3xl">
        <app-disc-chart
          [chartData]="chartData"
          [chartOptions]="chartOptions"
          [chartType]="ChartType"
          class="transition-all duration-300 hover:scale-105"
        ></app-disc-chart>
      </div>
    </div>
  </div>

  <!-- Hasil Analysis Section -->
  <div class="bg-white rounded-xl shadow-lg overflow-hidden">
    <!-- Header Hasil -->
    <div class="bg-gradient-to-r from-sky-500 to-blue-600 p-6">
      <h2 class="text-2xl font-bold text-white mb-2">{{ judulHasil }}</h2>
      <h3 *ngIf="subtitleHasil" class="text-lg text-sky-100">
        {{ subtitleHasil }}
      </h3>
    </div>

    <!-- Konten Hasil -->
    <div class="p-8">
      <div class="prose max-w-none">
        <div
          class="text-gray-700 leading-7 space-y-6 text-lg font-normal"
          [innerHTML]="convertMarkdown(keteranganHasil)"
        ></div>
      </div>
    </div>
  </div>
</div>
