import { ChartType } from 'chart.js';
import { Component, Input } from '@angular/core';
import { marked } from 'marked';

@Component({
  selector: 'app-report-detail',
  templateUrl: './report-detail.component.html',
  styleUrls: ['./report-detail.component.css'],
})
export class ReportDetailComponent {
  @Input() judulReport: string = '';
  @Input() subtitle: string = '';
  @Input() keterangan: string = '';
  @Input() chartData: any;
  @Input() chartOptions: any;
  @Input() ChartType: any;
  @Input() judulHasil: string = '';
  @Input() subtitleHasil: string = '';
  @Input() keteranganHasil: string = '';

  convertMarkdown(text: string) {
    return marked(text);
  }

  ngOnInit() {
    // Additional initialization logic if needed
  }
}
