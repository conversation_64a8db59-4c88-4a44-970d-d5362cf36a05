import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { DynamicNavbarComponent } from './dynamic-navbar.component';

describe('DynamicNavbarComponent', () => {
  let component: DynamicNavbarComponent;
  let fixture: ComponentFixture<DynamicNavbarComponent>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [DynamicNavbarComponent],
      providers: [{ provide: Router, useValue: routerSpy }],
    }).compileComponents();

    fixture = TestBed.createComponent(DynamicNavbarComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should toggle mobile menu', () => {
    expect(component.isMenuOpen).toBeFalse();
    component.toggleMobileMenu();
    expect(component.isMenuOpen).toBeTrue();
    component.toggleMobileMenu();
    expect(component.isMenuOpen).toBeFalse();
  });

  it('should navigate to login page', () => {
    component.login();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/login']);
  });

  it('should navigate to specified path and close mobile menu', () => {
    component.isMenuOpen = true;
    component.navigateTo('/test-packages');

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/test-packages']);
    expect(component.isMenuOpen).toBeFalse();
  });
});
