import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-dynamic-navbar',
  templateUrl: './dynamic-navbar.component.html',
  styleUrls: ['./dynamic-navbar.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class DynamicNavbarComponent {
  isMenuOpen = false;

  constructor(private router: Router) {}

  toggleMobileMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }

  navigateTo(path: string) {
    if (path.includes('?')) {
      // Handle URLs with query parameters
      const [route, queryString] = path.split('?');
      const queryParams: any = {};

      if (queryString) {
        const params = queryString.split('&');
        params.forEach((param) => {
          const [key, value] = param.split('=');
          queryParams[key] = value;
        });
      }

      this.router.navigate([route], { queryParams });
    } else {
      this.router.navigate([path]);
    }
    this.isMenuOpen = false; // Close mobile menu after navigation
  }

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    this.isMenuOpen = false; // Close mobile menu after navigation
  }

  login() {
    this.router.navigate(['/login']);
  }
}
