/* Modern Professional Navbar */

/* Main navbar styling */
.navbar-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.navbar-container.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-lg);
  border-bottom-color: var(--color-gray-300);
}

/* Logo styling */
.logo-container {
  transition: all 0.3s ease;
}

.logo-container:hover {
  transform: scale(1.05);
}

.logo-container img {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Desktop menu links */
.nav-link {
  position: relative;
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-xl);
  transition: all var(--transition-base);
  font-size: var(--font-size-sm);
  letter-spacing: 0.025em;
  text-decoration: none;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  transform: translateX(-50%);
  transition: width var(--transition-base);
}

.nav-link:hover {
  color: var(--color-primary);
  background: var(--color-primary-50);
  transform: translateY(-1px);
}

.nav-link:hover::before {
  width: 80%;
}

/* Professional login button */
.login-button {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-700) 100%);
  color: var(--color-white);
  padding: var(--spacing-3) var(--spacing-8);
  border-radius: var(--radius-xl);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  letter-spacing: 0.025em;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
  border: none;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -4px rgba(102, 126, 234, 0.5);
}

.login-button:hover::before {
  left: 100%;
}

.login-button:active {
  transform: translateY(0);
}

/* Mobile menu button */
.mobile-menu-button {
  padding: 0.5rem;
  border-radius: 0.5rem;
  color: #374151;
  transition: all 0.2s ease;
  border: none;
  background: transparent;
}

.mobile-menu-button:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* Mobile menu styling */
.mobile-menu {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(229, 231, 235, 0.5);
  box-shadow: 0 10px 30px -4px rgba(0, 0, 0, 0.15);
}

.mobile-nav-link {
  display: block;
  padding: 1rem 1.5rem;
  color: #374151;
  font-weight: 500;
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.mobile-nav-link:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #667eea;
  padding-left: 2rem;
}

.mobile-nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.mobile-nav-link:hover::before {
  width: 4px;
}

.mobile-login-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  font-weight: 600;
  text-align: center;
  margin: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 14px -4px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  border: none;
  width: calc(100% - 2rem);
}

.mobile-login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px -4px rgba(102, 126, 234, 0.5);
}

/* Animations */
.navbar-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes slideDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus styles for accessibility */
.nav-link:focus,
.login-button:focus,
.mobile-menu-button:focus,
.mobile-nav-link:focus,
.mobile-login-button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .login-button {
    padding: 0.625rem 1.5rem;
    font-size: 0.875rem;
  }
}
