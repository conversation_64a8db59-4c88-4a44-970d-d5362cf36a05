<div class="about-section">
  <h2>{{ title }}</h2>
  <p>{{ description }}</p>

  <div class="grid grid-cols-3">
    <div *ngFor="let feature of features" class="feature-card">
      <div class="feature-icon">
        <svg
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            [attr.d]="feature.svgIcon"
          />
        </svg>
      </div>
      <h3>{{ feature.title }}</h3>
      <p>{{ feature.description }}</p>
    </div>
  </div>

  <!-- Trust Indicators -->
  <div *ngIf="trustIndicators" class="trust-indicators">
    <h3>{{ trustIndicators.title }}</h3>
    <p>{{ trustIndicators.subtitle }}</p>

    <div class="trust-stats">
      <div *ngFor="let stat of trustIndicators.statistics" class="trust-stat">
        <span class="value">{{ stat.value }}</span>
        <span class="label">{{ stat.label }}</span>
      </div>
    </div>
  </div>
</div>
