/* About Section - Fluid & Elegant Design */

/* Grid Layout - More Organic */
.grid {
  display: grid;
  gap: 2.5rem;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  margin-bottom: 4rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Grid Adjustments */
@media (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: 500px;
  }
}

@media (max-width: 480px) {
  .grid {
    gap: 1.25rem;
  }
}
