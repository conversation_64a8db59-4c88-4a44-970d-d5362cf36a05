import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

interface FeatureCard {
  svgIcon: string;
  title: string;
  description: string;
}

interface TrustStatistic {
  value: string;
  label: string;
}

interface TrustIndicators {
  title: string;
  subtitle: string;
  statistics: TrustStatistic[];
}

@Component({
  selector: 'app-about-section',
  templateUrl: './about-section.component.html',
  styleUrls: ['./about-section.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class AboutSectionComponent {
  @Input() title!: string;
  @Input() description!: string;
  @Input() features!: FeatureCard[];
  @Input() trustIndicators!: TrustIndicators;
  @Input() showOnHomePage: boolean = true;
}
