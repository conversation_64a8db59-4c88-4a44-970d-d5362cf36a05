import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-payment-simulator',
  templateUrl: './payment-simulator.component.html',
  styleUrls: ['./payment-simulator.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule],
})
export class PaymentSimulatorComponent implements OnInit {
  paymentId: string = '';
  isLoading: boolean = false;
  error: string | null = null;

  constructor(private route: ActivatedRoute, private router: Router) {}

  ngOnInit(): void {
    // Get payment ID from route
    this.route.params.subscribe((params) => {
      this.paymentId = params['id'];

      // Also check URL for more complex paths
      if (!this.paymentId) {
        const fullUrl = this.router.url;
        const match = fullUrl.match(/payment-simulator\/(.*)/i);
        if (match && match[1]) {
          this.paymentId = match[1];
        } else {
          this.paymentId = 'mock_payment_default';
        }
      }

      if (!this.paymentId) {
        this.error = 'Payment ID tidak ditemukan';
      }
    });
  }

  simulateSuccessfulPayment(): void {
    this.isLoading = true;

    // Simulate API call delay
    setTimeout(() => {
      this.isLoading = false;
      // Redirect to payment success page
      this.router.navigate(['/payment-success'], {
        queryParams: {
          external_id: this.paymentId,
          status: 'PAID',
        },
      });
    }, 1500);
  }

  simulateFailedPayment(): void {
    this.isLoading = true;

    // Simulate API call delay
    setTimeout(() => {
      this.isLoading = false;
      // Redirect to payment error page
      this.router.navigate(['/payment-error'], {
        queryParams: {
          external_id: this.paymentId,
          status: 'FAILED',
        },
      });
    }, 1500);
  }

  goBack(): void {
    window.history.back();
  }
}
