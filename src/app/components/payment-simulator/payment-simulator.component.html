<div class="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
    <!-- Header -->
    <div class="bg-blue-500 px-6 py-4">
      <h2 class="text-xl font-bold text-white">Simulator Pembayaran</h2>
      <p class="text-white text-sm mt-1">ID: {{ paymentId }}</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="p-6">
      <div class="flex flex-col items-center justify-center py-8">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <p class="text-gray-600">Memproses pembayaran...</p>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="error" class="p-6">
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p>{{ error }}</p>
      </div>
      <div class="flex justify-center mt-4">
        <button 
          (click)="goBack()" 
          class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors">
          Kembali
        </button>
      </div>
    </div>

    <!-- Payment Simulator -->
    <div *ngIf="!isLoading && !error" class="p-6">
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Simulator Pembayaran</h3>
        <p class="text-gray-600 mb-4">
          Ini adalah halaman simulator untuk menguji alur pembayaran. Pilih salah satu opsi di bawah untuk mensimulasikan hasil pembayaran.
        </p>
      </div>

      <div class="flex flex-col space-y-3">
        <button 
          (click)="simulateSuccessfulPayment()" 
          class="w-full bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition-colors font-semibold">
          Simulasi Pembayaran Berhasil
        </button>
        <button 
          (click)="simulateFailedPayment()" 
          class="w-full bg-red-500 text-white py-3 rounded-lg hover:bg-red-600 transition-colors font-semibold">
          Simulasi Pembayaran Gagal
        </button>
        <button 
          (click)="goBack()" 
          class="w-full bg-gray-200 text-gray-700 py-2 rounded-lg hover:bg-gray-300 transition-colors">
          Kembali
        </button>
      </div>
    </div>
  </div>
</div>
