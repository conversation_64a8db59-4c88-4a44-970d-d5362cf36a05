import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TestPackPurchaseService } from 'src/app/service/test-pack-purchase.service';
import { AuthService } from 'src/app/service/auth.service';
import { TestManagerService } from 'src/app/service/test-manager.service';
import { PaymentService } from 'src/app/service/payment.service';

@Component({
  selector: 'app-payment-success',
  templateUrl: './payment-success.component.html',
  styleUrls: ['./payment-success.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class PaymentSuccessComponent implements OnInit {
  isLoading: boolean = true;
  error: string | null = null;
  countdown: number = 5;
  countdownTimer: any;
  testPackId: number | null = null;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private testPackPurchaseService: TestPackPurchaseService,
    private authService: AuthService,
    private testManagerService: TestManagerService,
    private paymentService: PaymentService
  ) {}

  ngOnInit(): void {
    // Check if user is logged in
    if (!this.authService.isLoggedIn()) {
      this.router.navigate(['/login'], {
        queryParams: { returnUrl: '/home/<USER>' },
      });
      return;
    }

    // Get purchase data
    const purchaseData = this.testPackPurchaseService.getPurchaseData();
    console.log('Payment success - Purchase data:', purchaseData);

    // Also check sessionStorage directly for debugging
    const sessionData = sessionStorage.getItem('purchaseData');
    const localData = localStorage.getItem('purchaseDataBackup');
    console.log('Direct sessionStorage check:', sessionData);
    console.log('Direct localStorage backup check:', localData);

    if (!purchaseData) {
      console.error('No purchase data found in sessionStorage or localStorage');
      this.error = 'Data pembelian tidak ditemukan.';
      this.isLoading = false;
      return;
    }

    // Validate that we have the created test pack ID
    if (!purchaseData.createdTestPackId) {
      console.error(
        'Created test pack ID not found in purchase data:',
        purchaseData
      );

      // If createdTestPackId is missing, try to find the most recent test pack for this user
      const user = this.authService.getCurrentUser();
      if (user?.id) {
        console.log('Attempting to find recent test pack for user:', user.id);
        this.findRecentTestPack(user.id);
        return;
      }

      this.error =
        'Test pack ID tidak ditemukan. Silakan hubungi administrator.';
      this.isLoading = false;
      return;
    }

    // Use the correct test pack ID - the one that was actually created in Strapi
    this.testPackId = purchaseData.createdTestPackId;

    // Get payment status from URL parameters
    this.route.queryParams.subscribe((params) => {
      const externalId = params['external_id'];
      const status = params['status'];

      if (externalId && status === 'PAID' && this.testPackId) {
        // Process the successful payment using the created test pack ID
        console.log(
          'Processing payment with test pack ID:',
          this.testPackId,
          'External ID:',
          externalId
        );
        this.processSuccessfulPayment(this.testPackId, externalId);
      } else {
        this.error = 'Status pembayaran tidak valid.';
        this.isLoading = false;
      }
    });
  }

  findRecentTestPack(userId: number): void {
    // Try to find the most recent test pack for this user that hasn't been paid
    this.testManagerService.getRecentTestPackForUser(userId).subscribe({
      next: (testPack: any) => {
        if (testPack && testPack.id) {
          console.log('Found recent test pack:', testPack);
          this.testPackId = testPack.id;

          // Continue with payment processing
          this.route.queryParams.subscribe((params) => {
            const externalId = params['external_id'];
            const status = params['status'];

            if (externalId && status === 'PAID' && this.testPackId) {
              console.log(
                'Processing payment with found test pack ID:',
                this.testPackId
              );
              this.processSuccessfulPayment(this.testPackId, externalId);
            } else {
              this.error = 'Status pembayaran tidak valid.';
              this.isLoading = false;
            }
          });
        } else {
          // If no existing test pack found, create one based on selected test pack
          console.log('No existing test pack found, creating new one');
          this.createTestPackForPayment(userId);
        }
      },
      error: (err: any) => {
        console.error('Error finding recent test pack:', err);
        // Try to create a test pack instead of failing
        this.createTestPackForPayment(userId);
      },
    });
  }

  createTestPackForPayment(userId: number): void {
    const selectedTestPack = this.testPackPurchaseService.getSelectedTestPack();

    if (!selectedTestPack) {
      this.error =
        'Paket tes tidak ditemukan. Silakan pilih paket tes kembali.';
      this.isLoading = false;
      return;
    }

    console.log('Creating test pack for successful payment:', selectedTestPack);

    // Create test pack for the user
    this.testManagerService.createTestPack(userId).subscribe({
      next: (response) => {
        console.log('Test pack created successfully:', response);
        if (response && response.id) {
          const newTestPackId = response.id;

          // Create test access entries for the test pack
          const testAccesses = selectedTestPack.tests.map((type, index) => ({
            type: type as 'DISC' | 'TEAMS' | 'VALUES' | 'MBTI',
            isCompleted: false,
            order: index + 1,
            route: `/home/<USER>
            test_packs: newTestPackId,
          }));

          console.log('Generated test accesses:', testAccesses);

          this.testManagerService
            .assignTestsToTestPack(newTestPackId, testAccesses)
            .subscribe({
              next: (assignResponse) => {
                console.log('Tests assigned successfully:', assignResponse);
                this.testPackId = newTestPackId;

                // Update purchase data with created test pack ID
                const purchaseData =
                  this.testPackPurchaseService.getPurchaseData() || {};
                purchaseData.createdTestPackId = newTestPackId;
                this.testPackPurchaseService.setPurchaseData(purchaseData);

                // Continue with payment processing
                this.route.queryParams.subscribe((params) => {
                  const externalId = params['external_id'];
                  const status = params['status'];

                  if (externalId && status === 'PAID' && this.testPackId) {
                    console.log(
                      'Processing payment with newly created test pack ID:',
                      this.testPackId
                    );
                    this.processSuccessfulPayment(this.testPackId, externalId);
                  } else {
                    this.error = 'Status pembayaran tidak valid.';
                    this.isLoading = false;
                  }
                });
              },
              error: (assignError) => {
                console.error(
                  'Error assigning tests to test pack:',
                  assignError
                );
                this.error =
                  'Gagal mengatur paket tes. Silakan hubungi administrator.';
                this.isLoading = false;
              },
            });
        }
      },
      error: (error) => {
        console.error('Error creating test pack:', error);
        this.error = 'Gagal membuat paket tes. Silakan hubungi administrator.';
        this.isLoading = false;
      },
    });
  }

  processSuccessfulPayment(
    testPackId: number,
    paymentExternalId: string
  ): void {
    const user = this.authService.getCurrentUser();
    const userId = user?.id;

    console.log(
      'Processing payment for user:',
      userId,
      'user email:',
      user?.email,
      'testPackId:',
      testPackId,
      'externalId:',
      paymentExternalId
    );

    if (!userId) {
      this.error = 'User data not found. Please log in again.';
      this.isLoading = false;
      return;
    }

    // Validate that we're using the correct test pack ID
    console.log('Processing payment for test pack ID:', testPackId);

    // Check if this payment has already been processed to prevent duplicate processing
    const purchaseData = this.testPackPurchaseService.getPurchaseData();
    if (purchaseData && purchaseData.paymentProcessed) {
      console.log('Payment already processed, skipping duplicate processing');
      this.testPackPurchaseService.clearAllData();
      this.isLoading = false;
      this.startCountdown();
      return;
    }

    // Just update payment status and link test pack to payment
    this.paymentService
      .linkTestPackToPayment(testPackId, paymentExternalId, userId)
      .subscribe({
        next: () => {
          console.log('Payment linked successfully to test pack:', testPackId);

          // Update test pack status from 'pending' to 'open' after successful payment
          this.testManagerService
            .updateTestPackStatusAndPayment(testPackId, 'open', 'PAID')
            .subscribe({
              next: () => {
                console.log('Test pack status updated to open');
                // Mark payment as processed to prevent duplicate processing
                this.testPackPurchaseService.setPurchaseData({
                  ...purchaseData,
                  paymentProcessed: true,
                });

                // Clear purchase data
                this.testPackPurchaseService.clearPurchaseData();
                this.testPackPurchaseService.clearSelectedTestPack();

                // Start countdown to redirect
                this.isLoading = false;
                this.startCountdown();
              },
              error: (updateError) => {
                console.error('Error updating test pack status:', updateError);
                // Even if status update fails, proceed since payment was successful
                this.testPackPurchaseService.clearPurchaseData();
                this.testPackPurchaseService.clearSelectedTestPack();
                this.isLoading = false;
                this.startCountdown();
              },
            });
        },
        error: (err) => {
          console.error('Error linking test pack to payment:', err);
          // Even if linking fails, we still want to proceed
          this.testPackPurchaseService.clearPurchaseData();
          this.testPackPurchaseService.clearSelectedTestPack();
          this.isLoading = false;
          this.startCountdown();
        },
      });
  }

  startCountdown(): void {
    this.countdown = 5;
    this.countdownTimer = setInterval(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        clearInterval(this.countdownTimer);
        this.router.navigate(['/home/<USER>']);
      }
    }, 1000);
  }

  goToStartTest(): void {
    clearInterval(this.countdownTimer);
    this.router.navigate(['/home/<USER>']);
  }

  ngOnDestroy(): void {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  }
}
