<div class="min-h-screen bg-gray-100 flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
  <div class="max-w-md w-full bg-white rounded-lg shadow-lg overflow-hidden">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="p-8">
      <div class="flex flex-col items-center justify-center py-8">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500 mb-4"></div>
        <p class="text-gray-600">Memproses pembayaran Anda...</p>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="error" class="p-8">
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
        <p>{{ error }}</p>
      </div>
      <div class="flex justify-center">
        <button 
          (click)="goToStartTest()" 
          class="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
          Ke Hal<PERSON> Tes
        </button>
      </div>
    </div>

    <!-- Success State -->
    <div *ngIf="!isLoading && !error" class="p-8">
      <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
          <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-800 mb-2">Pembayaran Berhasil!</h2>
        <p class="text-gray-600 mb-4">
          Terima kasih atas pembelian Anda. Paket tes Anda telah diaktifkan dan siap untuk digunakan.
        </p>
        <div class="bg-blue-50 p-4 rounded-lg mb-6">
          <p class="text-blue-800">
            Anda akan diarahkan ke halaman tes dalam <span class="font-bold">{{ countdown }}</span> detik.
          </p>
        </div>
        <button 
          (click)="goToStartTest()" 
          class="w-full bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 transition-colors font-semibold">
          Mulai Tes Sekarang
        </button>
      </div>
    </div>
  </div>
</div>
