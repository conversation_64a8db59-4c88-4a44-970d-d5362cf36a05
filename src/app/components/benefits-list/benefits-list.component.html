<div class="benefits-container">
  <div class="benefits-content">
    <div class="benefits-header">
      <h2 *ngIf="title" class="benefits-title">
        {{ title }}
      </h2>
      <p *ngIf="subtitle" class="benefits-subtitle">{{ subtitle }}</p>
    </div>

    <div class="benefits-grid">
      <div
        *ngFor="let benefit of benefits"
        class="benefit-card"
        [ngClass]="{
          highlighted: benefit.highlight
        }"
      >
        <div
          class="benefit-icon-container"
          [ngClass]="{
            highlighted: benefit.highlight
          }"
        >
          <div [innerHTML]="benefit.icon" class="benefit-icon"></div>
        </div>
        <div class="benefit-content">
          <h3 class="benefit-title">
            {{ benefit.title }}
          </h3>
          <div
            class="benefit-description"
            [innerHTML]="benefit.description"
          ></div>
        </div>
      </div>
    </div>
  </div>
</div>
