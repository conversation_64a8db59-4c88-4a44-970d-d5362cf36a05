/* Clean Benefits List Component */

.benefits-container {
  background: white;
  padding: 3rem 2rem;
}

.benefits-content {
  max-width: 1200px;
  margin: 0 auto;
}

.benefits-header {
  text-align: center;
  margin-bottom: 3rem;
}

.benefits-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.benefits-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.benefit-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.benefit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #6366f1;
}

.benefit-card.highlighted {
  background: #f8fafc;
  border-color: #6366f1;
}

.benefit-icon-container {
  width: 3rem;
  height: 3rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: #6366f1;
}

.benefit-icon-container.highlighted {
  background: #4f46e5;
}

.benefit-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.benefit-content {
  flex: 1;
}

.benefit-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.benefit-description {
  color: #64748b;
  line-height: 1.6;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .benefits-container {
    padding: 2rem 1rem;
  }
  
  .benefits-title {
    font-size: 2rem;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .benefit-card {
    padding: 1.5rem;
  }
}
