import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Benefit {
  icon: string;
  title: string;
  description: string;
  highlight: boolean;
}

@Component({
  selector: 'app-benefits-list',
  templateUrl: './benefits-list.component.html',
  styleUrls: ['./benefits-list.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class BenefitsListComponent {
  @Input() title!: string;
  @Input() subtitle!: string;
  @Input() benefits!: Benefit[];
}
