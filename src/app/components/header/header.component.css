/* Modern Header Component */
.hero-section {
  padding: var(--spacing-20) var(--spacing-4) var(--spacing-16);
  text-align: center;
  background: linear-gradient(135deg, var(--color-gray-25) 0%, var(--color-white) 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: clamp(var(--font-size-3xl), 6vw, var(--font-size-6xl));
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-6);
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, var(--color-gray-900) 0%, var(--color-gray-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: clamp(var(--font-size-lg), 2.5vw, var(--font-size-xl));
  line-height: var(--line-height-relaxed);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-normal);
  margin: 0;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero-image {
  margin-top: var(--spacing-12);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  transform: perspective(1000px) rotateX(5deg);
  transition: transform var(--transition-slow);
}

.hero-image:hover {
  transform: perspective(1000px) rotateX(0deg) translateY(-8px);
}

.hero-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform var(--transition-slow);
}

.hero-image:hover img {
  transform: scale(1.02);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    padding: var(--spacing-16) var(--spacing-4) var(--spacing-12);
  }

  .hero-title {
    margin-bottom: var(--spacing-4);
  }

  .hero-image {
    margin-top: var(--spacing-8);
    transform: none;
  }

  .hero-image:hover {
    transform: translateY(-4px);
  }
}

@media (max-width: 640px) {
  .hero-section {
    padding: var(--spacing-12) var(--spacing-4) var(--spacing-10);
  }
}
