import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import {
  PaymentService,
  PaymentRequest,
} from 'src/app/service/payment.service';
import { TestPackPurchaseService } from 'src/app/service/test-pack-purchase.service';
import { AuthService } from 'src/app/service/auth.service';

@Component({
  selector: 'app-payment',
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.css'],
  standalone: true,
  imports: [CommonModule],
})
export class PaymentComponent implements OnInit {
  isLoading: boolean = true;
  error: string | null = null;
  paymentUrl: string | null = null;
  testPackName: string = '';
  testPackPrice: number = 0;

  constructor(
    private paymentService: PaymentService,
    private testPackPurchaseService: TestPackPurchaseService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // First, check authentication - this is critical for proper payment flow
    if (!this.authService.isLoggedIn()) {
      this.error =
        'Anda harus masuk terlebih dahulu untuk melakukan pembayaran.';
      this.isLoading = false;

      // Redirect to auth-required page with return URL
      setTimeout(() => {
        this.router.navigate(['/auth-required'], {
          queryParams: { returnUrl: '/payment' },
        });
      }, 2000);
      return;
    }

    // Get selected test pack
    const selectedTestPack = this.testPackPurchaseService.getSelectedTestPack();
    if (!selectedTestPack) {
      this.error =
        'Tidak ada paket tes yang dipilih. Silakan pilih paket tes terlebih dahulu.';
      this.isLoading = false;

      // Redirect back to test packages page after a short delay
      setTimeout(() => {
        this.router.navigate(['/test-packages']);
      }, 2000);
      return;
    }

    this.testPackName = selectedTestPack.name;
    this.testPackPrice = selectedTestPack.price;

    // Create payment request
    this.createPayment(
      selectedTestPack.id,
      selectedTestPack.price,
      selectedTestPack.name
    );
  }

  createPayment(testPackId: number, amount: number, description: string): void {
    // We don't require user to be logged in at this point
    // We'll just create a payment request with placeholder customer info
    // The actual user info will be collected at login or registration

    const externalId = `testpack_${testPackId}_${Date.now()}`;

    // Get user data if logged in, otherwise use placeholder
    const user = this.authService.getCurrentUser();
    const isLoggedIn = this.authService.isLoggedIn();

    const paymentRequest: PaymentRequest = {
      amount: amount,
      description: `Pembayaran untuk ${description}`,
      externalId: externalId,
      customerName:
        isLoggedIn && user?.username ? user.username : 'Guest Customer',
      customerEmail:
        isLoggedIn && user?.email ? user.email : '<EMAIL>',
      successRedirectUrl: `${window.location.origin}/payment-success`,
      failureRedirectUrl: `${window.location.origin}/payment-error`,
    };

    // Store payment data for later reference
    this.testPackPurchaseService.setPurchaseData({
      testPackId: testPackId,
      paymentExternalId: externalId,
      amount: amount,
      timestamp: new Date().toISOString(),
    });

    // Create payment with Tripay and save to Strapi
    this.paymentService.createPayment(paymentRequest).subscribe({
      next: (response) => {
        if (response && response.paymentUrl) {
          this.paymentUrl = response.paymentUrl;
        } else {
          this.error = 'Gagal membuat pembayaran. Silakan coba lagi.';
        }
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error creating payment:', err);
        this.error =
          'Terjadi kesalahan saat membuat pembayaran. Silakan coba lagi.';
        this.isLoading = false;
      },
    });
  }

  redirectToPayment(): void {
    // Check if user is logged in before proceeding to payment
    if (!this.authService.isLoggedIn()) {
      // If not logged in, redirect to login page with return URL
      this.router.navigate(['/login'], {
        queryParams: { returnUrl: '/payment' },
      });
      return;
    }

    // User is logged in, proceed to payment URL
    if (this.paymentUrl) {
      window.location.href = this.paymentUrl;
    }
  }

  goBack(): void {
    this.router.navigate(['/']);
  }
}
