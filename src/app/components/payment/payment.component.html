<div class="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
    <!-- Header -->
    <div class="bg-pink-500 px-6 py-4">
      <h2 class="text-xl font-bold text-white">Pembayaran</h2>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="p-6">
      <div class="flex flex-col items-center justify-center py-8">
        <div
          class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500 mb-4"
        ></div>
        <p class="text-gray-600">Mempersiapkan pembayaran...</p>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="error" class="p-6">
      <div
        class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
      >
        <p>{{ error }}</p>
      </div>
      <div class="flex justify-center mt-4">
        <button
          (click)="goBack()"
          class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors"
        >
          Kembali
        </button>
      </div>
    </div>

    <!-- Payment Details -->
    <div *ngIf="!isLoading && !error" class="p-6">
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">
          Detail Pembayaran
        </h3>
        <div class="border-t border-b border-gray-200 py-4">
          <div class="flex justify-between mb-2">
            <span class="text-gray-600">Paket Tes:</span>
            <span class="font-medium">{{ testPackName }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Jumlah:</span>
            <span class="font-bold text-pink-600"
              >Rp {{ testPackPrice | number }}</span
            >
          </div>
        </div>
      </div>

      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">
          Instruksi Pembayaran
        </h3>
        <ol class="list-decimal pl-5 text-gray-600 space-y-2">
          <li>Klik tombol "Lanjutkan ke Pembayaran" di bawah</li>
          <li>Anda akan diarahkan ke halaman pembayaran Tripay</li>
          <li>Pilih metode pembayaran yang Anda inginkan</li>
          <li>Selesaikan pembayaran sesuai instruksi</li>
          <li>
            Setelah pembayaran berhasil, Anda akan diarahkan kembali ke situs
            kami
          </li>
        </ol>
      </div>

      <div class="flex flex-col space-y-3">
        <button
          (click)="redirectToPayment()"
          class="w-full bg-pink-500 text-white py-3 rounded-lg hover:bg-pink-600 transition-colors font-semibold"
        >
          Lanjutkan ke Pembayaran
        </button>
        <button
          (click)="goBack()"
          class="w-full bg-gray-200 text-gray-700 py-2 rounded-lg hover:bg-gray-300 transition-colors"
        >
          Kembali
        </button>
      </div>
    </div>
  </div>
</div>
