/* Modern Payment Page Styles */

.payment-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-gray-25) 0%, var(--color-white) 100%);
  padding: var(--spacing-8) var(--spacing-4);
}

.payment-content {
  max-width: 1000px;
  margin: 0 auto;
}

.payment-header {
  text-align: center;
  margin-bottom: var(--spacing-12);
  padding: var(--spacing-8);
  background: var(--color-white);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.payment-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.payment-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  margin: 0;
}

.payment-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--spacing-8);
}

.payment-form-section {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
}

.payment-summary {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  height: fit-content;
  position: sticky;
  top: var(--spacing-8);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.section-icon {
  width: var(--spacing-8);
  height: var(--spacing-8);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--font-size-lg);
}

.payment-methods {
  display: grid;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.payment-method {
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  background: var(--color-white);
}

.payment-method:hover {
  border-color: var(--color-primary-300);
  background: var(--color-primary-25);
}

.payment-method.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-50);
  box-shadow: var(--shadow-md);
}

.payment-method.selected::after {
  content: '✓';
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  width: 24px;
  height: 24px;
  background: var(--color-primary);
  color: var(--color-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
}

.payment-method-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-3);
}

.payment-method-icon {
  width: 48px;
  height: 48px;
  background: var(--color-gray-100);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}

.payment-method-info h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0 0 var(--spacing-1) 0;
}

.payment-method-info p {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin: 0;
}

.payment-method-description {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

.order-summary {
  border-bottom: 1px solid var(--color-gray-200);
  padding-bottom: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-3);
}

.order-item-info h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0 0 var(--spacing-1) 0;
}

.order-item-info p {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin: 0;
}

.order-item-price {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
}

.price-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-base);
}

.price-row.subtotal {
  color: var(--color-gray-600);
}

.price-row.discount {
  color: var(--color-success);
}

.price-row.total {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  padding-top: var(--spacing-4);
  border-top: 2px solid var(--color-gray-200);
}

.payment-button {
  width: 100%;
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-6);
}

.payment-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-base);
}

.payment-button:hover {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.payment-button:hover::before {
  left: 100%;
}

.payment-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.security-info {
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  border: 1px solid var(--color-gray-200);
}

.security-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.security-text {
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.payment-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-8);
  position: relative;
}

.payment-steps::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-gray-200);
  z-index: 1;
}

.payment-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: var(--color-white);
  padding: 0 var(--spacing-4);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-gray-200);
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-2);
  transition: all var(--transition-fast);
}

.payment-step.active .step-number {
  background: var(--color-primary);
  color: var(--color-white);
}

.payment-step.completed .step-number {
  background: var(--color-success);
  color: var(--color-white);
}

.step-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  text-align: center;
}

.payment-step.active .step-label {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

/* Loading States */
.payment-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-12);
  gap: var(--spacing-4);
}

.payment-loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.payment-loading-text {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .payment-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }

  .payment-summary {
    position: static;
    order: -1;
  }
}

@media (max-width: 768px) {
  .payment-container {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .payment-header {
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-8);
  }

  .payment-title {
    font-size: var(--font-size-3xl);
  }

  .payment-form-section,
  .payment-summary {
    padding: var(--spacing-6);
  }

  .payment-steps {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .payment-steps::before {
    display: none;
  }

  .payment-method {
    padding: var(--spacing-4);
  }

  .payment-method-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-3);
  }
}

@media (max-width: 640px) {
  .payment-title {
    font-size: var(--font-size-2xl);
  }

  .section-title {
    font-size: var(--font-size-xl);
  }
}
