import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Material Modules - gunakan SharedModule sebagai gantinya
import { SharedModule } from './shared/shared.module';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

// Standalone components
// import { DynamicPageComponent } from './pages/dynamic-page/dynamic-page.component';
import { HomeMemberComponent } from './pages/home-member/home-member.component';
import { WelcomingMemberComponent } from './pages/welcoming-member/welcoming-member.component';
import { FooterComponent } from './components/footer/footer.component';
import { FullBannerComponent } from './components/full-banner/full-banner.component';
import { MemberMenuBarComponent } from './components/member-menu-bar/member-menu-bar.component';
import { GrafikTestComponent } from './pages/grafik-test/grafik-test.component';
import { AuthInterceptor } from './interceptor/auth.interceptor';
import { StartTestPageComponent } from './pages/start-test-page/start-test-page.component';
// Standalone components
// import { CustomerTestSelectionComponent } from './components/customer-test-selection/customer-test-selection.component';
// import { PaymentComponent } from './components/payment/payment.component';
// import { PaymentSuccessComponent } from './components/payment-success/payment-success.component';
// import { PaymentErrorComponent } from './components/payment-error/payment-error.component';

@NgModule({
  declarations: [
    AppComponent,
    FooterComponent,
    HomeMemberComponent,
    WelcomingMemberComponent,
    FullBannerComponent,
    MemberMenuBarComponent,
    GrafikTestComponent,
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    FormsModule, // Add FormsModule here to fix ngModel errors
    ReactiveFormsModule,
    SharedModule, // Gunakan SharedModule untuk Material dan komponen bersama
    AppRoutingModule,
    StartTestPageComponent,
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
