import { NgModule } from '@angular/core';
import { RouterModule, Routes, PreloadAllModules } from '@angular/router';
import { AuthGuard } from './auth.guard';
import { AdminGuard } from './admin.guard';
import { LoginComponent } from './auth/login/login.component';
import { DynamicPageComponent } from './pages/dynamic-page/dynamic-page.component';
import { HomeMemberComponent } from './pages/home-member/home-member.component';
import { WelcomingMemberComponent } from './pages/welcoming-member/welcoming-member.component';
import { StartTestPageComponent } from './pages/start-test-page/start-test-page.component';
import { CustomerTestSelectionComponent } from './components/customer-test-selection/customer-test-selection.component';
import { PaymentComponent } from './components/payment/payment.component';
import { PaymentSuccessComponent } from './components/payment-success/payment-success.component';
import { PaymentErrorComponent } from './components/payment-error/payment-error.component';
import { PaymentSimulatorComponent } from './components/payment-simulator/payment-simulator.component';
import { PaymentHistoryComponent } from './components/payment-history/payment-history.component';
import { PaymentDetailComponent } from './components/payment-detail/payment-detail.component';
import { AuthRequiredComponent } from './components/auth-required/auth-required.component';
import { UserTestResultsComponent } from './components/user-test-results/user-test-results.component';
import { TestPackReportComponent } from './features/admin/test-pack-report/test-pack-report.component';
import { MemberTestPackSelectionComponent } from './components/member-test-pack-selection/member-test-pack-selection.component';

// Make sure all components are properly imported

const routes: Routes = [
  { path: '', component: DynamicPageComponent },
  {
    path: 'login',
    loadChildren: () => import('./auth/auth.module').then((m) => m.AuthModule),
  },
  {
    path: 'register',
    loadChildren: () => import('./auth/auth.module').then((m) => m.AuthModule),
  },
  { path: 'test-packages', component: CustomerTestSelectionComponent },
  { path: 'auth-required', component: AuthRequiredComponent },
  { path: 'payment', component: PaymentComponent },
  { path: 'payment-success', component: PaymentSuccessComponent },
  { path: 'payment-error', component: PaymentErrorComponent },
  { path: 'payment-simulator/:id', component: PaymentSimulatorComponent },
  // Wildcard route for handling more complex simulator URLs
  {
    path: 'payment-simulator/**',
    redirectTo: 'payment-simulator/mock_payment_default',
  },
  {
    path: 'home',
    component: HomeMemberComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        component: WelcomingMemberComponent,
        canActivate: [AuthGuard],
      },
      {
        path: 'start-test',
        component: StartTestPageComponent,
        canActivate: [AuthGuard],
      },
      {
        path: 'payment-history',
        component: PaymentHistoryComponent,
        canActivate: [AuthGuard],
      },
      {
        path: 'payment-detail/:id',
        component: PaymentDetailComponent,
        canActivate: [AuthGuard],
      },
      {
        path: 'test-results',
        component: UserTestResultsComponent,
        canActivate: [AuthGuard],
      },
      {
        path: 'buy-test-pack',
        component: MemberTestPackSelectionComponent,
        canActivate: [AuthGuard],
      },
      {
        path: 'test-pack-report/:id',
        component: TestPackReportComponent,
        canActivate: [AuthGuard],
      },
      {
        path: 'disc',
        loadChildren: () =>
          import('./features/disc/disc.module').then((m) => m.DiscModule),
        canActivate: [AuthGuard],
      },
      {
        path: 'teams',
        loadChildren: () =>
          import('./features/teams/teams.module').then((m) => m.TeamsModule),
        canActivate: [AuthGuard],
      },
      {
        path: 'values',
        loadChildren: () =>
          import('./features/values/values.module').then((m) => m.ValuesModule),
        canActivate: [AuthGuard],
      },
      {
        path: 'mbti',
        loadChildren: () =>
          import('./features/mbti/mbti.module').then((m) => m.MbtiModule),
        canActivate: [AuthGuard],
      },
      {
        path: 'admin',
        loadChildren: () =>
          import('./features/admin/admin.module').then((m) => m.AdminModule),
        canActivate: [AuthGuard, AdminGuard],
      },
    ],
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      preloadingStrategy: PreloadAllModules,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
