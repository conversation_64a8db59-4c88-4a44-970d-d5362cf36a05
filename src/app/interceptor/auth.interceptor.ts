import { Injectable } from '@angular/core';
import {
  <PERSON>ttpRequest,
  <PERSON>ttpHandler,
  HttpEvent,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../service/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService) {}

  intercept(
    req: HttpRequest<unknown>,
    next: <PERSON>ttpHandler
  ): Observable<HttpEvent<unknown>> {
    const token = this.authService.getToken(); // Ambil JWT token dari AuthService

    // List of endpoints that don't require authentication
    const publicEndpoints = [
      '/api/home-page',
      '/api/about-page',
      '/api/why-page',
      '/api/auth/local',
      '/api/auth/local/register',
    ];

    // Check if current request is to a public endpoint
    const isPublicEndpoint = publicEndpoints.some((endpoint) =>
      req.url.includes(endpoint)
    );

    // Only add token for non-public endpoints and when token exists
    if (token && !isPublicEndpoint) {
      const clonedReq = req.clone({
        headers: req.headers.set('Authorization', `Bearer ${token}`),
      });
      return next.handle(clonedReq);
    }

    return next.handle(req);
  }
}
