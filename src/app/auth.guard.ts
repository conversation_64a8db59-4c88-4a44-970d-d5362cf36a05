import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from './service/auth.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(private router: Router, private authService: AuthService) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    // Use AuthService for more robust authentication check
    const isAuthenticated = this.authService.isLoggedIn();

    if (!isAuthenticated) {
      // Redirect to auth-required page with return URL
      this.router.navigate(['/auth-required'], {
        queryParams: { returnUrl: state.url },
      });
      return false;
    }

    return true;
  }
}
