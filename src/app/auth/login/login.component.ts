import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from 'src/app/service/auth.service';
import { TestPackPurchaseService } from 'src/app/service/test-pack-purchase.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
})
export class LoginComponent implements OnInit {
  email: string = '';
  password: string = '';
  errorMessage: string = '';
  returnUrl: string = '/home/<USER>';

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private testPackPurchaseService: TestPackPurchaseService
  ) {}

  ngOnInit(): void {
    // Get return URL from route parameters
    this.route.queryParams.subscribe((params) => {
      this.returnUrl = params['returnUrl'] || this.getDefaultRedirectUrl();
    });
  }

  private getDefaultRedirectUrl(): string {
    // Check if user is coming from test pack selection
    const selectedTestPack = this.testPackPurchaseService.getSelectedTestPack();
    if (selectedTestPack) {
      // User selected a test pack, should go to start-test after login
      return '/home/<USER>';
    }
    // Default redirect to test results
    return '/home/<USER>';
  }

  login() {
    this.errorMessage = '';

    if (!this.email || !this.password) {
      this.errorMessage = 'Email dan password harus diisi';
      return;
    }

    this.authService.login(this.email, this.password).subscribe(
      (response) => {
        // Jika login berhasil, simpan token JWT dan data user
        if (response && response.jwt && response.user) {
          this.authService.saveUserData(response.jwt, response.user);

          // Ambil role user dari Strapi jika user ID tersedia
          if (response.user.id) {
            this.authService.getUserRole(response.user.id).subscribe(
              (role) => {
                // Check if there's a selected test pack to create
                this.createTestPackIfNeeded(response.user.id);
              },
              (error) => {
                // Tetap lanjutkan meskipun gagal mengambil role
                this.createTestPackIfNeeded(response.user.id);
              }
            );
          } else {
            this.createTestPackIfNeeded(response.user.id);
          }
        } else {
          this.errorMessage = 'Format respons login tidak valid';
        }
      },
      (error) => {
        if (error.status === 401) {
          this.errorMessage = 'Email atau password salah. Silakan coba lagi.';
        } else if (error.status === 0) {
          this.errorMessage =
            'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.';
        } else {
          this.errorMessage = `Login gagal: ${
            error.error?.message || error.message || 'Unknown error'
          }`;
        }
      }
    );
  }

  private createTestPackIfNeeded(userId: number): void {
    const selectedTestPack = this.testPackPurchaseService.getSelectedTestPack();

    if (selectedTestPack) {
      // ✅ PERBAIKAN: Jangan buat test pack di sini untuk menghindari duplikasi
      // Test pack akan dibuat di PaymentSuccessComponent setelah payment berhasil
      console.log(
        'User logged in with selected test pack:',
        userId,
        selectedTestPack
      );

      // Store user info and selected test pack for payment process
      const existingPurchaseData =
        this.testPackPurchaseService.getPurchaseData();
      const purchaseData = {
        testPackId: selectedTestPack.id,
        userId: userId,
        userLoggedIn: true,
        // Preserve existing payment data if available
        paymentExternalId: existingPurchaseData?.paymentExternalId,
        amount: existingPurchaseData?.amount,
        timestamp: existingPurchaseData?.timestamp || new Date().toISOString(),
      };

      console.log('Storing purchase data for payment:', purchaseData);
      this.testPackPurchaseService.setPurchaseData(purchaseData);

      // Navigate to return URL (usually payment)
      this.router.navigateByUrl(this.returnUrl);
    } else {
      // No test pack selected, just navigate normally
      this.router.navigateByUrl(this.returnUrl);
    }
  }
}
