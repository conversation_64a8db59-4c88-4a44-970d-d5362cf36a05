/* Modern Auth Page Styles */

.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-white) 50%, var(--color-secondary-50) 100%);
  padding: var(--spacing-8) var(--spacing-4);
  position: relative;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.auth-card {
  background: var(--color-white);
  border-radius: var(--radius-3xl);
  padding: var(--spacing-10);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--color-gray-200);
  width: 100%;
  max-width: 450px;
  position: relative;
  z-index: 1;
  animation: slideUp 0.6s ease-out;
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.auth-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-4);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  box-shadow: var(--shadow-lg);
}

.auth-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-2);
}

.auth-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin: 0;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.form-group {
  position: relative;
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.form-input {
  width: 100%;
  padding: var(--spacing-4) var(--spacing-4);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  font-family: var(--font-family-primary);
  background-color: var(--color-white);
  transition: all var(--transition-fast);
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  background-color: var(--color-primary-25);
}

.form-input:invalid {
  border-color: var(--color-error);
}

.form-input:invalid:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.form-input-icon {
  position: relative;
}

.form-input-icon input {
  padding-left: var(--spacing-12);
}

.form-icon {
  position: absolute;
  left: var(--spacing-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-400);
  font-size: var(--font-size-lg);
  pointer-events: none;
  transition: color var(--transition-fast);
}

.form-input:focus + .form-icon {
  color: var(--color-primary);
}

.form-error {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.password-toggle {
  position: absolute;
  right: var(--spacing-4);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  font-size: var(--font-size-lg);
  transition: color var(--transition-fast);
}

.password-toggle:hover {
  color: var(--color-gray-600);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: var(--spacing-4) 0;
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.form-checkbox input {
  width: 18px;
  height: 18px;
  accent-color: var(--color-primary);
}

.form-checkbox label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  cursor: pointer;
}

.forgot-password {
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
}

.forgot-password:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}

.auth-button {
  width: 100%;
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-base);
}

.auth-button:hover {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.auth-button:hover::before {
  left: 100%;
}

.auth-button:active {
  transform: translateY(0);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.auth-button-loading {
  position: relative;
  color: transparent;
}

.auth-button-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid var(--color-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: var(--spacing-6) 0;
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--color-gray-200);
}

.auth-divider::before {
  margin-right: var(--spacing-4);
}

.auth-divider::after {
  margin-left: var(--spacing-4);
}

.social-auth {
  display: flex;
  gap: var(--spacing-3);
}

.social-button {
  flex: 1;
  padding: var(--spacing-3);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  background: var(--color-white);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.social-button:hover {
  border-color: var(--color-gray-300);
  background: var(--color-gray-50);
  transform: translateY(-1px);
}

.auth-footer {
  text-align: center;
  margin-top: var(--spacing-8);
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
}

.auth-footer-text {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
}

.auth-footer-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: color var(--transition-fast);
}

.auth-footer-link:hover {
  color: var(--color-primary-600);
  text-decoration: underline;
}

/* Success/Error Messages */
.auth-message {
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.auth-message-success {
  background: var(--color-success-light);
  color: var(--color-success);
  border: 1px solid var(--color-success);
}

.auth-message-error {
  background: var(--color-error-light);
  color: var(--color-error);
  border: 1px solid var(--color-error);
}

/* Responsive Design */
@media (max-width: 640px) {
  .auth-container {
    padding: var(--spacing-4);
  }

  .auth-card {
    padding: var(--spacing-8);
  }

  .auth-title {
    font-size: var(--font-size-2xl);
  }

  .social-auth {
    flex-direction: column;
  }
}