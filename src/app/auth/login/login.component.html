<div class="min-h-screen flex items-center justify-center bg-gray-100">
  <div class="bg-white shadow-lg rounded-lg p-8 w-full max-w-sm">
    <h2 class="text-2xl font-semibold text-gray-700 text-center mb-6">Login</h2>

    <div class="mb-4">
      <label class="block text-gray-600 mb-2">Email:</label>
      <input
        [(ngModel)]="email"
        type="email"
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
        required
      />
    </div>

    <div class="mb-4">
      <label class="block text-gray-600 mb-2">Password:</label>
      <input
        [(ngModel)]="password"
        type="password"
        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
        required
      />
    </div>

    <div *ngIf="errorMessage" class="text-red-500 text-sm mb-4">
      {{ errorMessage }}
    </div>

    <button
      (click)="login()"
      class="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600 transition duration-200"
    >
      Login
    </button>

    <!-- Register Link -->
    <div class="mt-6 text-center">
      <p class="text-gray-600">
        Belum memiliki akun?
        <a
          routerLink="/login/register"
          class="text-blue-500 hover:text-blue-700 cursor-pointer"
          >Daftar di sini</a
        >
      </p>
    </div>
  </div>
</div>
