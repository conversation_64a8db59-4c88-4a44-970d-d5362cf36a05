<div class="min-h-screen flex items-center justify-center bg-gray-100">
  <div class="bg-white shadow-lg rounded-lg p-8 w-full max-w-md">
    <h2 class="text-2xl font-semibold text-gray-700 text-center mb-6">Daftar Akun Baru</h2>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="space-y-4">
      <!-- Username Field -->
      <div>
        <label class="block text-gray-600 mb-2">Username:</label>
        <input
          formControlName="username"
          type="text"
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
          [ngClass]="{'border-red-500': username?.invalid && (username?.dirty || username?.touched)}"
        />
        <div *ngIf="username?.invalid && (username?.dirty || username?.touched)" class="text-red-500 text-sm mt-1">
          <div *ngIf="username?.errors?.['required']">Username wajib diisi.</div>
          <div *ngIf="username?.errors?.['minlength']">Username minimal 3 karakter.</div>
        </div>
      </div>

      <!-- Email Field -->
      <div>
        <label class="block text-gray-600 mb-2">Email:</label>
        <input
          formControlName="email"
          type="email"
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
          [ngClass]="{'border-red-500': email?.invalid && (email?.dirty || email?.touched)}"
        />
        <div *ngIf="email?.invalid && (email?.dirty || email?.touched)" class="text-red-500 text-sm mt-1">
          <div *ngIf="email?.errors?.['required']">Email wajib diisi.</div>
          <div *ngIf="email?.errors?.['email']">Format email tidak valid.</div>
        </div>
      </div>

      <!-- Password Field -->
      <div>
        <label class="block text-gray-600 mb-2">Password:</label>
        <input
          formControlName="password"
          type="password"
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
          [ngClass]="{'border-red-500': password?.invalid && (password?.dirty || password?.touched)}"
        />
        <div *ngIf="password?.invalid && (password?.dirty || password?.touched)" class="text-red-500 text-sm mt-1">
          <div *ngIf="password?.errors?.['required']">Password wajib diisi.</div>
          <div *ngIf="password?.errors?.['minlength']">Password minimal 6 karakter.</div>
        </div>
      </div>

      <!-- Confirm Password Field -->
      <div>
        <label class="block text-gray-600 mb-2">Konfirmasi Password:</label>
        <input
          formControlName="confirmPassword"
          type="password"
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
          [ngClass]="{'border-red-500': (confirmPassword?.invalid || registerForm.hasError('passwordMismatch')) && (confirmPassword?.dirty || confirmPassword?.touched)}"
        />
        <div *ngIf="(confirmPassword?.invalid || registerForm.hasError('passwordMismatch')) && (confirmPassword?.dirty || confirmPassword?.touched)" class="text-red-500 text-sm mt-1">
          <div *ngIf="confirmPassword?.errors?.['required']">Konfirmasi password wajib diisi.</div>
          <div *ngIf="registerForm.hasError('passwordMismatch')">Password tidak cocok.</div>
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="errorMessage" class="text-red-500 text-sm py-2">
        {{ errorMessage }}
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        [disabled]="registerForm.invalid || isSubmitting"
        class="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600 transition duration-200 disabled:bg-blue-300 disabled:cursor-not-allowed"
      >
        <span *ngIf="isSubmitting" class="inline-block mr-2">
          <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </span>
        {{ isSubmitting ? 'Mendaftar...' : 'Daftar' }}
      </button>
    </form>

    <!-- Login Link -->
    <div class="mt-6 text-center">
      <p class="text-gray-600">
        Sudah memiliki akun?
        <a (click)="goToLogin()" class="text-blue-500 hover:text-blue-700 cursor-pointer">Login di sini</a>
      </p>
    </div>
  </div>
</div>
