import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from 'src/app/service/auth.service';
import { TestPackPurchaseService } from 'src/app/service/test-pack-purchase.service';
import { TestManagerService } from 'src/app/service/test-manager.service';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css'],
})
export class RegisterComponent {
  registerForm: FormGroup;
  isSubmitting = false;
  errorMessage: string | null = null;
  returnUrl: string = '/home/<USER>';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private testPackPurchaseService: TestPackPurchaseService,
    private testManagerService: TestManagerService
  ) {
    // Initialize the form with validators
    this.registerForm = this.fb.group(
      {
        username: ['', [Validators.required, Validators.minLength(3)]],
        email: ['', [Validators.required, Validators.email]],
        password: ['', [Validators.required, Validators.minLength(6)]],
        confirmPassword: ['', Validators.required],
      },
      {
        validators: this.passwordMatchValidator,
      }
    );

    // Get return URL from route parameters or default to '/home/<USER>'
    this.route.queryParams.subscribe((params) => {
      this.returnUrl = params['returnUrl'] || '/home/<USER>';
    });
  }

  // Custom validator to check if passwords match
  passwordMatchValidator(group: FormGroup) {
    const password = group.get('password')?.value;
    const confirmPassword = group.get('confirmPassword')?.value;
    return password === confirmPassword ? null : { passwordMismatch: true };
  }

  // Getter methods for easy access to form fields
  get username() {
    return this.registerForm.get('username');
  }
  get email() {
    return this.registerForm.get('email');
  }
  get password() {
    return this.registerForm.get('password');
  }
  get confirmPassword() {
    return this.registerForm.get('confirmPassword');
  }

  onSubmit() {
    if (this.registerForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = null;

    const { username, email, password } = this.registerForm.value;

    this.authService.register(username, email, password).subscribe({
      next: (response) => {
        // If registration is successful, automatically log the user in
        if (response && response.jwt && response.user) {
          this.authService.saveUserData(response.jwt, response.user);

          // Check if there's a selected test pack to create
          this.createTestPackIfNeeded(response.user.id);
        } else {
          this.errorMessage = 'Format respons registrasi tidak valid';
          this.isSubmitting = false;
        }
      },
      error: (error) => {
        console.error('Registration error:', error);
        if (error.error && error.error.message) {
          // Handle specific error messages from the API
          if (error.error.message.includes('Email is already taken')) {
            this.errorMessage =
              'Email sudah terdaftar. Silakan gunakan email lain.';
          } else if (
            error.error.message.includes('Username is already taken')
          ) {
            this.errorMessage =
              'Username sudah digunakan. Silakan pilih username lain.';
          } else {
            this.errorMessage = error.error.message;
          }
        } else {
          this.errorMessage =
            'Terjadi kesalahan saat mendaftar. Silakan coba lagi.';
        }
        this.isSubmitting = false;
      },
    });
  }

  goToLogin() {
    this.router.navigate(['/login'], {
      queryParams: { returnUrl: this.returnUrl },
    });
  }

  private createTestPackIfNeeded(userId: number): void {
    const selectedTestPack = this.testPackPurchaseService.getSelectedTestPack();

    if (selectedTestPack) {
      // ✅ PERBAIKAN: Jangan buat test pack di sini untuk menghindari duplikasi
      // Test pack akan dibuat di PaymentSuccessComponent setelah payment berhasil
      console.log(
        'User registered with selected test pack:',
        userId,
        selectedTestPack
      );

      // Store user info and selected test pack for payment process
      const purchaseData = {
        testPackId: selectedTestPack.id,
        userId: userId,
        userLoggedIn: true,
        timestamp: new Date().toISOString(),
      };

      console.log('Storing purchase data for payment:', purchaseData);
      this.testPackPurchaseService.setPurchaseData(purchaseData);

      this.isSubmitting = false;
      // Navigate to return URL (usually payment)
      this.router.navigate([this.returnUrl]);
    } else {
      // No test pack selected, just navigate normally
      this.isSubmitting = false;
      this.router.navigate([this.returnUrl]);
    }
  }
}
