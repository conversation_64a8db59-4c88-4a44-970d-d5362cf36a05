/* Modern Register Page Styles */
/* Inherits from login styles */

@import '../login/login.component.css';

/* Register-specific customizations */
.auth-card {
  max-width: 500px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.terms-checkbox {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin: var(--spacing-4) 0;
}

.terms-checkbox input {
  margin-top: var(--spacing-1);
}

.terms-text {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
}

.terms-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.terms-link:hover {
  text-decoration: underline;
}

@media (max-width: 640px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}
