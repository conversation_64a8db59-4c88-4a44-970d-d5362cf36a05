import { Component, OnInit } from '@angular/core';
import { DiscReportService, DiscReportData } from 'src/app/service/disc-report.service';

@Component({
  selector: 'app-disc-report-example',
  templateUrl: './disc-report-example.component.html',
  styleUrls: ['./disc-report-example.component.css']
})
export class DiscReportExampleComponent implements OnInit {
  // Contoh 1: Menggunakan answerId
  answerId: number = 1; // Ganti dengan ID yang valid

  // Contoh 2: Menggunakan reportData yang sudah disiapkan
  reportData?: DiscReportData;

  constructor(private discReportService: DiscReportService) { }

  ngOnInit(): void {
    // Contoh menyiapkan data laporan secara manual
    // Ini hanya contoh, dalam kasus nyata Anda mungkin mendapatkan data dari API lain
    const mostCounts = { D: 7, I: 5, S: 3, C: 2 };
    const leastCounts = { D: 2, I: 3, S: 5, C: 7 };
    const difference = { 
      D: mostCounts.D - leastCounts.D,
      I: mostCounts.I - leastCounts.I,
      S: mostCounts.S - leastCounts.S,
      C: mostCounts.C - leastCounts.C
    };

    // Membuat data laporan menggunakan service
    this.reportData = this.discReportService.createDiscReport(
      mostCounts,
      leastCounts,
      difference
    );
  }
}
