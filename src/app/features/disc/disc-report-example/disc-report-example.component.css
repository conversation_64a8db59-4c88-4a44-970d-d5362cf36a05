.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.example-container {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

h2 {
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

/* Pastikan chart di example juga memiliki tinggi yang sama */
:host ::ng-deep .chart-container {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:host ::ng-deep .chart-wrapper {
  min-height: 380px;
}

:host ::ng-deep canvas {
  height: 380px !important;
  max-height: 100%;
}
