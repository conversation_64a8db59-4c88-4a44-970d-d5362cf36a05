# DISC Report Component

Komponen ini digunakan untuk menampilkan laporan DISC berdasarkan hasil tes DISC.

## Cara Penggunaan

Ada dua cara untuk menggunakan komponen `DiscDetailReportComponent`:

### 1. Menggunakan ID Jawaban

Jika Anda memiliki ID jawaban DISC, Anda dapat menggunakan komponen dengan cara berikut:

```html
<app-disc-detail-report [answerId]="answerId"></app-disc-detail-report>
```

Dimana `answerId` adalah ID jawaban DISC yang ingin ditampilkan laporannya.

### 2. Menggunakan Data Laporan yang Sudah Disiapkan

Jika Anda sudah memiliki data laporan DISC, Anda dapat menggunakan komponen dengan cara berikut:

```html
<app-disc-detail-report [reportData]="reportData"></app-disc-detail-report>
```

Dimana `reportData` adalah objek yang berisi data laporan DISC dengan struktur sebagai berikut:

```typescript
interface DiscReportData {
  dataSource: DiscDataSource[];
  mostString: string;
  leastString: string;
  differenceString: string;
  mostChartData: ChartData<'line'>;
  leastChartData: ChartData<'line'>;
  differenceChartData: ChartData<'line'>;
  mostChartOptions: ChartOptions;
  leastChartOptions: ChartOptions;
  differenceChartOptions: ChartOptions;
  chartType: ChartType;
}
```

## Contoh Penggunaan

Anda dapat melihat contoh penggunaan komponen di `DiscReportExampleComponent` yang dapat diakses melalui rute `/home/<USER>/example`.

## Service

Komponen ini menggunakan `DiscReportService` untuk memproses data laporan DISC. Service ini menyediakan dua metode utama:

1. `getDiscReportByAnswerId(answerId: number)`: Mendapatkan data laporan DISC berdasarkan ID jawaban
2. `createDiscReport(mostCounts, leastCounts, difference)`: Membuat data laporan DISC dari data yang sudah ada

## Keuntungan

Dengan menggunakan komponen ini, Anda dapat:

1. Menampilkan laporan DISC dengan mudah di berbagai bagian aplikasi
2. Menghindari duplikasi kode untuk menampilkan laporan DISC
3. Memisahkan logika bisnis dari tampilan
4. Menggunakan kembali komponen dengan data yang berbeda
