import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DiscDetailReportComponent } from './disc-detail-report/disc-detail-report.component';
import { DiscManualComponent } from './disc-manual/disc-manual.component';
import { DiscResultsComponent } from './disc-results/disc-results.component';
import { DiscTestPageComponent } from './disc-test-page/disc-test-page.component';

const routes: Routes = [
  { path: '', component: DiscTestPageComponent },
  { path: 'manual', component: DiscManualComponent },
  { path: 'results', component: DiscResultsComponent },
  { path: 'report/:id', component: DiscDetailReportComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DiscRoutingModule {}
