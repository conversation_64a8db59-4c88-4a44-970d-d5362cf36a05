import { NgModule } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';

import { DiscRoutingModule } from './disc-routing.module';
import { DiscDetailReportComponent } from './disc-detail-report/disc-detail-report.component';
import { DiscManualComponent } from './disc-manual/disc-manual.component';
import { DiscResultsComponent } from './disc-results/disc-results.component';
import { DiscTestPageComponent } from './disc-test-page/disc-test-page.component';

import { DiscAnalyzerComponent } from 'src/app/components/disc-analyzer/disc-analyzer.component';
import { DiscInputComponent } from 'src/app/components/disc-input/disc-input.component';

import { DiscQuestionaireComponent } from 'src/app/components/disc-questionaire/disc-questionaire.component';

@NgModule({
  declarations: [
    DiscTestPageComponent,
    DiscResultsComponent,
    DiscDetailReportComponent,
    DiscManualComponent,

    DiscAnalyzerComponent,
    DiscInputComponent,
    DiscQuestionaireComponent,
  ],
  imports: [SharedModule, DiscRoutingModule],
})
export class DiscModule {}
