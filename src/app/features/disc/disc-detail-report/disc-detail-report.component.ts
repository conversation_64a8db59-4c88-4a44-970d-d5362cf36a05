import { Component, OnInit, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ChartOptions, ChartType, ChartData } from 'chart.js';
import {
  DiscReportService,
  DiscReportData,
  DiscDataSource,
} from 'src/app/service/disc-report.service';

@Component({
  selector: 'app-disc-detail-report',
  templateUrl: './disc-detail-report.component.html',
  styleUrls: ['./disc-detail-report.component.css'],
})
export class DiscDetailReportComponent implements OnInit {
  // Input properties untuk menerima data dari komponen lain
  @Input() answerId?: number;
  @Input() reportData?: DiscReportData;

  // Properties yang digunakan di template
  displayedColumns: string[] = ['category', 'most', 'least', 'difference'];
  dataSource: DiscDataSource[] = [];
  mostString: string = '';
  leastString: string = '';
  differenceString: string = '';
  mostChartData!: ChartData<'line'>;
  leastChartData!: ChartData<'line'>;
  differenceChartData!: ChartData<'line'>;
  mostChartOptions!: ChartOptions;
  leastChartOptions!: ChartOptions;
  differenceChartOptions!: ChartOptions;
  chartType: ChartType = 'line';
  chartLegend = true;

  constructor(
    private route: ActivatedRoute,
    private discReportService: DiscReportService
  ) {}

  ngOnInit(): void {
    // Jika reportData sudah disediakan melalui @Input, gunakan itu
    if (this.reportData) {
      this.updateReportData(this.reportData);
      return;
    }

    // Jika answerId disediakan melalui @Input, gunakan itu
    let idToUse = this.answerId;

    // Jika tidak ada answerId dari @Input, coba ambil dari route params
    if (!idToUse && this.route.snapshot.params['id']) {
      idToUse = this.route.snapshot.params['id'];
    }

    // Jika ada ID yang valid, ambil data laporan
    if (idToUse) {
      this.discReportService
        .getDiscReportByAnswerId(idToUse)
        .subscribe((reportData) => {
          this.updateReportData(reportData);
        });
    }
  }

  /**
   * Memperbarui data laporan yang ditampilkan
   * @param reportData Data laporan DISC
   */
  private updateReportData(reportData: DiscReportData): void {
    this.dataSource = reportData.dataSource;
    this.mostString = reportData.mostString;
    this.leastString = reportData.leastString;
    this.differenceString = reportData.differenceString;
    this.mostChartData = reportData.mostChartData;
    this.leastChartData = reportData.leastChartData;
    this.differenceChartData = reportData.differenceChartData;
    this.mostChartOptions = reportData.mostChartOptions;
    this.leastChartOptions = reportData.leastChartOptions;
    this.differenceChartOptions = reportData.differenceChartOptions;
    this.chartType = reportData.chartType;
  }
}
