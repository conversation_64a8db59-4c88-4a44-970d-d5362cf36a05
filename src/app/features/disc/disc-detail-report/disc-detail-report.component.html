<div class="max-w-5xl mx-auto px-4 py-8">
  <!-- Header Section -->
  <div class="text-center mb-12">
    <h1 class="text-3xl font-bold text-gray-900 mb-4">DISC Report</h1>
  </div>
  <div class="report-container">
    <h1>Quisionaire Results</h1>
    <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
      <!-- Category Column -->
      <ng-container matColumnDef="category">
        <th mat-header-cell *matHeaderCellDef>Category</th>
        <td mat-cell *matCellDef="let element">{{ element.category }}</td>
      </ng-container>

      <!-- Most Column -->
      <ng-container matColumnDef="most">
        <th mat-header-cell *matHeaderCellDef>Most</th>
        <td mat-cell *matCellDef="let element">{{ element.most }}</td>
      </ng-container>

      <!-- Least Column -->
      <ng-container matColumnDef="least">
        <th mat-header-cell *matHeaderCellDef>Least</th>
        <td mat-cell *matCellDef="let element">{{ element.least }}</td>
      </ng-container>

      <!-- Difference Column -->
      <ng-container matColumnDef="difference">
        <th mat-header-cell *matHeaderCellDef>Difference</th>
        <td mat-cell *matCellDef="let element">{{ element.difference }}</td>
      </ng-container>

      <!-- Header and Row Declarations -->
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>

  <div class="report-container">
    <h2 class="text-xl font-semibold mb-4">DISC Detail Report</h2>

    <div class="flex justify-around mt-8">
      <div class="w-1/3">
        <h3 class="text-center mb-2 font-medium">MOST</h3>
        <div class="chart-container">
          <app-disc-chart
            [chartData]="mostChartData"
            [chartOptions]="mostChartOptions"
            [chartType]="chartType"
          ></app-disc-chart>
        </div>
      </div>
      <div class="w-1/3">
        <h3 class="text-center mb-2 font-medium">LEAST</h3>
        <div class="chart-container">
          <app-disc-chart
            [chartData]="leastChartData"
            [chartOptions]="leastChartOptions"
            [chartType]="chartType"
          ></app-disc-chart>
        </div>
      </div>
      <div class="w-1/3">
        <h3 class="text-center mb-2 font-medium">DIFFERENCE</h3>
        <div class="chart-container">
          <app-disc-chart
            [chartData]="differenceChartData"
            [chartOptions]="differenceChartOptions"
            [chartType]="chartType"
          ></app-disc-chart>
        </div>
      </div>
    </div>
  </div>

  <div class="report-container">
    <app-disc-analyzer [discString]="differenceString"></app-disc-analyzer>
  </div>
</div>
