.report-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

table {
  width: 100%;
}

.mat-header-cell {
  font-weight: bold;
}

.mat-elevation-z8 {
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
}

/* Styling untuk container chart */
.flex {
  display: flex;
  min-height: 400px; /* Tinggi minimum untuk container chart */
}

.w-1\/3 {
  width: 33.333%;
  min-height: 400px; /* Tinggi minimum untuk setiap chart container */
  padding: 10px;
}

/* Styling untuk chart container */
.chart-container {
  height: 400px; /* Tinggi container chart */
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Override canvas height */
:host ::ng-deep canvas {
  width: 100% !important;
  height: 380px !important; /* Tinggi chart */
  max-height: 100%;
}