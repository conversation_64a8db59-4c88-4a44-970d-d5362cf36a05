import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AnswerService } from 'src/app/service/answer.service';
import { AuthService } from 'src/app/service/auth.service';
import { QuestionGroupService } from 'src/app/service/question-group.service';
import { TestManagerService } from 'src/app/service/test-manager.service';

@Component({
  selector: 'app-disc-test-page',
  templateUrl: './disc-test-page.component.html',
  styleUrls: ['./disc-test-page.component.css'],
})
export class DiscTestPageComponent implements OnInit {
  questionGroups: any[] = [];

  constructor(
    private data: QuestionGroupService,
    private authService: AuthService,
    private answerService: AnswerService,
    private testManager: TestManagerService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Ambil data dari API Strapi
    this.data.getQuestionGroups().subscribe(
      (response: any) => {
        const fetchedData = response.data;

        // <PERSON><PERSON> apakah fetchedData dan fetchedData.questions ada
        if (
          fetchedData &&
          fetchedData[0] &&
          fetchedData[0].attributes &&
          fetchedData[0].attributes.questions &&
          fetchedData[0].attributes.questions.data
        ) {
          // Susun ulang sesuai format yang diinginkan
          this.questionGroups = fetchedData.map((group: any) => ({
            id: group.id, // Ambil ID Group
            questions: group.attributes.questions.data.map((q: any) => ({
              id: q.id,
              text: q.attributes.text,
              mostValue: q.attributes.mostValue,
              leastValue: q.attributes.leastValue,
            })),
            selectedMost: null,
            selectedLeast: null,
            warning: '',
          }));
        } else {
          console.error('Questions data not found in API response.');
        }
      },
      (error) => {
        console.error('Error fetching question groups:', error);
      }
    );
  }

  // Fungsi untuk menangani jawaban dari DiscQuestionnaireComponent
  handleFormSubmit(answers: any[]) {
    const userId = this.authService.getCurrentUser().id;
    const currentTest = this.testManager.getCurrentTest();

    // Tambahkan log untuk debugging
    console.log('DISC Answers to be saved:', answers);
    console.log('User ID:', userId);
    console.log('Current Test:', currentTest);

    // Jika tidak ada currentTest, tetap simpan jawaban tanpa testManagerId
    this.answerService.saveAnswers(answers, userId, currentTest?.id).subscribe(
      (response: any) => {
        console.log('DISC Save Response:', response);
        // Tambahkan pengecekan untuk menangani respons null
        if (response && response.data) {
          const resultId = response.data?.id;

          // Jika currentTest null, tangani dengan cara berbeda
          if (currentTest) {
            // Gunakan resultId saat menandai test selesai
            this.testManager.markCurrentTestComplete(resultId);
            this.testManager.moveToNextTest();
          } else {
            // Jika tidak ada currentTest, navigasi ke halaman hasil atau dashboard
            console.log('Test completed with resultId:', resultId);
            // Tambahkan navigasi ke halaman hasil DISC
            this.navigateToResults(resultId);
          }
        } else {
          console.error('Invalid response format from API:', response);
          // Tambahkan penanganan error untuk respons null
          this.handleSaveError('Respons dari server tidak valid');
        }
      },
      (error) => {
        console.error('Error saving DISC answers:', error);
        this.handleSaveError('Gagal menyimpan jawaban DISC');
      }
    );
  }

  // Tambahkan fungsi untuk menangani error saat menyimpan
  handleSaveError(message: string) {
    // Tampilkan pesan error ke pengguna (bisa menggunakan alert, toast, atau komponen lain)
    alert(message + '. Silakan coba lagi.');

    // Opsional: Tambahkan logika untuk mencoba kembali atau tindakan lain
  }

  // Tambahkan fungsi untuk navigasi ke halaman hasil
  navigateToResults(resultId: number) {
    // Navigasi ke halaman hasil DISC dengan ID hasil
    this.router.navigate(['/home/<USER>/report', resultId]);
  }
}
