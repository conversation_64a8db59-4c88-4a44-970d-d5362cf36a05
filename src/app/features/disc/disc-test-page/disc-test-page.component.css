/* Modern Assessment Page Styles */

.assessment-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-gray-25) 0%, var(--color-white) 100%);
  padding: var(--spacing-8) var(--spacing-4);
}

.assessment-content {
  max-width: 900px;
  margin: 0 auto;
}

.assessment-header {
  text-align: center;
  margin-bottom: var(--spacing-12);
  padding: var(--spacing-8);
  background: var(--color-white);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.assessment-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.assessment-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  max-width: 600px;
  margin: 0 auto;
}

.assessment-progress {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-8);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.progress-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
}

.progress-counter {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  background: var(--color-gray-100);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-full);
}

.progress-bar-container {
  background: var(--color-gray-200);
  border-radius: var(--radius-full);
  height: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.question-card {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-base);
  animation: slideUp 0.6s ease-out;
}

.question-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.question-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: var(--color-white);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-4);
}

.question-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-6);
}

.answer-options {
  display: grid;
  gap: var(--spacing-4);
}

.answer-option {
  padding: var(--spacing-4);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--color-white);
  position: relative;
  overflow: hidden;
}

.answer-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left var(--transition-base);
}

.answer-option:hover {
  border-color: var(--color-primary-300);
  background: var(--color-primary-25);
  transform: translateX(4px);
}

.answer-option:hover::before {
  left: 100%;
}

.answer-option.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-50);
  box-shadow: var(--shadow-md);
}

.answer-option.selected::after {
  content: '✓';
  position: absolute;
  top: var(--spacing-2);
  right: var(--spacing-4);
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
}

.answer-text {
  font-size: var(--font-size-base);
  color: var(--color-gray-700);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.answer-option.selected .answer-text {
  color: var(--color-primary-700);
  font-weight: var(--font-weight-medium);
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-8);
  padding: var(--spacing-6);
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.nav-button {
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-xl);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  min-width: 120px;
  justify-content: center;
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.nav-button-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}

.nav-button-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.nav-button-secondary {
  background: var(--color-white);
  color: var(--color-gray-700);
  border: 2px solid var(--color-gray-300);
}

.nav-button-secondary:hover:not(:disabled) {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
  transform: translateY(-1px);
}

.assessment-timer {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4) var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  text-align: center;
}

.timer-text {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-1);
}

.timer-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  font-family: var(--font-family-mono);
}

.assessment-instructions {
  background: var(--color-info-light);
  border: 1px solid var(--color-info);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.instructions-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-info);
  margin-bottom: var(--spacing-3);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.instructions-text {
  color: var(--color-info);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(4px);
}

.loading-spinner-large {
  width: 60px;
  height: 60px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .assessment-container {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .assessment-header {
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-8);
  }

  .assessment-title {
    font-size: var(--font-size-3xl);
  }

  .question-card {
    padding: var(--spacing-6);
  }

  .question-text {
    font-size: var(--font-size-lg);
  }

  .navigation-buttons {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .nav-button {
    width: 100%;
  }

  .answer-options {
    gap: var(--spacing-3);
  }

  .answer-option {
    padding: var(--spacing-3);
  }
}

@media (max-width: 640px) {
  .assessment-container {
    padding: var(--spacing-4) var(--spacing-4);
  }

  .assessment-title {
    font-size: var(--font-size-2xl);
  }

  .question-card {
    padding: var(--spacing-4);
  }

  .progress-header {
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: flex-start;
  }
}