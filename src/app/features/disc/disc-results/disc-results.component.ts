import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/service/auth.service';
import { DiscResultService } from 'src/app/service/disc-result.service';

@Component({
  selector: 'app-disc-results',
  templateUrl: './disc-results.component.html',
  styleUrls: ['./disc-results.component.css'],
})
export class DiscResultsComponent implements OnInit {
  displayedColumns: string[] = ['createdAt', 'detail'];
  dataSource = new MatTableDataSource<any>();
  isAdmin = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private discResultService: DiscResultService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Cek apakah user adalah admin
    const user = this.authService.getCurrentUser();
    this.isAdmin = user.role === 'admin';

    // Jika admin, tambahkan kolom username
    if (this.isAdmin) {
      this.displayedColumns = ['username', 'createdAt', 'detail'];
    }

    this.loadDiscResults();
  }

  loadDiscResults() {
    const user = this.authService.getCurrentUser();

    // Jika admin, ambil semua hasil
    if (this.isAdmin) {
      this.discResultService.getAllDiscAnswers().subscribe((response: any) => {
        const discResults = response.data.map((item: any) => ({
          id: item.id,
          createdAt: item.attributes.createdAt,
          username: item.attributes.user?.data?.attributes?.username || 'N/A',
        }));

        this.dataSource.data = discResults;
        this.dataSource.paginator = this.paginator;
      });
    } else {
      // Jika bukan admin, hanya ambil hasil user tersebut
      this.discResultService
        .getDiscAnswersByUser(user.id)
        .subscribe((response: any) => {
          const discResults = response.data.map((item: any) => ({
            id: item.id,
            createdAt: item.attributes.createdAt,
          }));

          this.dataSource.data = discResults;
          this.dataSource.paginator = this.paginator;
        });
    }
  }

  viewDetail(answerId: number) {
    if (answerId) {
      this.router.navigate(['/home/<USER>/report/', answerId]);
    }
  }
}
