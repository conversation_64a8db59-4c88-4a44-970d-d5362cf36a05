/* Modern Results Page Styles */

.results-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-gray-25) 0%, var(--color-white) 100%);
  padding: var(--spacing-8) var(--spacing-4);
}

.results-content {
  max-width: 1200px;
  margin: 0 auto;
}

.results-header {
  text-align: center;
  margin-bottom: var(--spacing-12);
  padding: var(--spacing-10);
  background: var(--color-white);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-gray-200);
  position: relative;
  overflow: hidden;
}

.results-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.results-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.results-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  max-width: 700px;
  margin: 0 auto var(--spacing-6);
  position: relative;
  z-index: 1;
}

.completion-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  background: var(--color-success-light);
  color: var(--color-success);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  border: 1px solid var(--color-success);
  position: relative;
  z-index: 1;
}

.results-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
}

.result-card {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.result-card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.result-icon {
  width: var(--spacing-16);
  height: var(--spacing-16);
  border-radius: var(--radius-xl);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  box-shadow: var(--shadow-md);
}

.result-card-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.result-card-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin: 0;
}

.score-display {
  text-align: center;
  margin-bottom: var(--spacing-6);
}

.score-value {
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-extrabold);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: var(--spacing-2);
}

.score-label {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

.progress-ring {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto var(--spacing-4);
}

.progress-ring svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-ring-bg {
  fill: none;
  stroke: var(--color-gray-200);
  stroke-width: 8;
}

.progress-ring-fill {
  fill: none;
  stroke: url(#gradient);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dasharray var(--transition-slow);
}

.progress-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
}

.chart-container {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  margin-bottom: var(--spacing-8);
}

.chart-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.chart-wrapper {
  position: relative;
  height: 400px;
  margin-bottom: var(--spacing-6);
}

.insights-section {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
  margin-bottom: var(--spacing-8);
}

.insights-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
}

.insight-card {
  background: var(--color-gray-50);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-fast);
}

.insight-card:hover {
  background: var(--color-white);
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.insight-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-3);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.insight-description {
  color: var(--color-gray-700);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.strengths-weaknesses {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

.strength-weakness-card {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-gray-200);
}

.strength-card {
  border-top: 4px solid var(--color-success);
}

.weakness-card {
  border-top: 4px solid var(--color-warning);
}

.strength-weakness-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.strength-title {
  color: var(--color-success);
}

.weakness-title {
  color: var(--color-warning);
}

.strength-weakness-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.strength-weakness-item {
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-2);
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
  border-left: 3px solid;
  transition: all var(--transition-fast);
}

.strength-item {
  border-left-color: var(--color-success);
}

.weakness-item {
  border-left-color: var(--color-warning);
}

.strength-weakness-item:hover {
  background: var(--color-white);
  box-shadow: var(--shadow-sm);
  transform: translateX(4px);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  margin-top: var(--spacing-12);
  flex-wrap: wrap;
}

.action-button {
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-xl);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  text-decoration: none;
  min-width: 160px;
  justify-content: center;
}

.action-button-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}

.action-button-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.action-button-secondary {
  background: var(--color-white);
  color: var(--color-gray-700);
  border: 2px solid var(--color-gray-300);
  box-shadow: var(--shadow-sm);
}

.action-button-secondary:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
  transform: translateY(-1px);
}

/* Animations */
.result-card {
  animation: slideUp 0.6s ease-out;
}

.result-card:nth-child(1) { animation-delay: 0.1s; }
.result-card:nth-child(2) { animation-delay: 0.2s; }
.result-card:nth-child(3) { animation-delay: 0.3s; }
.result-card:nth-child(4) { animation-delay: 0.4s; }

.score-value {
  animation: countUp 1.5s ease-out 0.5s both;
}

.insight-card {
  animation: fadeIn 0.8s ease-out;
}

.insight-card:nth-child(1) { animation-delay: 0.2s; }
.insight-card:nth-child(2) { animation-delay: 0.4s; }
.insight-card:nth-child(3) { animation-delay: 0.6s; }

/* Loading States */
.results-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: var(--spacing-4);
}

.results-loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.results-loading-text {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .results-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }

  .strengths-weaknesses {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }
}

@media (max-width: 768px) {
  .results-container {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .results-header {
    padding: var(--spacing-8);
    margin-bottom: var(--spacing-8);
  }

  .results-title {
    font-size: var(--font-size-4xl);
  }

  .results-subtitle {
    font-size: var(--font-size-lg);
  }

  .result-card {
    padding: var(--spacing-6);
  }

  .result-card-title {
    font-size: var(--font-size-xl);
  }

  .score-value {
    font-size: var(--font-size-5xl);
  }

  .chart-wrapper {
    height: 300px;
  }

  .insights-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-button {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 640px) {
  .results-header {
    padding: var(--spacing-6);
  }

  .results-title {
    font-size: var(--font-size-3xl);
  }

  .result-card-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-3);
  }

  .result-icon {
    width: var(--spacing-12);
    height: var(--spacing-12);
    font-size: var(--font-size-xl);
  }

  .score-value {
    font-size: var(--font-size-4xl);
  }

  .progress-ring {
    width: 100px;
    height: 100px;
  }

  .chart-wrapper {
    height: 250px;
  }
}