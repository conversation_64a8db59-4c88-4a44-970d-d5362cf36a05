import { Component, OnInit } from '@angular/core';
import { ValuesService, ValuesReport, ValuesAnalysis } from 'src/app/service/values.service';

@Component({
  selector: 'app-values-report-example',
  templateUrl: './values-report-example.component.html',
  styleUrls: ['./values-report-example.component.css']
})
export class ValuesReportExampleComponent implements OnInit {
  // Contoh 1: Menggunakan answerId
  answerId: number = 1; // Ganti dengan ID yang valid

  // Contoh 2: Menggunakan data yang sudah disiapkan
  valuesReport?: ValuesReport;
  valuesAnalysis?: ValuesAnalysis;

  // Loading state
  loading = false;
  error = false;
  errorMessage = '';

  constructor(private valuesService: ValuesService) { }

  ngOnInit(): void {
    // Contoh menyiapkan data secara manual
    this.prepareManualData();
  }

  /**
   * Menyiapkan data laporan dan analisis secara manual
   */
  private prepareManualData(): void {
    // Contoh data kategori
    const categoryTotals = {
      L: 35,
      E: 25,
      P: 20,
      J: 30
    };

    // Buat data chart
    const chartData = this.valuesService.getValuesChartData(categoryTotals);
    
    // Dapatkan kategori tertinggi
    const highestCategories = this.valuesService.getHighestCategory(categoryTotals);

    // Buat laporan
    this.valuesReport = {
      categoryTotals,
      highestCategories,
      chartData
    };

    // Buat analisis
    this.valuesAnalysis = {
      mainCategory: 'L',
      mainCategoryName: 'Loyalty',
      mainCategoryValue: 35,
      analysisDetail: 'Anda memiliki kecenderungan yang kuat pada nilai Loyalty. Orang dengan nilai Loyalty yang tinggi sangat menghargai kesetiaan, komitmen, dan hubungan jangka panjang. Mereka cenderung memprioritaskan kepentingan kelompok di atas kepentingan pribadi dan memiliki dedikasi yang kuat terhadap orang-orang dan organisasi yang mereka anggap penting.'
    };
  }
}
