# Values Report Component

Komponen ini digunakan untuk menampilkan laporan Values (Loyal<PERSON>, Equality, Personal Freedom, Justice) berdasarkan hasil tes Values.

## Cara Penggunaan

Ada tiga cara untuk menggunakan komponen `ValuesDetailReportComponent`:

### 1. Menggunakan ID Jawaban

Jika Anda memiliki ID jawaban Values, Anda dapat menggunakan komponen dengan cara berikut:

```html
<app-values-detail-report [answerId]="answerId"></app-values-detail-report>
```

Dimana `answerId` adalah ID jawaban Values yang ingin ditampilkan laporannya.

### 2. Menggunakan Data Laporan yang Sudah Disiapkan

Jika Anda sudah memiliki data laporan dan analisis Values, Anda dapat menggunakan komponen dengan cara berikut:

```html
<app-values-detail-report 
  [valuesReport]="valuesReport" 
  [valuesAnalysis]="valuesAnalysis">
</app-values-detail-report>
```

Dimana:
- `valuesReport` adalah objek yang berisi data laporan Values dengan struktur `ValuesReport`
- `valuesAnalysis` adalah objek yang berisi data analisis Values dengan struktur `ValuesAnalysis`

### 3. Menggunakan Route Parameter

Anda juga dapat mengakses komponen melalui route dengan parameter ID:

```
/home/<USER>/report/:id
```

## Service

Komponen ini menggunakan `ValuesService` untuk memproses data laporan Values. Service ini menyediakan beberapa metode utama:

1. `calculateReport(answers)`: Menghitung total nilai untuk setiap kategori Values
2. `getValuesChartData(categoryTotals)`: Mendapatkan data chart berdasarkan hasil perhitungan
3. `getValuesChartOptions()`: Mendapatkan opsi chart default untuk Values
4. `getHighestCategory(categoryTotals)`: Mendapatkan kategori dengan nilai tertinggi
5. `getValuesReportById(answerId)`: Mendapatkan laporan Values lengkap berdasarkan ID jawaban
6. `getValuesAnalysis(highestCategories)`: Mendapatkan analisis Values lengkap berdasarkan kategori tertinggi
7. `getCompleteValuesReport(answerId)`: Mendapatkan laporan dan analisis Values lengkap berdasarkan ID jawaban

## Contoh Penggunaan

Anda dapat melihat contoh penggunaan komponen di `ValuesReportExampleComponent` yang dapat diakses melalui rute `/home/<USER>/example`.

## Struktur Data

### ValuesReport

```typescript
interface ValuesReport {
  categoryTotals: Record<Category, number>;
  highestCategories: CategoryResult[];
  chartData: ChartData<'line'>;
}
```

### ValuesAnalysis

```typescript
interface ValuesAnalysis {
  mainCategory: string;
  mainCategoryName: string;
  mainCategoryValue: number;
  analysisDetail: string;
}
```

## Keuntungan

Dengan menggunakan komponen ini, Anda dapat:

1. Menampilkan laporan Values dengan mudah di berbagai bagian aplikasi
2. Menghindari duplikasi kode untuk menampilkan laporan Values
3. Memisahkan logika bisnis dari tampilan
4. Menggunakan kembali komponen dengan data yang berbeda
