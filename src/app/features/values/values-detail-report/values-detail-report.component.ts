import { ChartData, ChartOptions, ChartType } from 'chart.js';
import { Component, OnInit, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import {
  ValuesService,
  ValuesReport,
  ValuesAnalysis,
} from 'src/app/service/values.service';

@Component({
  selector: 'app-values-detail-report',
  templateUrl: './values-detail-report.component.html',
  styleUrls: ['./values-detail-report.component.css'],
})
export class ValuesDetailReportComponent implements OnInit {
  // Input properties untuk menerima data dari komponen lain
  @Input() answerId?: number;
  @Input() valuesReport?: ValuesReport;
  @Input() valuesAnalysis?: ValuesAnalysis;

  // Properties untuk tampilan
  judulReport = 'Report Values';
  keterangan = 'Keterangan';
  judulHasil = 'Judul Hasil';
  keteranganHasil = 'Keterangan Hasil';

  public valuesChartData: ChartData<'line'> | undefined;
  public valuesLabels: string[] = ['L', 'E', 'P', 'J'];
  public chartOptions: ChartOptions;
  public ChartType: ChartType = 'line';
  public chartLegend = false;

  // Loading state
  loading = false;
  error = false;
  errorMessage = '';

  constructor(
    private valuesService: ValuesService,
    private route: ActivatedRoute
  ) {
    // Gunakan opsi chart default dari service
    this.chartOptions = this.valuesService.getValuesChartOptions();
  }

  ngOnInit(): void {
    // Jika valuesReport dan valuesAnalysis sudah disediakan melalui @Input, gunakan itu
    if (this.valuesReport && this.valuesAnalysis) {
      this.updateReportData(this.valuesReport, this.valuesAnalysis);
      return;
    }

    // Jika answerId disediakan melalui @Input, gunakan itu
    let idToUse = this.answerId;

    // Jika tidak ada answerId dari @Input, coba ambil dari route params
    if (!idToUse && this.route.snapshot.params['id']) {
      idToUse = this.route.snapshot.params['id'];
    }

    // Jika ada ID yang valid, ambil data laporan lengkap
    if (idToUse) {
      this.loading = true;
      this.error = false;

      this.valuesService.getCompleteValuesReport(idToUse).subscribe({
        next: (data) => {
          this.updateReportData(data.report, data.analysis);
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading Values report:', error);
          this.loading = false;
          this.error = true;
          this.errorMessage =
            'Terjadi kesalahan saat memuat laporan. Silakan coba lagi nanti.';
        },
      });
    }
  }

  /**
   * Memperbarui data laporan yang ditampilkan
   * @param report Data laporan Values
   * @param analysis Data analisis Values
   */
  private updateReportData(
    report: ValuesReport,
    analysis: ValuesAnalysis
  ): void {
    // Update chart data
    this.valuesChartData = report.chartData;

    // Update analysis data
    this.judulHasil = analysis.mainCategoryName;
    this.keteranganHasil = analysis.analysisDetail;
  }
}
