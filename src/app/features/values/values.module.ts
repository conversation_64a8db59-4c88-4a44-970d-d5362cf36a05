import { NgModule } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';

import { ValuesRoutingModule } from 'src/app/features/values/values-routing.module';
import { ValuesTestPageComponent } from './values-test-page/values-test-page.component';
import { ValuesDetailReportComponent } from './values-detail-report/values-detail-report.component';
import { ValuesResultsComponent } from './values-results/values-results.component';
import { ValuesReportExampleComponent } from './values-report-example/values-report-example.component';

@NgModule({
  declarations: [
    ValuesTestPageComponent,
    ValuesDetailReportComponent,
    ValuesResultsComponent,
    ValuesReportExampleComponent,
  ],
  imports: [SharedModule, ValuesRoutingModule],
})
export class ValuesModule {}
