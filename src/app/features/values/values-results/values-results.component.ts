import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/service/auth.service';
import { ValuesService } from 'src/app/service/values.service';

@Component({
  selector: 'app-values-results',
  templateUrl: './values-results.component.html',
  styleUrls: ['./values-results.component.css'],
})
export class ValuesResultsComponent implements OnInit {
  displayedColumns: string[] = ['createdAt', 'detail'];
  dataSource = new MatTableDataSource<any>();
  isAdmin = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private valuesService: ValuesService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Cek apakah user adalah admin
    const user = this.authService.getCurrentUser();
    this.isAdmin = user.role === 'admin';

    // Jika admin, tambahkan kolom username
    if (this.isAdmin) {
      this.displayedColumns = ['username', 'createdAt', 'detail'];
    }

    this.loadValuesResults();
  }

  loadValuesResults() {
    const user = this.authService.getCurrentUser();

    // Jika admin, ambil semua hasil
    if (this.isAdmin) {
      this.valuesService.getAllResults().subscribe((results: any) => {
        const valuesResults = results.data.map((item: any) => ({
          id: item.id,
          createdAt: item.attributes.createdAt,
          username: item.attributes.user?.data?.attributes?.username || 'N/A',
        }));
        this.dataSource.data = valuesResults;
        this.dataSource.paginator = this.paginator;
      });
    } else {
      // Jika bukan admin, hanya ambil hasil user tersebut
      this.valuesService.getResult(user.id).subscribe((results: any) => {
        const valuesResults = results.data.map((item: any) => ({
          id: item.id,
          createdAt: item.attributes.createdAt,
        }));
        this.dataSource.data = valuesResults;
        this.dataSource.paginator = this.paginator;
      });
    }
  }

  viewDetail(resultId: number) {
    if (resultId) {
      this.router.navigate(['/home/<USER>/report/', resultId]);
    }
  }
}
