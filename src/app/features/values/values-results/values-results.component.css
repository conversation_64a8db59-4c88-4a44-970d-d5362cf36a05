/* Modern Values Results Page Styles */
/* Inherits from DISC results styles with Values-specific customizations */

@import '../../../features/disc/disc-results/disc-results.component.css';

/* Values-specific color overrides */
.results-title,
.score-value {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.result-card::before {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.result-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.action-button-primary {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.action-button-primary:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}

/* Values Priority Badge */
.values-priority-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: var(--color-white);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
  box-shadow: var(--shadow-sm);
}