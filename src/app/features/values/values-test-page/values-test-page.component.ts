import { Component, OnInit } from '@angular/core';
import { AuthService } from 'src/app/service/auth.service';
import { TestManagerService } from 'src/app/service/test-manager.service';
import { ValuesService } from 'src/app/service/values.service';

interface AnswerOption {
  text: string;
  value: number;
  category: string;
}

interface AnswerType {
  data: {
    id: number;
    attributes: {
      title: string;
      AnswerOption: AnswerOption[];
    };
  };
}

interface Question {
  id: number;
  attributes: {
    question: string;
    category: string;
    answer_type: AnswerType;
  };
}
@Component({
  selector: 'app-values-test-page',
  templateUrl: './values-test-page.component.html',
  styleUrls: ['./values-test-page.component.css'],
})
export class ValuesTestPageComponent implements OnInit {
  pertanyaan: Question[] = [];
  answers: { id: number; value: number; answer: string; category: string }[] =
    [];

  constructor(
    private valuesQuestion: ValuesService,
    private authService: AuthService,
    private testManager: TestManagerService
  ) {}

  ngOnInit(): void {
    this.valuesQuestion.getQuestion().subscribe((response: any) => {
      this.pertanyaan = response.data;
    });
  }

  handleSubmitAnswers(answers: any[]) {
    // Anda bisa melakukan proses lebih lanjut di sini, misalnya mengirim jawaban ke server
    this.submitAnswers(answers);
  }

  submitAnswers(answers: any[]) {
    const userId = this.authService.getCurrentUser().id;
    const currentTest = this.testManager.getCurrentTest();

    // Gunakan testManagerId saat menyimpan jawaban
    this.valuesQuestion.saveAnswer(userId, answers, currentTest?.id).subscribe(
      (response: any) => {
        const resultId = response.data?.id;
        // Gunakan resultId saat menandai test selesai
        this.testManager.markCurrentTestComplete(resultId);
        this.testManager.moveToNextTest();
      },
      (error) => {
        console.error('Error saving VALUES answers:', error);
      }
    );
  }
}
