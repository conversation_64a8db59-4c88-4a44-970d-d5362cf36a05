/* Modern Values Assessment Page Styles */
/* Inherits from DISC styles with Values-specific customizations */

@import '../../../features/disc/disc-test-page/disc-test-page.component.css';

/* Values-specific color overrides */
.assessment-title {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.question-number {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.progress-bar {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.answer-option.selected {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
}

.answer-option.selected::after {
  color: #f59e0b;
}

.answer-option.selected .answer-text {
  color: #d97706;
}

.nav-button-primary {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.nav-button-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}

.timer-value {
  color: #f59e0b;
}