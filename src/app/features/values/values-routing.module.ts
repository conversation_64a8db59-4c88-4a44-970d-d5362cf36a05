import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ValuesTestPageComponent } from './values-test-page/values-test-page.component';
import { ValuesResultsComponent } from './values-results/values-results.component';
import { ValuesDetailReportComponent } from './values-detail-report/values-detail-report.component';
import { ValuesReportExampleComponent } from './values-report-example/values-report-example.component';

const routes: Routes = [
  { path: '', component: ValuesTestPageComponent },
  { path: 'results', component: ValuesResultsComponent },
  { path: 'report/:id', component: ValuesDetailReportComponent },
  { path: 'example', component: ValuesReportExampleComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ValuesRoutingModule {}
