import { Component, OnInit, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ChartData, ChartOptions, ChartType } from 'chart.js';
import {
  MbtiService,
  MbtiReport,
  MbtiAnalysis,
} from 'src/app/service/mbti.service';

@Component({
  selector: 'app-mbti-detail-report',
  templateUrl: './mbti-detail-report.component.html',
  styleUrls: ['./mbti-detail-report.component.css'],
})
export class MbtiDetailReportComponent implements OnInit {
  // Input properties untuk menerima data dari komponen lain
  @Input() answerId?: number;
  @Input() mbtiReport?: MbtiReport;
  @Input() mbtiAnalysis?: MbtiAnalysis;

  // Properties untuk tampilan
  judulReport = 'Report MBTI';
  keterangan = 'Keterangan';
  judulHasil = 'Judul Hasil';
  judulSlogan = 'Slogan';
  keteranganHasil = 'Keterangan Hasil';

  // Variabel untuk menyimpan hasil MBTI
  mbtiResult: string = '';

  // Variabel untuk data chart
  public chartData!: ChartData<'bar'>;
  public chartOptions: ChartOptions;
  public ChartType: ChartType = 'bar';
  public chartLegend = false;

  // Loading state
  loading = false;
  error = false;
  errorMessage = '';

  constructor(private mbtiService: MbtiService, private route: ActivatedRoute) {
    // Gunakan opsi chart default dari service
    this.chartOptions = this.mbtiService.getMbtiChartOptions();
  }

  ngOnInit(): void {
    // Jika mbtiReport dan mbtiAnalysis sudah disediakan melalui @Input, gunakan itu
    if (this.mbtiReport && this.mbtiAnalysis) {
      this.updateReportData(this.mbtiReport, this.mbtiAnalysis);
      return;
    }

    // Jika answerId disediakan melalui @Input, gunakan itu
    let idToUse = this.answerId;

    // Jika tidak ada answerId dari @Input, coba ambil dari route params
    if (!idToUse && this.route.snapshot.params['id']) {
      idToUse = this.route.snapshot.params['id'];
    }

    // Jika ada ID yang valid, ambil data laporan lengkap
    if (idToUse) {
      this.loading = true;
      this.error = false;

      this.mbtiService.getCompleteMbtiReport(idToUse).subscribe({
        next: (data) => {
          this.updateReportData(data.report, data.analysis);
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading MBTI report:', error);
          this.loading = false;
          this.error = true;
          this.errorMessage =
            'Terjadi kesalahan saat memuat laporan. Silakan coba lagi nanti.';
        },
      });
    }
  }

  /**
   * Memperbarui data laporan yang ditampilkan
   * @param report Data laporan MBTI
   * @param analysis Data analisis MBTI
   */
  private updateReportData(report: MbtiReport, analysis: MbtiAnalysis): void {
    // Update MBTI result
    this.mbtiResult = report.mbtiResult;

    // Update chart data
    this.chartData = report.chartData;

    // Update analysis data
    this.judulHasil = analysis.patternName;
    this.judulSlogan = analysis.patternSlogan;
    this.keteranganHasil = analysis.analysisDetail;
  }
}
