import { NgModule } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';

import { MbtiRoutingModule } from './mbti-routing.module';
import { MbtiDetailReportComponent } from './mbti-detail-report/mbti-detail-report.component';
import { MbtiResultsComponent } from './mbti-results/mbti-results.component';
import { MbtiTestPageComponent } from './mbti-test-page/mbti-test-page.component';
import { MbtiReportExampleComponent } from './mbti-report-example/mbti-report-example.component';

@NgModule({
  declarations: [
    MbtiTestPageComponent,
    MbtiResultsComponent,
    MbtiDetailReportComponent,
    MbtiReportExampleComponent,
  ],
  imports: [SharedModule, MbtiRoutingModule],
})
export class MbtiModule {}
