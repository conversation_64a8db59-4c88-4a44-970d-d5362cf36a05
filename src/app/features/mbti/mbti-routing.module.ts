import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MbtiDetailReportComponent } from './mbti-detail-report/mbti-detail-report.component';
import { MbtiResultsComponent } from './mbti-results/mbti-results.component';
import { MbtiTestPageComponent } from './mbti-test-page/mbti-test-page.component';
import { MbtiReportExampleComponent } from './mbti-report-example/mbti-report-example.component';

const routes: Routes = [
  { path: '', component: MbtiTestPageComponent },
  { path: 'results', component: MbtiResultsComponent },
  { path: 'report/:id', component: MbtiDetailReportComponent },
  { path: 'example', component: MbtiReportExampleComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MbtiRoutingModule {}
