import { Component, OnInit } from '@angular/core';
import { MBTIQuestion } from 'src/app/interfaces/mbti-question';
import { AuthService } from 'src/app/service/auth.service';
import { MbtiService } from 'src/app/service/mbti.service';
import { TestManagerService } from 'src/app/service/test-manager.service';

@Component({
  selector: 'app-mbti-test-page',
  templateUrl: './mbti-test-page.component.html',
  styleUrls: ['./mbti-test-page.component.css'],
})
export class MbtiTestPageComponent implements OnInit {
  questions: MBTIQuestion[] = [];
  answers: { id: number; value: number; category: string }[] = [];

  constructor(
    private mbti: MbtiService,
    private testManager: TestManagerService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.mbti.getQuestion().subscribe({
      next: (data) => {
        this.questions = data;
        //
      },
      error: (error) => {
        console.error('Error fetching MBTI questions:', error);
      },
    });
  }

  handleAnswer(answers: { id: number; value: number; category: string }[]) {
    this.answers = [...answers];
  }

  handleSubmit(answers: { id: number; value: number; category: string }[]) {
    this.answers = [...answers];
    const userId = this.authService.getCurrentUser().id;
    const currentTest = this.testManager.getCurrentTest();

    // Gunakan testManagerId saat menyimpan jawaban
    this.mbti.saveAnswer(userId, this.answers, currentTest?.id).subscribe(
      (response: any) => {
        const resultId = response.data?.id;
        // Gunakan resultId saat menandai test selesai
        this.testManager.markCurrentTestComplete(resultId);
        this.testManager.moveToNextTest();
      },
      (error) => {
        console.error('Error saving MBTI answers:', error);
      }
    );
  }
}
