import { Component, OnInit } from '@angular/core';
import { MbtiService, MbtiReport, MbtiAnalysis } from 'src/app/service/mbti.service';

@Component({
  selector: 'app-mbti-report-example',
  templateUrl: './mbti-report-example.component.html',
  styleUrls: ['./mbti-report-example.component.css']
})
export class MbtiReportExampleComponent implements OnInit {
  // Contoh 1: Menggunakan answerId
  answerId: number = 1; // Ganti dengan ID yang valid

  // Contoh 2: Menggunakan data yang sudah disiapkan
  mbtiReport?: MbtiReport;
  mbtiAnalysis?: MbtiAnalysis;

  // Loading state
  loading = false;
  error = false;
  errorMessage = '';

  constructor(private mbtiService: MbtiService) { }

  ngOnInit(): void {
    // Contoh menyiapkan data secara manual
    this.prepareManualData();
  }

  /**
   * Menyiapkan data laporan dan analisis secara manual
   */
  private prepareManualData(): void {
    // Contoh data jawaban
    const answers = [
      { category: 'EI', value: -5 },
      { category: 'SN', value: 3 },
      { category: 'TF', value: -2 },
      { category: 'JP', value: 4 }
    ];

    // Hitung hasil MBTI
    const mbtiResult = this.mbtiService.calculateMBTI(answers);

    // Objek untuk menyimpan total value per kategori
    const categoryTotals: { [key: string]: number } = {
      EI: -5,
      SN: 3,
      TF: -2,
      JP: 4
    };

    // Generate data chart
    const chartData = this.mbtiService.generateHorizontalStackedBarData(answers);

    // Buat laporan
    this.mbtiReport = {
      mbtiResult,
      categoryTotals,
      chartData
    };

    // Buat analisis
    this.mbtiAnalysis = {
      pattern: mbtiResult,
      patternName: 'ENFP',
      patternSlogan: 'The Inspirer',
      analysisDetail: 'ENFP adalah tipe kepribadian yang antusias, kreatif, dan suka bersosialisasi. Mereka memiliki kemampuan untuk menginspirasi orang lain dengan ide-ide inovatif mereka. ENFP cenderung melihat potensi dan kemungkinan di segala hal, dan mereka sangat baik dalam memahami dan berempati dengan orang lain.'
    };
  }
}
