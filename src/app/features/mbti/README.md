# MBTI Report Component

Komponen ini digunakan untuk menampilkan laporan MBTI (<PERSON><PERSON><PERSON> Type Indicator) berdasarkan hasil tes MBTI.

## Cara Penggunaan

Ada tiga cara untuk menggunakan komponen `MbtiDetailReportComponent`:

### 1. Menggunakan ID Jawaban

Jika Anda memiliki ID jawaban MBTI, Anda dapat menggunakan komponen dengan cara berikut:

```html
<app-mbti-detail-report [answerId]="answerId"></app-mbti-detail-report>
```

Dimana `answerId` adalah ID jawaban MBTI yang ingin ditampilkan laporannya.

### 2. Menggunakan Data Laporan yang Sudah Disiapkan

Jika Anda sudah memiliki data laporan dan analisis MBTI, Anda dapat menggunakan komponen dengan cara berikut:

```html
<app-mbti-detail-report 
  [mbtiReport]="mbtiReport" 
  [mbtiAnalysis]="mbtiAnalysis">
</app-mbti-detail-report>
```

Dimana:
- `mbtiReport` adalah objek yang berisi data laporan MBTI dengan struktur `MbtiReport`
- `mbtiAnalysis` adalah objek yang berisi data analisis MBTI dengan struktur `MbtiAnalysis`

### 3. Menggunakan Route Parameter

Anda juga dapat mengakses komponen melalui route dengan parameter ID:

```
/home/<USER>/report/:id
```

## Service

Komponen ini menggunakan `MbtiService` untuk memproses data laporan MBTI. Service ini menyediakan beberapa metode utama:

1. `calculateMBTI(answers)`: Menghitung hasil MBTI berdasarkan jawaban
2. `generateHorizontalStackedBarData(answers)`: Menghasilkan data chart berbasis horizontal stacked bar
3. `getMbtiChartOptions()`: Mendapatkan opsi chart default untuk MBTI
4. `getMbtiReportById(answerId)`: Mendapatkan laporan MBTI lengkap berdasarkan ID jawaban
5. `getMbtiAnalysis(pattern)`: Mendapatkan analisis MBTI lengkap berdasarkan pola
6. `getCompleteMbtiReport(answerId)`: Mendapatkan laporan dan analisis MBTI lengkap berdasarkan ID jawaban

## Contoh Penggunaan

Anda dapat melihat contoh penggunaan komponen di `MbtiReportExampleComponent` yang dapat diakses melalui rute `/home/<USER>/example`.

## Struktur Data

### MbtiReport

```typescript
interface MbtiReport {
  mbtiResult: string;
  categoryTotals: { [key: string]: number };
  chartData: ChartData<'bar'>;
}
```

### MbtiAnalysis

```typescript
interface MbtiAnalysis {
  pattern: string;
  patternName: string;
  patternSlogan: string;
  analysisDetail: string;
}
```

## Keuntungan

Dengan menggunakan komponen ini, Anda dapat:

1. Menampilkan laporan MBTI dengan mudah di berbagai bagian aplikasi
2. Menghindari duplikasi kode untuk menampilkan laporan MBTI
3. Memisahkan logika bisnis dari tampilan
4. Menggunakan kembali komponen dengan data yang berbeda
