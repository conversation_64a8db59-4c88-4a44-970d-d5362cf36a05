<div class="max-w-6xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
  <!-- Header Section -->
  <div class="mb-10" *ngIf="dataSource?.data?.length !== 0">
    <h1 class="text-3xl font-bold text-gray-900 mb-4">Hasil Kuesioner MBTI</h1>
    <p class="text-lg text-gray-600">Daftar hasil pengisian kuesioner MBTI</p>
  </div>

  <!-- Table Card Container -->
  <div
    class="bg-white rounded-xl shadow-lg overflow-hidden"
    *ngIf="dataSource?.data?.length !== 0"
  >
    <!-- Table Section -->
    <div class="overflow-x-auto">
      <table mat-table [dataSource]="dataSource" class="w-full">
        <!-- Username Column (Admin Only) -->
        <ng-container matColumnDef="username" *ngIf="isAdmin">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-4 bg-gray-50 text-left text-sm font-semibold text-gray-900"
          >
            Pengguna
          </th>
          <td
            mat-cell
            *matCellDef="let element"
            class="px-6 py-4 text-sm text-gray-700 border-b border-gray-200"
          >
            {{ element.username }}
          </td>
        </ng-container>

        <!-- Kolom Waktu -->
        <ng-container matColumnDef="createdAt">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            class="px-6 py-4 bg-gray-50 text-left text-sm font-semibold text-gray-900"
          >
            Waktu Pengisian
          </th>
          <td
            mat-cell
            *matCellDef="let element"
            class="px-6 py-4 text-sm text-gray-700 border-b border-gray-200"
          >
            {{ element.createdAt | date : "d MMMM y, HH:mm" }}
          </td>
        </ng-container>

        <!-- Kolom Detail -->
        <ng-container matColumnDef="detail">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-4 bg-gray-50 text-left text-sm font-semibold text-gray-900"
          >
            Aksi
          </th>
          <td
            mat-cell
            *matCellDef="let element"
            class="px-6 py-4 text-sm border-b border-gray-200"
          >
            <button
              mat-button
              (click)="viewDetail(element.id)"
              class="inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-700 hover:bg-blue-100 rounded-md transition-all duration-200 group"
            >
              <span class="font-medium">Lihat Detail</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 ml-2 transform transition-transform duration-200 group-hover:translate-x-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </td>
        </ng-container>

        <tr
          mat-header-row
          *matHeaderRowDef="displayedColumns"
          class="border-b border-gray-200"
        ></tr>
        <tr
          mat-row
          *matRowDef="let row; columns: displayedColumns"
          class="hover:bg-gray-50 transition-colors duration-200"
        ></tr>
      </table>
    </div>

    <!-- Pagination Section -->
    <div class="border-t border-gray-200 bg-gray-50">
      <mat-paginator
        [pageSize]="10"
        [pageSizeOptions]="[5, 10, 25, 100]"
        class="bg-transparent"
        aria-label="Pilih halaman hasil kuesioner"
      >
      </mat-paginator>
    </div>
  </div>

  <!-- Empty State (Optional) -->
  <div *ngIf="dataSource?.data?.length === 0" class="text-center py-12">
    <p class="text-gray-500 text-lg">
      Belum ada data hasil kuesioner yang tersedia
    </p>
  </div>
</div>
