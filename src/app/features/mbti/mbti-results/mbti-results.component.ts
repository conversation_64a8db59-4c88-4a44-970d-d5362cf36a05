import { MatTableDataSource } from '@angular/material/table';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MbtiService } from 'src/app/service/mbti.service';
import { AuthService } from 'src/app/service/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-mbti-results',
  templateUrl: './mbti-results.component.html',
  styleUrls: ['./mbti-results.component.css'],
})
export class MbtiResultsComponent implements OnInit {
  displayedColumns: string[] = ['createdAt', 'detail'];
  dataSource = new MatTableDataSource<any>();
  isAdmin = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private mbtiService: MbtiService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Cek apakah user adalah admin
    const user = this.authService.getCurrentUser();
    this.isAdmin = user.role === 'admin';

    // Jika admin, tambahkan kolom username
    if (this.isAdmin) {
      this.displayedColumns = ['username', 'createdAt', 'detail'];
    }

    this.loadMBTIResults();
  }

  loadMBTIResults() {
    const user = this.authService.getCurrentUser();

    // Jika admin, ambil semua hasil
    if (this.isAdmin) {
      this.mbtiService.getAllResults().subscribe((results: any) => {
        const mbtiResults = results.data.map((item: any) => ({
          id: item.id,
          createdAt: item.attributes.createdAt,
          username: item.attributes.user?.data?.attributes?.username || 'N/A',
        }));
        this.dataSource.data = mbtiResults;
        this.dataSource.paginator = this.paginator;
      });
    } else {
      // Jika bukan admin, hanya ambil hasil user tersebut
      this.mbtiService.getResult(user.id).subscribe((results: any) => {
        const mbtiResults = results.data.map((item: any) => ({
          id: item.id,
          createdAt: item.attributes.createdAt,
        }));
        this.dataSource.data = mbtiResults;
        this.dataSource.paginator = this.paginator;
      });
    }
  }

  viewDetail(resultId: number) {
    if (resultId) {
      this.router.navigate(['/home/<USER>/report/', resultId]);
    }
  }
}
