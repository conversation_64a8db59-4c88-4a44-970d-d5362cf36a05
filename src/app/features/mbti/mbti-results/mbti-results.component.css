/* Modern MBTI Results Page Styles */
/* Inherits from DISC results styles with MBTI-specific customizations */

@import '../../../features/disc/disc-results/disc-results.component.css';

/* MBTI-specific color overrides */
.results-title,
.score-value {
  background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.result-card::before {
  background: linear-gradient(90deg, #8b5cf6 0%, #06b6d4 100%);
}

.result-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
}

.action-button-primary {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.action-button-primary:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
}

/* MBTI Type Badge */
.mbti-type-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
  color: var(--color-white);
  border-radius: var(--radius-2xl);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin: 0 auto var(--spacing-4);
  box-shadow: var(--shadow-lg);
}