import {
  Component,
  OnInit,
  ElementRef,
  ViewChild,
  ViewContainerRef,
  ComponentRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { MatExpansionModule } from '@angular/material/expansion';
import { NgChartsModule } from 'ng2-charts';
import { ChartOptions } from 'chart.js';
import {
  fadeAnimation,
  slideInAnimation,
  listAnimation,
  tabAnimation,
  cardAnimation,
} from 'src/app/animations';
import {
  TestReportAiService,
  TestReportAiData,
} from 'src/app/service/test-report-ai.service';
import { PdfExportService } from 'src/app/service/pdf-export.service';
import { TestReportAiPdfComponent } from './test-report-ai-pdf/test-report-ai-pdf.component';

@Component({
  selector: 'app-test-report-ai',
  templateUrl: './test-report-ai.component.html',
  styleUrls: ['./test-report-ai.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgChartsModule,
    MatButtonModule,
    MatCardModule,
    MatDividerModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    MatSlideToggleModule,
    MatTooltipModule,
    MatChipsModule,
    MatExpansionModule,
  ],
  animations: [
    fadeAnimation,
    slideInAnimation,
    listAnimation,
    tabAnimation,
    cardAnimation,
  ],
})
export class TestReportAiComponent implements OnInit {
  @ViewChild('reportContent') reportContent!: ElementRef;
  testPackId: number = 0;
  reportData: TestReportAiData | null = null;
  isLoading: boolean = true;
  error: string | null = null;
  hasDiscReport: boolean = false;
  hasMbtiReport: boolean = false;
  hasTeamsReport: boolean = false;
  hasValuesReport: boolean = false;
  activeTabIndex: number = 0;

  // Chart options
  teamsChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: false,
        min: 10,
        max: 50,
        ticks: {
          stepSize: 5,
        },
        grid: {
          color: (context: any) => {
            // Ubah warna garis grid pada nilai tertentu
            if (context.tick.value === 30) {
              return 'rgba(0, 0, 0, 1)'; // Hitam untuk garis tebal
            }
            return 'rgba(0, 0, 0, 0.1)'; // Abu-abu untuk garis biasa
          },
          lineWidth: (context: any) => {
            // Ubah ketebalan garis grid pada nilai tertentu
            if (context.tick.value === 30) {
              return 2; // Ketebalan garis tebal
            }
            return 1; // Ketebalan garis biasa
          },
        },
      },
    },
  };

  valuesChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: false,
        min: 10,
        max: 40,
        ticks: {
          stepSize: 1,
        },
        grid: {
          color: (context: any) => {
            // Ubah warna garis grid pada nilai tertentu
            if (context.tick.value === 25) {
              return 'rgba(0, 0, 0, 1)'; // Hitam untuk garis tebal
            }
            return 'rgba(0, 0, 0, 0.1)'; // Abu-abu untuk garis biasa
          },
          lineWidth: (context: any) => {
            if (context.tick.value === 25) {
              return 2;
            }
            return 1;
          },
        },
      },
    },
  };

  // Chart options
  mostChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        min: 0,
        max: 20,
        ticks: {
          stepSize: 2,
        },
      },
    },
  };

  leastChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    },
    scales: {
      y: {
        reverse: true,
        min: 0,
        max: 20,
        ticks: {
          stepSize: 2,
        },
      },
    },
  };

  differenceChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    },
    scales: {
      y: {
        min: -20,
        max: 20,
        ticks: {
          stepSize: 2,
        },
      },
    },
  };

  mbtiChartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y', // Membuat grafik horizontal
    plugins: {
      legend: {
        display: false, // Menyembunyikan legenda
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        stacked: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)', // Garis grid tipis
        },
        ticks: {
          stepSize: 1, // Interval pada sumbu X
        },
      },
      y: {
        grid: {
          display: false, // Menghilangkan garis grid vertikal
        },
      },
    },
  };

  @ViewChild('pdfContainer', { read: ViewContainerRef })
  pdfContainer!: ViewContainerRef;
  pdfComponentRef: ComponentRef<TestReportAiPdfComponent> | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private testReportAiService: TestReportAiService,
    private pdfExportService: PdfExportService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.testPackId = +params['id'];
        this.loadTestReportAi(this.testPackId);
      } else {
        this.error = 'ID Test Pack tidak ditemukan';
        this.isLoading = false;
      }
    });
  }

  loadTestReportAi(testPackId: number): void {
    this.isLoading = true;
    this.error = null;

    this.testReportAiService.getTestReportAi(testPackId).subscribe({
      next: (data) => {
        this.reportData = data;
        this.hasDiscReport = !!data.discReport;
        this.hasMbtiReport = !!data.mbtiReport;
        this.hasTeamsReport = !!data.teamsReport;
        this.hasValuesReport = !!data.valuesReport;

        this.isLoading = false;
      },
      error: (err) => {
        this.error =
          'Gagal memuat data test report AI: ' +
          (err.message || 'Unknown error');
        this.isLoading = false;
      },
    });
  }

  async exportToPdf(): Promise<void> {
    if (!this.reportData) {
      return;
    }

    // Create the PDF component dynamically
    this.pdfContainer.clear();
    this.pdfComponentRef = this.pdfContainer.createComponent(
      TestReportAiPdfComponent
    );

    // Set the data for the PDF component
    this.pdfComponentRef.instance.reportData = this.reportData;
    this.pdfComponentRef.instance.hasDiscReport = this.hasDiscReport;
    this.pdfComponentRef.instance.hasMbtiReport = this.hasMbtiReport;
    this.pdfComponentRef.instance.hasTeamsReport = this.hasTeamsReport;
    this.pdfComponentRef.instance.hasValuesReport = this.hasValuesReport;

    // Wait for the component to be initialized and rendered
    setTimeout(async () => {
      try {
        if (this.pdfComponentRef && this.pdfComponentRef.instance.pdfContent) {
          const filename = `test_report_ai_${this.testPackId}_${new Date()
            .toISOString()
            .slice(0, 10)}.pdf`;
          const pdfElement =
            this.pdfComponentRef.instance.pdfContent.nativeElement;

          // Generate the PDF using the service
          await this.pdfExportService.generatePdf(pdfElement, filename);
        } else {
          console.error('PDF content element not found');
          alert('Failed to generate PDF. Please try again.');
        }
      } catch (error) {
        console.error('Error generating PDF:', error);
        alert('Failed to generate PDF. Please try again.');
      } finally {
        // Clean up the component
        if (this.pdfComponentRef) {
          this.pdfContainer.clear();
          this.pdfComponentRef = null;
        }
      }
    }, 500); // Give time for the component to render
  }

  goBack(): void {
    this.router.navigate(['/home/<USER>/test-pack-results']);
  }

  onTabChange(event: any): void {
    this.activeTabIndex = event.index;
  }
}
