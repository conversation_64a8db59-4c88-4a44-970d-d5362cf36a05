/* Styling for cards */
mat-card {
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

mat-card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

mat-card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

mat-card-subtitle {
  color: #666;
}

/* Styling for sections */
.rounded-md {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.rounded-md:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Styling for lists */
ul {
  margin-top: 0.5rem;
}

ul li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

/* Styling for chips */
mat-chip-option {
  margin: 4px;
}

/* Styling for expansion panels */
mat-expansion-panel {
  margin-bottom: 8px;
  border-radius: 8px;
}

mat-panel-title {
  font-weight: 500;
}

/* Chart Styling - Copied from test-pack-report */
.disc-chart-container {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 15px;
}

.chart-column {
  flex: 1;
  min-width: 250px;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Specific styling for DISC charts */
.disc-chart-container.disc-three-charts .chart-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 15px;
  margin-top: 10px;
}

.disc-chart-container.disc-three-charts .chart-column {
  flex: 1;
  min-width: 200px;
  background-color: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

/* Responsive adjustments for three charts */
@media (max-width: 992px) {
  .disc-chart-container.disc-three-charts .chart-column {
    min-width: 100%;
    margin-bottom: 20px;
  }
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-column {
    min-width: 100%;
  }

  .chart-container {
    height: 250px;
  }

  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

h3 {
  margin-top: 20px;
  margin-bottom: 15px;
  color: var(--text-primary);
  font-weight: 600;
  transition: color 0.3s ease;
}

h4 {
  margin-top: 5px;
  margin-bottom: 12px;
  color: var(--text-secondary);
  font-weight: 500;
  text-align: center;
  transition: color 0.3s ease;
}

/* Tambahan style untuk menyesuaikan dengan disc-detail-report */
canvas {
  width: 100% !important;
  height: 250px !important; /* Mengurangi tinggi chart */
  transition: filter 0.3s ease;
}

/* Tambahan style untuk chart DISC */
.disc-chart-container canvas {
  max-height: 180px !important;
  margin: 0 auto;
}

.disc-chart-container .chart-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px !important;
  margin-bottom: 5px;
  padding: 5px;
}

.disc-chart-container h4 {
  margin-bottom: 5px;
  font-weight: 600;
  color: #4a5568;
  text-align: center;
}

.disc-chart-container .chart-column p {
  margin-top: 5px;
  margin-bottom: 0;
  text-align: center;
  font-weight: 500;
  color: #4a5568;
}

/* Styling untuk tabel data */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

table tr {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

table tr:last-child {
  border-bottom: none;
}

table td {
  padding: 8px 4px;
  vertical-align: middle;
}

table td.font-medium {
  font-weight: 500;
  color: var(--text-secondary);
}

/* Styling untuk DISC Summary */
.bg-blue-50 {
  background-color: #e6f2ff;
}

.bg-blue-50 h3 {
  color: #2563eb;
  margin-top: 0;
}

.bg-blue-50 h4 {
  color: #1e40af;
  text-align: left;
}

.bg-blue-50 .text-lg {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.bg-blue-50 .text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #4b5563;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .chart-container {
    height: 250px;
  }

  .disc-chart-container.disc-three-charts .chart-column {
    min-width: 100%;
  }
}
