<div class="pdf-container" #pdfContent>
  <!-- PDF Header -->
  <div class="pdf-header">
    <div>
      <h1 class="pdf-title">AI Analysis Report</h1>
      <p class="pdf-subtitle" *ngIf="reportData">
        User: {{ reportData.username }} | Created:
        {{ reportData.createdAt | date : "medium" }}
      </p>
    </div>
    <div>
      <h2 class="pdf-logo">MAXIMA</h2>
    </div>
  </div>

  <!-- Summary Section -->
  <div class="pdf-section">
    <h2 class="pdf-section-title">AI Analysis Summary</h2>
    <div class="pdf-card">
      <h3 class="pdf-card-title">Test Summary</h3>
      <p>{{ reportData.aiAnalysis?.testSummary }}</p>
    </div>

    <div class="pdf-card">
      <h3 class="pdf-card-title">Personality Overview</h3>
      <p>{{ reportData.aiAnalysis?.personalityOverview }}</p>
    </div>
  </div>

  <!-- Strengths & Weaknesses Section -->
  <div class="pdf-section">
    <h2 class="pdf-section-title">Strengths & Weaknesses</h2>
    <div class="pdf-two-columns">
      <div class="pdf-column">
        <div class="pdf-card strengths">
          <h3 class="pdf-card-title">Your Strengths</h3>
          <ul>
            <li *ngFor="let strength of reportData.aiAnalysis?.strengths">
              {{ strength }}
            </li>
          </ul>
        </div>
      </div>
      <div class="pdf-column">
        <div class="pdf-card weaknesses">
          <h3 class="pdf-card-title">Areas for Improvement</h3>
          <ul>
            <li *ngFor="let weakness of reportData.aiAnalysis?.weaknesses">
              {{ weakness }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Work Style Section -->
  <div class="pdf-section">
    <h2 class="pdf-section-title">Work Style Analysis</h2>
    <div class="pdf-card">
      <h3 class="pdf-card-title">Individual Work Style</h3>
      <p>{{ reportData.aiAnalysis?.workStyle }}</p>
    </div>

    <div class="pdf-two-columns">
      <div class="pdf-column">
        <div class="pdf-card team-strengths">
          <h3 class="pdf-card-title">Team Strengths</h3>
          <ul>
            <li *ngFor="let strength of reportData.aiAnalysis?.teamStrengths">
              {{ strength }}
            </li>
          </ul>
        </div>
      </div>
      <div class="pdf-column">
        <div class="pdf-card team-weaknesses">
          <h3 class="pdf-card-title">Team Challenges</h3>
          <ul>
            <li *ngFor="let weakness of reportData.aiAnalysis?.teamWeaknesses">
              {{ weakness }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Career Section -->
  <div class="pdf-section">
    <h2 class="pdf-section-title">Career Insights</h2>
    <div class="pdf-card">
      <h3 class="pdf-card-title">Suitable Job Types</h3>
      <div class="pdf-job-chips">
        <span class="pdf-chip" *ngFor="let job of reportData.aiAnalysis?.suitableJobs">
          {{ job }}
        </span>
      </div>
    </div>
  </div>

  <!-- Similar Figures Section -->
  <div class="pdf-section">
    <h2 class="pdf-section-title">Similar Personality Figures</h2>
    <div class="pdf-card" *ngFor="let figure of reportData.aiAnalysis?.similarFigures">
      <h3 class="pdf-card-title">{{ figure.name }}</h3>
      <p class="pdf-figure-description">{{ figure.description }}</p>
      <p class="pdf-figure-similarity"><strong>Similarity:</strong> {{ figure.similarity }}</p>
    </div>
  </div>

  <!-- Test Data Summary Section -->
  <div class="pdf-section">
    <h2 class="pdf-section-title">Test Data Summary</h2>
    <div class="pdf-summary-grid">
      <div *ngIf="hasDiscReport" class="pdf-summary-item disc">
        <h3>DISC Assessment</h3>
        <p *ngIf="reportData.discReport">
          <span class="label">MOST Pattern:</span>
          {{ reportData.discReport.reportData.mostString }}<br />
          <span class="label">LEAST Pattern:</span>
          {{ reportData.discReport.reportData.leastString }}<br />
          <span class="label">DIFFERENCE Pattern:</span>
          {{ reportData.discReport.reportData.differenceString }}
        </p>
      </div>
      <div *ngIf="hasMbtiReport" class="pdf-summary-item mbti">
        <h3>MBTI Assessment</h3>
        <p *ngIf="reportData.mbtiReport">
          <span class="label">MBTI Type:</span>
          {{ reportData.mbtiReport.report.mbtiResult }}<br />
          <span class="label">Type Name:</span>
          {{ reportData.mbtiReport.analysis.patternName }}<br />
          <span class="label">Type Slogan:</span>
          {{ reportData.mbtiReport.analysis.patternSlogan }}
        </p>
      </div>
      <div *ngIf="hasTeamsReport" class="pdf-summary-item teams">
        <h3>TEAMS Assessment</h3>
        <p *ngIf="reportData.teamsReport">
          <span class="label">Highest Categories:</span>
          <span *ngFor="let cat of reportData.teamsReport.report.highestCategories; let last = last">
            {{ cat.category }} ({{ cat.value }}){{ !last ? ', ' : '' }}
          </span>
        </p>
      </div>
      <div *ngIf="hasValuesReport" class="pdf-summary-item values">
        <h3>VALUES Assessment</h3>
        <p *ngIf="reportData.valuesReport">
          <span class="label">Main Value:</span>
          {{ reportData.valuesReport.analysis.mainCategoryName }} ({{ reportData.valuesReport.analysis.mainCategory }})
        </p>
      </div>
    </div>
  </div>

  <!-- PDF Footer -->
  <div class="pdf-footer">
    <p>
      Generated on {{ reportData.createdAt | date : "medium" }} | Maxima
      Assessment Platform
    </p>
    <p>© {{ currentYear }} Maxima. All rights reserved.</p>
  </div>
</div>
