<div class="max-w-5xl mx-auto px-4 py-8" [@fadeAnimation]>
  <!-- Container for PDF component -->
  <div #pdfContainer></div>

  <!-- Header -->
  <div class="flex justify-between items-center mb-6" [@slideInAnimation]>
    <div>
      <h1 class="text-2xl font-bold">Test Report AI</h1>
      <p *ngIf="reportData">
        User: {{ reportData.username }} | Created:
        {{ reportData.createdAt | date : "medium" }}
      </p>
    </div>
    <div class="flex space-x-2">
      <button
        mat-raised-button
        color="primary"
        (click)="exportToPdf()"
        [disabled]="isLoading || !reportData"
        matTooltip="Export report to PDF"
      >
        <mat-icon>picture_as_pdf</mat-icon>
        Export to PDF
      </button>
      <button mat-button (click)="goBack()" matTooltip="Return to results page">
        <mat-icon>arrow_back</mat-icon>
        Back
      </button>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="flex justify-center my-12">
    <mat-spinner diameter="50"></mat-spinner>
    <span class="ml-4 text-gray-600">Loading report data...</span>
  </div>

  <!-- Error Message -->
  <div
    *ngIf="error"
    class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6"
    role="alert"
  >
    <p class="font-bold">Error</p>
    <p>{{ error }}</p>
  </div>

  <!-- Report Content -->
  <div *ngIf="reportData && !isLoading && !error">
    <!-- AI Analysis Summary Card -->
    <mat-card class="mb-6" [@cardAnimation]>
      <mat-card-header>
        <mat-card-title>AI Analysis Summary</mat-card-title>
        <mat-card-subtitle>
          Based on your combined test results
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="p-4 bg-purple-50 rounded-md mb-4">
          <h3 class="font-semibold text-purple-800 mb-2">Test Summary</h3>
          <p *ngIf="reportData.aiAnalysis?.testSummary">
            {{ reportData.aiAnalysis?.testSummary }}
          </p>
          <p
            *ngIf="!reportData.aiAnalysis?.testSummary"
            class="text-gray-500 italic"
          >
            Data tidak tersedia
          </p>
        </div>

        <div class="p-4 bg-blue-50 rounded-md">
          <h3 class="font-semibold text-blue-800 mb-2">Personality Overview</h3>
          <p *ngIf="reportData.aiAnalysis?.personalityOverview">
            {{ reportData.aiAnalysis?.personalityOverview }}
          </p>
          <p
            *ngIf="!reportData.aiAnalysis?.personalityOverview"
            class="text-gray-500 italic"
          >
            Data tidak tersedia
          </p>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Strengths & Weaknesses Card -->
    <mat-card class="mb-6" [@cardAnimation]>
      <mat-card-header>
        <mat-card-title>Strengths & Weaknesses</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Strengths -->
          <div class="p-4 bg-green-50 rounded-md">
            <h3 class="font-semibold text-green-800 mb-2">Your Strengths</h3>
            <ul class="list-disc pl-5">
              <li
                *ngFor="let strength of reportData.aiAnalysis?.strengths"
                class="mb-1"
              >
                {{ strength }}
              </li>
            </ul>
          </div>

          <!-- Weaknesses -->
          <div class="p-4 bg-red-50 rounded-md">
            <h3 class="font-semibold text-red-800 mb-2">
              Areas for Improvement
            </h3>
            <ul class="list-disc pl-5">
              <li
                *ngFor="let weakness of reportData.aiAnalysis?.weaknesses"
                class="mb-1"
              >
                {{ weakness }}
              </li>
            </ul>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Work Style Card -->
    <mat-card class="mb-6" [@cardAnimation]>
      <mat-card-header>
        <mat-card-title>Work Style Analysis</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="p-4 bg-amber-50 rounded-md mb-4">
          <h3 class="font-semibold text-amber-800 mb-2">
            Individual Work Style
          </h3>
          <p>{{ reportData.aiAnalysis?.workStyle }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Team Strengths -->
          <div class="p-4 bg-teal-50 rounded-md">
            <h3 class="font-semibold text-teal-800 mb-2">Team Strengths</h3>
            <ul class="list-disc pl-5">
              <li
                *ngFor="let strength of reportData.aiAnalysis?.teamStrengths"
                class="mb-1"
              >
                {{ strength }}
              </li>
            </ul>
          </div>

          <!-- Team Weaknesses -->
          <div class="p-4 bg-orange-50 rounded-md">
            <h3 class="font-semibold text-orange-800 mb-2">Team Challenges</h3>
            <ul class="list-disc pl-5">
              <li
                *ngFor="let weakness of reportData.aiAnalysis?.teamWeaknesses"
                class="mb-1"
              >
                {{ weakness }}
              </li>
            </ul>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Career & Similar Figures Card -->
    <mat-card class="mb-6" [@cardAnimation]>
      <mat-card-header>
        <mat-card-title>Career & Personality Insights</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <!-- Suitable Jobs -->
        <div class="p-4 bg-indigo-50 rounded-md mb-4">
          <h3 class="font-semibold text-indigo-800 mb-2">Suitable Job Types</h3>
          <div class="flex flex-wrap gap-2">
            <mat-chip-option
              *ngFor="let job of reportData.aiAnalysis?.suitableJobs"
              color="primary"
              selected
            >
              {{ job }}
            </mat-chip-option>
          </div>
        </div>

        <!-- Similar Figures -->
        <div class="p-4 bg-violet-50 rounded-md">
          <h3 class="font-semibold text-violet-800 mb-2">
            Similar Personality Figures
          </h3>

          <mat-accordion>
            <mat-expansion-panel
              *ngFor="let figure of reportData.aiAnalysis?.similarFigures"
            >
              <mat-expansion-panel-header>
                <mat-panel-title>
                  {{ figure.name }}
                </mat-panel-title>
                <mat-panel-description>
                  {{ figure.description }}
                </mat-panel-description>
              </mat-expansion-panel-header>
              <p><strong>Similarity:</strong> {{ figure.similarity }}</p>
            </mat-expansion-panel>
          </mat-accordion>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Test Data Summary Card -->
    <mat-card class="mb-6" [@cardAnimation]>
      <mat-card-header>
        <mat-card-title>Test Data Summary</mat-card-title>
        <mat-card-subtitle>
          Raw data from your individual tests
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <!-- DISC Section -->
        <div
          *ngIf="hasDiscReport"
          class="mb-8"
          [@listAnimation]="reportData ? 'in' : 'out'"
        >
          <h3 class="text-xl font-bold mb-4 text-blue-800 border-b pb-2">
            DISC Assessment Results
          </h3>
          <!-- DISC Charts in Full Width -->
          <div class="disc-chart-container disc-three-charts mb-6">
            <h3 class="text-lg font-semibold mb-2">DISC Profiles</h3>
            <div class="chart-row">
              <div class="chart-column">
                <h4>MOST (Natural Tendencies)</h4>
                <div class="chart-container">
                  <canvas
                    *ngIf="reportData.discReport?.reportData?.mostChartData"
                    baseChart
                    [data]="reportData.discReport?.reportData?.mostChartData"
                    [options]="{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: true,
                          position: 'top'
                        }
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          min: 0,
                          max: 20,
                          ticks: {
                            stepSize: 2
                          }
                        }
                      }
                    }"
                    [type]="
                      reportData.discReport?.reportData?.chartType || 'bar'
                    "
                  >
                  </canvas>
                </div>
                <p class="text-center mt-2 font-medium">
                  Pattern: {{ reportData.discReport?.reportData?.mostString }}
                </p>
              </div>
              <div class="chart-column">
                <h4>LEAST (Avoided Behaviors)</h4>
                <div class="chart-container">
                  <canvas
                    *ngIf="reportData.discReport?.reportData?.leastChartData"
                    baseChart
                    [data]="reportData.discReport?.reportData?.leastChartData"
                    [options]="{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: true,
                          position: 'top'
                        }
                      },
                      scales: {
                        y: {
                          reverse: true,
                          min: 0,
                          max: 20,
                          ticks: {
                            stepSize: 2
                          }
                        }
                      }
                    }"
                    [type]="
                      reportData.discReport?.reportData?.chartType || 'bar'
                    "
                  >
                  </canvas>
                </div>
                <p class="text-center mt-2 font-medium">
                  Pattern: {{ reportData.discReport?.reportData?.leastString }}
                </p>
              </div>
              <div class="chart-column">
                <h4>DIFFERENCE (Adaptations)</h4>
                <div class="chart-container">
                  <canvas
                    *ngIf="
                      reportData.discReport?.reportData?.differenceChartData
                    "
                    baseChart
                    [data]="
                      reportData.discReport?.reportData?.differenceChartData
                    "
                    [options]="{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: true,
                          position: 'top'
                        }
                      },
                      scales: {
                        y: {
                          min: -20,
                          max: 20,
                          ticks: {
                            stepSize: 2
                          }
                        }
                      }
                    }"
                    [type]="
                      reportData.discReport?.reportData?.chartType || 'bar'
                    "
                  >
                  </canvas>
                </div>
                <p class="text-center mt-2 font-medium">
                  Pattern:
                  {{ reportData.discReport?.reportData?.differenceString }}
                </p>
              </div>
            </div>
          </div>

          <!-- DISC Summary -->
          <div class="bg-blue-50 p-5 rounded-md mb-6">
            <h3 class="text-lg font-semibold mb-3">DISC Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h4 class="font-medium text-blue-800 mb-2">MOST Pattern</h4>
                <p class="text-lg font-bold">
                  {{ reportData.discReport?.reportData?.mostString }}
                </p>
                <p class="text-sm mt-1">
                  Your natural tendencies and behaviors
                </p>
              </div>
              <div>
                <h4 class="font-medium text-blue-800 mb-2">LEAST Pattern</h4>
                <p class="text-lg font-bold">
                  {{ reportData.discReport?.reportData?.leastString }}
                </p>
                <p class="text-sm mt-1">Behaviors you tend to avoid</p>
              </div>
              <div>
                <h4 class="font-medium text-blue-800 mb-2">
                  DIFFERENCE Pattern
                </h4>
                <p class="text-lg font-bold">
                  {{ reportData.discReport?.reportData?.differenceString }}
                </p>
                <p class="text-sm mt-1">
                  How you adapt in different situations
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- MBTI Section -->
        <div
          *ngIf="hasMbtiReport"
          class="mb-8"
          [@listAnimation]="reportData ? 'in' : 'out'"
        >
          <h3 class="text-xl font-bold mb-4 text-green-800 border-b pb-2">
            MBTI Assessment Results
          </h3>
          <div class="flex flex-wrap">
            <!-- MBTI Chart -->
            <div class="w-full lg:w-2/3 pr-0 lg:pr-4 mb-4 lg:mb-0">
              <div
                class="chart-container bg-white p-4 rounded-lg shadow-sm mb-6"
              >
                <h3 class="text-lg font-semibold mb-2">MBTI Preferences</h3>
                <div style="height: 300px">
                  <canvas
                    baseChart
                    [data]="reportData.mbtiReport?.report?.chartData"
                    [type]="'bar'"
                    [options]="{
                      responsive: true,
                      maintainAspectRatio: false,
                      indexAxis: 'y',
                      plugins: {
                        legend: {
                          display: false
                        }
                      },
                      scales: {
                        x: {
                          beginAtZero: true,
                          stacked: true,
                          grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                          },
                          ticks: {
                            stepSize: 1
                          }
                        },
                        y: {
                          grid: {
                            display: false
                          }
                        }
                      }
                    }"
                  >
                  </canvas>
                </div>
              </div>
            </div>
            <!-- MBTI Data -->
            <div class="w-full lg:w-1/3">
              <div class="bg-green-50 p-4 rounded-md h-full">
                <h4 class="font-semibold mb-3">MBTI Type</h4>
                <table class="w-full text-sm">
                  <tr class="border-b border-green-200">
                    <td class="py-2 font-medium">Type:</td>
                    <td class="py-2">
                      {{ reportData.mbtiReport?.report?.mbtiResult }}
                    </td>
                  </tr>
                  <tr class="border-b border-green-200">
                    <td class="py-2 font-medium">Name:</td>
                    <td class="py-2">
                      {{ reportData.mbtiReport?.analysis?.patternName }}
                    </td>
                  </tr>
                  <tr>
                    <td class="py-2 font-medium">Slogan:</td>
                    <td class="py-2">
                      {{ reportData.mbtiReport?.analysis?.patternSlogan }}
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- TEAMS Section -->
        <div
          *ngIf="hasTeamsReport"
          class="mb-8"
          [@listAnimation]="reportData ? 'in' : 'out'"
        >
          <h3 class="text-xl font-bold mb-4 text-yellow-800 border-b pb-2">
            TEAMS Assessment Results
          </h3>
          <div class="flex flex-wrap">
            <!-- TEAMS Chart -->
            <div class="w-full lg:w-2/3 pr-0 lg:pr-4 mb-4 lg:mb-0">
              <div
                class="chart-container bg-white p-4 rounded-lg shadow-sm mb-6"
              >
                <h3 class="text-lg font-semibold mb-2">TEAMS Profile</h3>
                <div style="height: 300px">
                  <canvas
                    baseChart
                    [data]="reportData.teamsReport?.report?.chartData"
                    [type]="'line'"
                    [options]="teamsChartOptions"
                  >
                  </canvas>
                </div>
              </div>
            </div>
            <!-- TEAMS Data -->
            <div class="w-full lg:w-1/3">
              <div class="bg-yellow-50 p-4 rounded-md h-full">
                <h4 class="font-semibold mb-3">Highest Categories</h4>
                <div *ngIf="reportData.teamsReport?.report?.highestCategories">
                  <div
                    *ngFor="
                      let cat of reportData.teamsReport?.report
                        ?.highestCategories;
                      let i = index
                    "
                    class="mb-2 pb-2"
                    [class.border-b]="
                      i <
                      (reportData.teamsReport?.report?.highestCategories
                        ?.length || 0) -
                        1
                    "
                    [class.border-yellow-200]="
                      i <
                      (reportData.teamsReport?.report?.highestCategories
                        ?.length || 0) -
                        1
                    "
                  >
                    <div class="flex justify-between items-center">
                      <span class="font-medium">{{ cat.category }}:</span>
                      <span>{{ cat.value }}</span>
                    </div>
                    <div class="w-full bg-yellow-200 rounded-full h-2.5 mt-1">
                      <div
                        class="bg-yellow-600 h-2.5 rounded-full"
                        [style.width.%]="(cat.value / 20) * 100"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- VALUES Section -->
        <div
          *ngIf="hasValuesReport"
          class="mb-8"
          [@listAnimation]="reportData ? 'in' : 'out'"
        >
          <h3 class="text-xl font-bold mb-4 text-red-800 border-b pb-2">
            VALUES Assessment Results
          </h3>
          <div class="flex flex-wrap">
            <!-- VALUES Chart -->
            <div class="w-full lg:w-2/3 pr-0 lg:pr-4 mb-4 lg:mb-0">
              <div
                class="chart-container bg-white p-4 rounded-lg shadow-sm mb-6"
              >
                <h3 class="text-lg font-semibold mb-2">VALUES Profile</h3>
                <div style="height: 300px">
                  <canvas
                    baseChart
                    [data]="reportData.valuesReport?.report?.chartData"
                    [type]="'line'"
                    [options]="valuesChartOptions"
                  >
                  </canvas>
                </div>
              </div>
            </div>
            <!-- VALUES Data -->
            <div class="w-full lg:w-1/3">
              <div class="bg-red-50 p-4 rounded-md h-full">
                <h4 class="font-semibold mb-3">Main Value</h4>
                <table class="w-full text-sm">
                  <tr class="border-b border-red-200">
                    <td class="py-2 font-medium">Category:</td>
                    <td class="py-2">
                      {{ reportData.valuesReport?.analysis?.mainCategory }}
                    </td>
                  </tr>
                  <tr class="border-b border-red-200">
                    <td class="py-2 font-medium">Name:</td>
                    <td class="py-2">
                      {{ reportData.valuesReport?.analysis?.mainCategoryName }}
                    </td>
                  </tr>
                  <tr>
                    <td class="py-2 font-medium">Value:</td>
                    <td class="py-2">
                      {{ reportData.valuesReport?.analysis?.mainCategoryValue }}
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
