import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TestManagerService } from 'src/app/service/test-manager.service';
import { AuthService } from 'src/app/service/auth.service';
import { User } from 'src/app/interfaces/user';
import { CreateTestAccess } from 'src/app/interfaces/test-access';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

interface TestType {
  id: string;
  name: string;
  selected: boolean;
  type: 'DISC' | 'TEAMS' | 'VALUES' | 'MBTI';
  route: string;
}

@Component({
  selector: 'app-manage-test-access',
  templateUrl: './manage-test-access.component.html',
  styleUrls: ['./manage-test-access.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, MatSnackBarModule],
})
export class ManageTestAccessComponent implements OnInit {
  users: User[] = [];
  selectedUser: User | null = null;
  isLoading = false;
  error: string | null = null;
  isUserConfirmed = false;
  test_packs: number | null = null;

  availableTests: TestType[] = [
    {
      id: 'DISC',
      name: 'DISC Assessment',
      selected: false,
      type: 'DISC',
      route: 'home/disc',
    },
    {
      id: 'MBTI',
      name: 'MBTI Assessment',
      selected: false,
      type: 'MBTI',
      route: 'home/mbti',
    },
    {
      id: 'TEAM',
      name: 'Team Role Assessment',
      selected: false,
      type: 'TEAMS',
      route: 'home/teams',
    },
    {
      id: 'VALUES',
      name: 'Values Assessment',
      selected: false,
      type: 'VALUES',
      route: 'home/values',
    },
  ];

  constructor(
    private testManager: TestManagerService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.loadUsers();
  }

  loadUsers() {
    this.isLoading = true;
    this.authService.getUsers().subscribe({
      next: (users) => {
        this.users = users;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.error = 'Gagal memuat daftar pengguna';
        this.isLoading = false;
      },
    });
  }

  onUserSelect(userId: string) {
    // Cari user dari array users berdasarkan ID
    const user = this.users.find((u) => u.id === userId);
    if (user) {
      this.selectedUser = user;
      this.isUserConfirmed = false;
      this.test_packs = null;
      this.resetTestSelection();
    }
  }

  confirmUser() {
    if (!this.selectedUser) return;

    // Log untuk debugging

    this.isLoading = true;
    // Gunakan ID langsung dari selectedUser (sudah dalam bentuk string)
    this.testManager.createTestPack(this.selectedUser.id).subscribe({
      next: (response) => {
        if (response && response.id) {
          this.test_packs = response.id;
          this.isUserConfirmed = true;
          this.snackBar.open('Test pack berhasil dibuat', 'Tutup', {
            duration: 3000,
            horizontalPosition: 'center',
            verticalPosition: 'top',
          });
        } else {
          this.error = 'Gagal membuat test pack: Response tidak valid';
          this.snackBar.open('Gagal membuat test pack', 'Tutup', {
            duration: 3000,
            horizontalPosition: 'center',
            verticalPosition: 'top',
          });
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error creating test pack:', error);
        this.error = 'Gagal membuat test pack';
        this.snackBar.open('Gagal membuat test pack', 'Tutup', {
          duration: 3000,
          horizontalPosition: 'center',
          verticalPosition: 'top',
        });
        this.isLoading = false;
      },
    });
  }

  resetTestSelection() {
    this.availableTests.forEach((test) => (test.selected = false));
  }

  saveTestAccess() {
    if (!this.selectedUser || !this.test_packs) return;

    const selectedTests: CreateTestAccess[] = this.availableTests
      .filter((test) => test.selected)
      .map((test, index) => ({
        type: test.type,
        isCompleted: false,
        route: test.route,
        order: index + 1,
        test_packs: this.test_packs!,
      }));

    this.isLoading = true;
    this.testManager
      .assignTestsToTestPack(this.test_packs, selectedTests)
      .subscribe({
        next: () => {
          this.snackBar.open('Akses test berhasil disimpan', 'Tutup', {
            duration: 3000,
            horizontalPosition: 'center',
            verticalPosition: 'top',
          });
          this.resetForm();
        },
        error: (error) => {
          console.error('Error saving test access:', error);
          this.snackBar.open('Gagal menyimpan akses test', 'Tutup', {
            duration: 3000,
            horizontalPosition: 'center',
            verticalPosition: 'top',
          });
        },
      });
  }

  resetForm() {
    this.selectedUser = null;
    this.isUserConfirmed = false;
    this.test_packs = null;
    this.resetTestSelection();
  }
}
