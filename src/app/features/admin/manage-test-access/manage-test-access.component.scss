/* Modern Admin Panel Styles */

.admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-8) var(--spacing-4);
}

.admin-header {
  margin-bottom: var(--spacing-8);
  padding-bottom: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
}

.admin-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-2);
}

.admin-subtitle {
  color: var(--color-gray-600);
  font-size: var(--font-size-lg);
}

.test-selection {
  margin-top: var(--spacing-8);
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.test-selection-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
}

.test-item {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
  transition: all var(--transition-fast);
}

.test-item:hover {
  background: var(--color-gray-100);
  border-color: var(--color-primary-200);
}

.test-item:last-child {
  margin-bottom: 0;
}

.actions {
  margin-top: var(--spacing-8);
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.btn-admin {
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}

.btn-admin-primary {
  background: var(--color-primary);
  color: var(--color-white);
  box-shadow: var(--shadow-sm);
}

.btn-admin-primary:hover {
  background: var(--color-primary-600);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-admin-secondary {
  background: var(--color-white);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
}

.btn-admin-secondary:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-gray-300);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Form Styles */
.form-group-admin {
  margin-bottom: var(--spacing-6);
}

.form-label-admin {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.form-input-admin {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
}

.form-input-admin:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Table Styles */
.admin-table {
  width: 100%;
  background: var(--color-white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
}

.admin-table th,
.admin-table td {
  padding: var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--color-gray-200);
}

.admin-table th {
  background: var(--color-gray-50);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  font-size: var(--font-size-sm);
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table tr:hover {
  background: var(--color-gray-25);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-container {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .actions {
    flex-direction: column;
  }

  .btn-admin {
    justify-content: center;
  }
}