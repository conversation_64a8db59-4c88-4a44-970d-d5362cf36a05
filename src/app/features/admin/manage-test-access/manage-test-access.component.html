<div class="max-w-4xl mx-auto px-4 py-8">
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-4">Manajemen Akses Test</h1>
    <p class="text-gray-600"><PERSON><PERSON><PERSON> akses test untuk pengguna</p>
  </div>

  <!-- Error <PERSON> -->
  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
  >
    {{ error }}
  </div>

  <!-- User Selection -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h2 class="text-xl font-semibold mb-4">Pilih <PERSON>una:</h2>
    <select
      [disabled]="isLoading"
      (change)="onUserSelect($any($event.target).value)"
      class="w-full p-2 border rounded-lg"
    >
      <option value="">-- Pilih <PERSON> --</option>
      <option *ngFor="let user of users" [value]="user.id">
        {{ user.name }}
      </option>
    </select>

    <!-- Confirm User Button -->
    <div class="mt-4 text-center" *ngIf="selectedUser && !isUserConfirmed">
      <button
        (click)="confirmUser()"
        [disabled]="isLoading"
        class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
      >
        {{ isLoading ? "Memproses..." : "Konfirmasi Pengguna" }}
      </button>
    </div>

    <!-- Selected User Info -->
    <div
      *ngIf="selectedUser && isUserConfirmed"
      class="mt-4 p-4 bg-green-50 rounded-lg"
    >
      <p class="text-green-700">Pengguna terpilih: {{ selectedUser.name }}</p>
    </div>
  </div>

  <!-- Test Selection -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-8" *ngIf="isUserConfirmed">
    <h2 class="text-xl font-semibold mb-4">Pilih Test:</h2>
    <div class="space-y-4">
      <div
        *ngFor="let test of availableTests"
        class="flex items-center p-4 border rounded-lg"
      >
        <input
          type="checkbox"
          [id]="test.id"
          [(ngModel)]="test.selected"
          [disabled]="isLoading"
          class="w-5 h-5 text-blue-600 rounded"
        />
        <label [for]="test.id" class="ml-4 text-gray-700">{{
          test.name
        }}</label>
      </div>
    </div>
  </div>

  <!-- Action Button -->
  <div class="text-center">
    <button
      *ngIf="isUserConfirmed"
      (click)="saveTestAccess()"
      [disabled]="isLoading"
      class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
    >
      {{ isLoading ? "Menyimpan..." : "Simpan Akses Test" }}
    </button>
  </div>
</div>
