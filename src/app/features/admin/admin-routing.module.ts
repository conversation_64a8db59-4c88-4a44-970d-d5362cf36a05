import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ManageTestAccessComponent } from './manage-test-access/manage-test-access.component';
import { TestPackResultsComponent } from './test-pack-results/test-pack-results.component';
import { TestPackReportComponent } from './test-pack-report/test-pack-report.component';
import { TestReportAiComponent } from './test-report-ai/test-report-ai.component';
import { PaymentManagementComponent } from './payment-management/payment-management.component';
import { AdminGuard } from '../../admin.guard';

const routes: Routes = [
  { path: '', redirectTo: 'manage-access', pathMatch: 'full' },
  {
    path: 'manage-access',
    component: ManageTestAccessComponent,
    canActivate: [AdminGuard],
  },
  { path: 'test-pack-results', component: TestPackResultsComponent },
  { path: 'test-pack-report/:id', component: TestPackReportComponent },
  {
    path: 'test-report-ai/:id',
    component: TestReportAiComponent,
    canActivate: [AdminGuard],
  },
  {
    path: 'payment-management',
    component: PaymentManagementComponent,
    canActivate: [AdminGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminRoutingModule {}
