import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  ComponentRef,
  ViewContainerRef,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NgChartsModule } from 'ng2-charts';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import {
  fadeAnimation,
  slideInAnimation,
  listAnimation,
  tabAnimation,
  cardAnimation,
} from 'src/app/animations';
import { TestManagerService } from 'src/app/service/test-manager.service';
import {
  DiscReportService,
  DiscReportData,
} from 'src/app/service/disc-report.service';
import {
  MbtiService,
  MbtiReport,
  MbtiAnalysis,
} from 'src/app/service/mbti.service';
import {
  TeamsService,
  TeamsReport,
  TeamsAnalysis,
} from 'src/app/service/teams.service';
import {
  ValuesService,
  ValuesReport,
  ValuesAnalysis,
} from 'src/app/service/values.service';
import { PdfExportService } from 'src/app/service/pdf-export.service';
import { EmailService, EmailRequest } from 'src/app/service/email.service';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { TestPackReportPdfComponent } from './test-pack-report-pdf/test-pack-report-pdf.component';
import { marked } from 'marked';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../../service/auth.service';

interface TestPackReportData {
  testPackId: number;
  userId: number;
  username: string;
  createdAt: string;
  discReport?: {
    answerId: number;
    reportData: DiscReportData;
  };
  mbtiReport?: {
    answerId: number;
    report: MbtiReport;
    analysis: MbtiAnalysis;
  };
  teamsReport?: {
    answerId: number;
    report: TeamsReport;
    analysis: TeamsAnalysis;
  };
  valuesReport?: {
    answerId: number;
    report: ValuesReport;
    analysis: ValuesAnalysis;
  };
}

@Component({
  selector: 'app-test-pack-report',
  templateUrl: './test-pack-report.component.html',
  styleUrls: ['./test-pack-report.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgChartsModule,
    MatButtonModule,
    MatCardModule,
    MatDividerModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    MatSlideToggleModule,
    MatTooltipModule,
    MatSnackBarModule,
    // TestPackReportPdfComponent,
  ],
  animations: [
    fadeAnimation,
    slideInAnimation,
    listAnimation,
    tabAnimation,
    cardAnimation,
  ],
})
export class TestPackReportComponent implements OnInit {
  @ViewChild('reportContent') reportContent!: ElementRef;
  testPackId: number = 0;
  reportData: TestPackReportData | null = null;
  isLoading: boolean = true;
  error: string | null = null;
  hasDiscReport: boolean = false;
  hasMbtiReport: boolean = false;
  hasTeamsReport: boolean = false;
  hasValuesReport: boolean = false;
  activeTabIndex: number = 0;
  isSendingEmail: boolean = false;

  @ViewChild('pdfContainer', { read: ViewContainerRef })
  pdfContainer!: ViewContainerRef;
  pdfComponentRef: ComponentRef<TestPackReportPdfComponent> | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private testManager: TestManagerService,
    private discReportService: DiscReportService,
    private mbtiService: MbtiService,
    private teamsService: TeamsService,
    private valuesService: ValuesService,
    private pdfExportService: PdfExportService,
    private emailService: EmailService,
    private snackBar: MatSnackBar,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    console.log('TestPackReportComponent ngOnInit called');
    this.route.params.subscribe((params) => {
      console.log('Route params:', params);
      if (params['id']) {
        this.testPackId = +params['id'];
        console.log('Loading test pack report for ID:', this.testPackId);
        this.loadTestPackReport(this.testPackId);
      } else {
        console.error('No test pack ID found in route params');
        this.error = 'ID Test Pack tidak ditemukan';
        this.isLoading = false;
      }
    });
  }

  loadTestPackReport(testPackId: number): void {
    this.isLoading = true;
    this.error = null;

    this.testManager
      .getTestPackById(testPackId)
      .pipe(
        switchMap((testPack) => {
          if (!testPack.data) {
            throw new Error('Test pack tidak ditemukan');
          }

          const testPackData = testPack.data;
          const userId = testPackData.attributes.users.data[0]?.id;
          const username =
            testPackData.attributes.users.data[0]?.attributes.username ||
            'Unknown';
          const createdAt = testPackData.attributes.createdAt;

          // Check if user has access to this test pack
          const currentUser = this.authService.getCurrentUser();
          const isAdmin = this.authService.isAdmin();

          if (!isAdmin && currentUser.id !== userId) {
            this.error = 'Anda tidak memiliki akses ke laporan ini';
            this.isLoading = false;
            // Redirect to home after 2 seconds
            setTimeout(() => {
              this.router.navigate(['/home']);
            }, 2000);
            return throwError(
              () => new Error('Anda tidak memiliki akses ke laporan ini')
            );
          }

          const reportData: TestPackReportData = {
            testPackId,
            userId,
            username,
            createdAt,
          };

          // Handle both tests and testManager properties for backward compatibility
          const testManagers =
            testPackData.attributes.tests?.data ||
            testPackData.attributes.testManager?.data ||
            [];
          const reportObservables: Observable<any>[] = [];

          const discTestManager = testManagers.find(
            (tm: any) => tm.attributes.type === 'DISC'
          );
          const mbtiTestManager = testManagers.find(
            (tm: any) => tm.attributes.type === 'MBTI'
          );
          const teamsTestManager = testManagers.find(
            (tm: any) => tm.attributes.type === 'TEAMS'
          );
          const valuesTestManager = testManagers.find(
            (tm: any) => tm.attributes.type === 'VALUES'
          );

          if (discTestManager && discTestManager.attributes.resultId) {
            const discAnswerId = discTestManager.attributes.resultId;
            reportObservables.push(
              this.discReportService.getDiscReportByAnswerId(discAnswerId).pipe(
                map((reportData) => ({
                  type: 'DISC',
                  answerId: discAnswerId,
                  data: reportData,
                }))
              )
            );
          }

          if (mbtiTestManager && mbtiTestManager.attributes.resultId) {
            const mbtiAnswerId = mbtiTestManager.attributes.resultId;
            reportObservables.push(
              this.mbtiService.getMbtiReportById(mbtiAnswerId).pipe(
                switchMap((report) => {
                  return this.mbtiService
                    .getMbtiAnalysis(report.mbtiResult)
                    .pipe(
                      map((analysis) => ({
                        type: 'MBTI',
                        answerId: mbtiAnswerId,
                        report,
                        analysis,
                      }))
                    );
                })
              )
            );
          }

          if (teamsTestManager && teamsTestManager.attributes.resultId) {
            const teamsAnswerId = teamsTestManager.attributes.resultId;
            reportObservables.push(
              this.teamsService.getTeamsReportById(teamsAnswerId).pipe(
                switchMap((report) => {
                  return this.teamsService
                    .getTeamsAnalysis(report.highestCategories)
                    .pipe(
                      map((analysis) => ({
                        type: 'TEAMS',
                        answerId: teamsAnswerId,
                        report,
                        analysis,
                      }))
                    );
                })
              )
            );
          }

          if (valuesTestManager && valuesTestManager.attributes.resultId) {
            const valuesAnswerId = valuesTestManager.attributes.resultId;
            reportObservables.push(
              this.valuesService.getValuesReportById(valuesAnswerId).pipe(
                switchMap((report) => {
                  return this.valuesService
                    .getValuesAnalysis(report.highestCategories)
                    .pipe(
                      map((analysis) => ({
                        type: 'VALUES',
                        answerId: valuesAnswerId,
                        report,
                        analysis,
                      }))
                    );
                })
              )
            );
          }

          if (reportObservables.length === 0) {
            return of(reportData);
          }

          return forkJoin(reportObservables).pipe(
            map((results) => {
              results.forEach((result) => {
                switch (result.type) {
                  case 'DISC':
                    const processedDiscData = { ...result.data };

                    // Convert markdown in analyzer descriptions
                    if (processedDiscData.mostAnalyzer?.description) {
                      processedDiscData.mostAnalyzer.description =
                        this.convertMarkdown(
                          processedDiscData.mostAnalyzer.description
                        );
                    }
                    if (processedDiscData.leastAnalyzer?.description) {
                      processedDiscData.leastAnalyzer.description =
                        this.convertMarkdown(
                          processedDiscData.leastAnalyzer.description
                        );
                    }
                    if (processedDiscData.differenceAnalyzer?.description) {
                      processedDiscData.differenceAnalyzer.description =
                        this.convertMarkdown(
                          processedDiscData.differenceAnalyzer.description
                        );
                    }

                    reportData.discReport = {
                      answerId: result.answerId,
                      reportData: processedDiscData,
                    };
                    this.hasDiscReport = true;
                    break;
                  case 'MBTI':
                    reportData.mbtiReport = {
                      answerId: result.answerId,
                      report: result.report,
                      analysis: {
                        ...result.analysis,
                        analysisDetail: this.convertMarkdown(
                          result.analysis.analysisDetail || ''
                        ),
                      },
                    };
                    this.hasMbtiReport = true;
                    break;
                  case 'TEAMS':
                    reportData.teamsReport = {
                      answerId: result.answerId,
                      report: result.report,
                      analysis: {
                        ...result.analysis,
                        analysisDetail: this.convertMarkdown(
                          result.analysis.analysisDetail || ''
                        ),
                        combinationDetail: result.analysis.combinationDetail
                          ? this.convertMarkdown(
                              result.analysis.combinationDetail
                            )
                          : undefined,
                      },
                    };
                    this.hasTeamsReport = true;
                    break;
                  case 'VALUES':
                    reportData.valuesReport = {
                      answerId: result.answerId,
                      report: result.report,
                      analysis: {
                        ...result.analysis,
                        analysisDetail: this.convertMarkdown(
                          result.analysis.analysisDetail || ''
                        ),
                      },
                    };
                    this.hasValuesReport = true;
                    break;
                }
              });
              return reportData;
            })
          );
        })
      )
      .subscribe({
        next: (data) => {
          this.reportData = data;
          this.isLoading = false;
        },
        error: (err) => {
          console.error('Error loading test pack report:', err);
          this.error =
            'Gagal memuat data test pack report: ' +
            (err.message || 'Unknown error');
          this.isLoading = false;
        },
      });
  }

  async exportToPdf(): Promise<void> {
    if (!this.reportData) {
      console.error('Report data not found');
      return;
    }

    // Create the PDF component dynamically
    this.pdfContainer.clear();
    this.pdfComponentRef = this.pdfContainer.createComponent(
      TestPackReportPdfComponent
    );

    // Set the component inputs
    this.pdfComponentRef.instance.reportData = this.reportData;
    this.pdfComponentRef.instance.hasDiscReport = this.hasDiscReport;
    this.pdfComponentRef.instance.hasMbtiReport = this.hasMbtiReport;
    this.pdfComponentRef.instance.hasTeamsReport = this.hasTeamsReport;
    this.pdfComponentRef.instance.hasValuesReport = this.hasValuesReport;

    // Wait for the component to be initialized and rendered
    setTimeout(async () => {
      try {
        if (this.pdfComponentRef && this.pdfComponentRef.instance.pdfContent) {
          const filename = `test_pack_report_${this.testPackId}_${new Date()
            .toISOString()
            .slice(0, 10)}.pdf`;
          const pdfElement =
            this.pdfComponentRef.instance.pdfContent.nativeElement;

          // Generate the PDF using the service
          await this.pdfExportService.generatePdf(pdfElement, filename);
        } else {
          console.error('PDF content element not found');
          alert('Failed to generate PDF. Please try again.');
        }
      } catch (error) {
        console.error('Error generating PDF:', error);
        alert('Failed to generate PDF. Please try again.');
      } finally {
        // Clean up the component
        if (this.pdfComponentRef) {
          this.pdfContainer.clear();
          this.pdfComponentRef = null;
        }
      }
    }, 500); // Give time for the component to render
  }

  goBack(): void {
    this.router.navigate(['/home/<USER>/test-pack-results']);
  }

  onTabChange(event: any): void {
    this.activeTabIndex = event.index;
  }

  convertMarkdown(text: string) {
    return marked(text);
  }

  async sendReportByEmail(): Promise<void> {
    if (!this.reportData) {
      console.error('Report data not found');
      return;
    }

    this.isSendingEmail = true;

    try {
      // Create the PDF component dynamically for email content
      this.pdfContainer.clear();
      this.pdfComponentRef = this.pdfContainer.createComponent(
        TestPackReportPdfComponent
      );

      // Set the component inputs
      this.pdfComponentRef.instance.reportData = this.reportData;
      this.pdfComponentRef.instance.hasDiscReport = this.hasDiscReport;
      this.pdfComponentRef.instance.hasMbtiReport = this.hasMbtiReport;
      this.pdfComponentRef.instance.hasTeamsReport = this.hasTeamsReport;
      this.pdfComponentRef.instance.hasValuesReport = this.hasValuesReport;

      // Wait for the component to be initialized and rendered
      setTimeout(async () => {
        try {
          if (
            this.pdfComponentRef &&
            this.pdfComponentRef.instance.pdfContent
          ) {
            const pdfElement =
              this.pdfComponentRef.instance.pdfContent.nativeElement;

            // Generate PDF as base64 for email attachment
            const pdfBase64 = await this.pdfExportService.generatePdfAsBase64(
              pdfElement
            );

            // Get user email from the report data or use a default
            const userEmail = this.getUserEmail();

            if (!userEmail) {
              this.snackBar.open('Email pengguna tidak ditemukan.', 'Tutup', {
                duration: 5000,
                panelClass: ['error-snackbar'],
              });
              return;
            }

            // Prepare email request
            const emailRequest: EmailRequest = {
              to: userEmail,
              subject: `Hasil Test Pack Maxima - ${
                this.reportData?.username || 'Pengguna'
              }`,
              html: this.generateEmailHtml(),
              attachments: [
                {
                  filename: `test_pack_report_${this.testPackId}.pdf`,
                  content: pdfBase64,
                  contentType: 'application/pdf',
                },
              ],
            };

            // Send email
            this.emailService.sendTestResults(emailRequest).subscribe({
              next: () => {
                this.snackBar.open(
                  'Laporan berhasil dikirim ke email pengguna.',
                  'Tutup',
                  {
                    duration: 5000,
                    panelClass: ['success-snackbar'],
                  }
                );
                this.isSendingEmail = false;
              },
              error: (err) => {
                console.error('Error sending email:', err);
                this.snackBar.open(
                  'Gagal mengirim email. Silakan coba lagi.',
                  'Tutup',
                  {
                    duration: 5000,
                    panelClass: ['error-snackbar'],
                  }
                );
                this.isSendingEmail = false;
              },
            });
          } else {
            throw new Error('PDF content element not found');
          }
        } catch (error) {
          console.error('Error preparing email:', error);
          this.snackBar.open(
            'Gagal menyiapkan laporan untuk email. Silakan coba lagi.',
            'Tutup',
            {
              duration: 5000,
              panelClass: ['error-snackbar'],
            }
          );
          this.isSendingEmail = false;
        } finally {
          // Clean up the component
          if (this.pdfComponentRef) {
            this.pdfContainer.clear();
            this.pdfComponentRef = null;
          }
        }
      }, 500); // Give time for the component to render
    } catch (error) {
      console.error('Error in sendReportByEmail:', error);
      this.snackBar.open('Terjadi kesalahan. Silakan coba lagi.', 'Tutup', {
        duration: 5000,
        panelClass: ['error-snackbar'],
      });
      this.isSendingEmail = false;
    }
  }

  private getUserEmail(): string | null {
    // In a real implementation, you would get the user's email from the user data
    // For now, we'll return a mock email or extract it from the report data if available
    if (this.reportData && this.reportData.userId) {
      // In a real implementation, you would fetch the user's email from the user service
      // For now, we'll construct a mock email
      return `user_${this.reportData.userId}@example.com`;
    }
    return null;
  }

  private generateEmailHtml(): string {
    // Create a simple HTML email body
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f5f5f5; padding: 20px; text-align: center;">
          <h1 style="color: #333;">Maxima Potential</h1>
        </div>
        <div style="padding: 20px;">
          <h2 style="color: #333;">Hasil Test Pack Anda</h2>
          <p>Halo ${this.reportData?.username || 'Pengguna'},</p>
          <p>Terima kasih telah menyelesaikan test pack Maxima. Laporan hasil test Anda telah siap dan terlampir dalam email ini.</p>
          <p>Laporan ini berisi hasil dari test-test berikut:</p>
          <ul>
            ${this.hasDiscReport ? '<li>DISC Assessment</li>' : ''}
            ${this.hasMbtiReport ? '<li>MBTI Assessment</li>' : ''}
            ${this.hasTeamsReport ? '<li>TEAMS Assessment</li>' : ''}
            ${this.hasValuesReport ? '<li>VALUES Assessment</li>' : ''}
          </ul>
          <p>Silakan buka file PDF terlampir untuk melihat hasil lengkap test Anda.</p>
          <p>Salam,<br>Tim Maxima Potential</p>
        </div>
        <div style="background-color: #333; color: white; padding: 15px; text-align: center;">
          <p>&copy; ${new Date().getFullYear()} Maxima Potential. All rights reserved.</p>
        </div>
      </div>
    `;
  }
}
