.disc-chart-container {
  margin: 20px 0;
  padding: 15px;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  box-shadow: 0 2px 4px var(--shadow-color);
  transition: all 0.3s ease;
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
}

.chart-column {
  flex: 1;
  min-width: 250px;
  background-color: var(--bg-card);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px var(--shadow-color);
  transition: all 0.3s ease;
}

/* Specific styling for DISC charts */
.disc-chart-container.disc-three-charts .chart-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 15px;
}

.disc-chart-container.disc-three-charts .chart-column {
  flex: 1;
  min-width: 200px;
  background-color: var(--bg-card);
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 1px 3px var(--shadow-color);
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

/* Responsive adjustments for three charts */
@media (max-width: 992px) {
  .disc-chart-container.disc-three-charts .chart-column {
    min-width: 100%;
    margin-bottom: 20px;
  }
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
  background-color: var(--bg-card);
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 1px 3px var(--shadow-color);
  transition: all 0.3s ease;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-column {
    min-width: 100%;
  }

  .chart-container {
    height: 250px;
  }
}

h3 {
  margin-bottom: 15px;
  color: var(--text-primary);
  font-weight: 600;
  transition: color 0.3s ease;
}

h4 {
  margin-bottom: 10px;
  color: var(--text-secondary);
  font-weight: 500;
  text-align: center;
  transition: color 0.3s ease;
}

@media (max-width: 768px) {
  .chart-column {
    flex: 100%;
  }
}

/* Tambahan style untuk menyesuaikan dengan disc-detail-report */
canvas {
  width: 100% !important;
  height: 300px !important; /* Sesuaikan tinggi chart */
  transition: filter 0.3s ease;
}

/* Style untuk container chart */
.chart-container {
  width: 100%;
  height: 100%;
}

/* Animation styles */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in {
  animation: slideIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}



/* Mobile optimizations */
@media (max-width: 640px) {
  .chart-container {
    height: 250px;
  }

  .disc-chart-container.disc-three-charts .chart-column {
    min-width: 100%;
  }

}

