import { Component, Input, OnInit, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgChartsModule } from 'ng2-charts';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { fadeAnimation, slideInAnimation } from 'src/app/animations';

@Component({
  selector: 'app-test-pack-report-pdf',
  templateUrl: './test-pack-report-pdf.component.html',
  styleUrls: ['./test-pack-report-pdf.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgChartsModule,
    MatCardModule,
    MatDividerModule,
    MatIconModule,
  ],
  animations: [fadeAnimation, slideInAnimation],
})
export class TestPackReportPdfComponent implements OnInit {
  @ViewChild('pdfContent') pdfContent!: ElementRef;
  @Input() reportData: any;
  @Input() hasDiscReport: boolean = false;
  @Input() hasMbtiReport: boolean = false;
  @Input() hasTeamsReport: boolean = false;
  @Input() hasValuesReport: boolean = false;
  currentYear: number = new Date().getFullYear();

  constructor() {}

  ngOnInit(): void {}
}
