<div class="pdf-container" #pdfContent>
  <!-- PDF Header -->
  <div class="pdf-header">
    <div>
      <h1 class="pdf-title">Test Pack Report</h1>
      <p class="pdf-subtitle" *ngIf="reportData">
        User: {{ reportData.username }} | Created:
        {{ reportData.createdAt | date : "medium" }}
      </p>
    </div>
    <div>
      <h2 class="pdf-logo">MAXIMA</h2>
    </div>
  </div>

  <!-- Summary Section -->
  <div class="pdf-section">
    <h2 class="pdf-section-title">Test Pack Summary</h2>
    <div class="pdf-card">
      <h3 class="pdf-card-title">Combined Results from All Tests</h3>
      <div class="pdf-summary-grid">
        <div *ngIf="hasDiscReport" class="pdf-summary-item disc">
          <h3>DISC Assessment</h3>
          <p *ngIf="reportData.discReport">
            <span class="label">MOST Pattern:</span>
            {{ reportData.discReport.reportData.mostString }}<br />
            <span class="label">LEAST Pattern:</span>
            {{ reportData.discReport.reportData.leastString }}<br />
            <span class="label">DIFFERENCE Pattern:</span>
            {{ reportData.discReport.reportData.differenceString }}
          </p>
        </div>
        <div *ngIf="hasMbtiReport" class="pdf-summary-item mbti">
          <h3>MBTI Assessment</h3>
          <p *ngIf="reportData.mbtiReport">
            <span class="label">Type:</span>
            {{ reportData.mbtiReport.report.mbtiResult }}<br />
            <span class="label">Name:</span>
            {{ reportData.mbtiReport.analysis.patternName }}<br />
            <span class="label">Slogan:</span>
            {{ reportData.mbtiReport.analysis.patternSlogan }}
          </p>
        </div>
        <div *ngIf="hasTeamsReport" class="pdf-summary-item teams">
          <h3>TEAMS Assessment</h3>
          <p *ngIf="reportData.teamsReport && reportData.teamsReport.analysis">
            <span class="label">Primary Role:</span>
            {{ reportData.teamsReport.analysis.mainCategoryName }}<br />
            <span *ngIf="reportData.teamsReport.analysis.secondCategoryName">
              <span class="label">Secondary Role:</span>
              {{ reportData.teamsReport.analysis.secondCategoryName }}<br />
            </span>
            <span *ngIf="reportData.teamsReport.analysis.combinationName">
              <span class="label">Combination:</span>
              {{ reportData.teamsReport.analysis.combinationName }}
            </span>
          </p>
        </div>
        <div *ngIf="hasValuesReport" class="pdf-summary-item values">
          <h3>VALUES Assessment</h3>
          <p *ngIf="reportData.valuesReport">
            <span class="label">Primary Value:</span>
            {{ reportData.valuesReport.analysis.mainCategoryName }}<br />
            <span class="label">Score:</span>
            {{ reportData.valuesReport.analysis.mainCategoryValue }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Page break after summary -->
  <div class="page-break"></div>

  <!-- DISC Report Section -->
  <div *ngIf="hasDiscReport" class="pdf-section">
    <h2 class="pdf-section-title">DISC Assessment Results</h2>
    <div class="disc-chart-container">
      <h3 class="pdf-card-title">DISC Profiles</h3>
      <div class="chart-row">
        <div class="chart-column">
          <h4>MOST (Natural Tendencies)</h4>
          <div class="chart-container">
            <canvas
              baseChart
              [data]="reportData.discReport!.reportData.mostChartData"
              [options]="reportData.discReport!.reportData.mostChartOptions"
              [type]="reportData.discReport!.reportData.chartType"
            >
            </canvas>
          </div>
          <p class="pattern-text">
            Pattern: {{ reportData.discReport!.reportData.mostString }}
          </p>
        </div>
        <div class="chart-column">
          <h4>LEAST (Avoided Behaviors)</h4>
          <div class="chart-container">
            <canvas
              baseChart
              [data]="reportData.discReport!.reportData.leastChartData"
              [options]="reportData.discReport!.reportData.leastChartOptions"
              [type]="reportData.discReport!.reportData.chartType"
            >
            </canvas>
          </div>
          <p class="pattern-text">
            Pattern: {{ reportData.discReport!.reportData.leastString }}
          </p>
        </div>
        <div class="chart-column">
          <h4>DIFFERENCE (Adaptations)</h4>
          <div class="chart-container">
            <canvas
              baseChart
              [data]="reportData.discReport!.reportData.differenceChartData"
              [options]="
                reportData.discReport!.reportData.differenceChartOptions
              "
              [type]="reportData.discReport!.reportData.chartType"
            >
            </canvas>
          </div>
          <p class="pattern-text">
            Pattern: {{ reportData.discReport!.reportData.differenceString }}
          </p>
        </div>
      </div>
    </div>
    <div class="table-section">
      <table class="pdf-table">
        <thead>
          <tr>
            <th>Category</th>
            <th>MOST</th>
            <th>LEAST</th>
            <th>DIFFERENCE</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of reportData.discReport!.reportData.dataSource">
            <td>{{ item.category }}</td>
            <td>{{ item.most }}</td>
            <td>{{ item.least }}</td>
            <td>{{ item.difference }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pattern Analysis Section -->
    <div class="pattern-analysis-section">
      <h3 class="pattern-analysis-title">Pattern Analysis</h3>

      <!-- MOST Pattern Analysis -->
      <div
        *ngIf="reportData.discReport!.reportData.mostAnalyzer"
        class="pattern-card most"
      >
        <h4 class="pattern-header most">
          MOST Pattern:
          {{ reportData.discReport!.reportData.mostAnalyzer.pattern }} ({{
            reportData.discReport!.reportData.mostAnalyzer.patternName
          }})
        </h4>
        <p class="pattern-slogan most">
          "{{ reportData.discReport!.reportData.mostAnalyzer.patternSlogan }}"
        </p>
        <div
          class="pattern-description"
          [innerHTML]="
            reportData.discReport!.reportData.mostAnalyzer.description
          "
        ></div>
      </div>

      <!-- LEAST Pattern Analysis -->
      <div
        *ngIf="reportData.discReport!.reportData.leastAnalyzer"
        class="pattern-card least"
      >
        <h4 class="pattern-header least">
          LEAST Pattern:
          {{ reportData.discReport!.reportData.leastAnalyzer.pattern }} ({{
            reportData.discReport!.reportData.leastAnalyzer.patternName
          }})
        </h4>
        <p class="pattern-slogan least">
          "{{ reportData.discReport!.reportData.leastAnalyzer.patternSlogan }}"
        </p>
        <div
          class="pattern-description"
          [innerHTML]="
            reportData.discReport!.reportData.leastAnalyzer.description
          "
        ></div>
      </div>

      <!-- DIFFERENCE Pattern Analysis -->
      <div
        *ngIf="reportData.discReport!.reportData.differenceAnalyzer"
        class="pattern-card difference"
      >
        <h4 class="pattern-header difference">
          DIFFERENCE Pattern:
          {{ reportData.discReport!.reportData.differenceAnalyzer.pattern }} ({{
            reportData.discReport!.reportData.differenceAnalyzer.patternName
          }})
        </h4>
        <p class="pattern-slogan difference">
          "{{
            reportData.discReport!.reportData.differenceAnalyzer.patternSlogan
          }}"
        </p>
        <div
          class="pattern-description"
          [innerHTML]="
            reportData.discReport!.reportData.differenceAnalyzer.description
          "
        ></div>
      </div>
    </div>
  </div>

  <!-- Page break after DISC -->
  <div *ngIf="hasDiscReport" class="page-break"></div>

  <!-- MBTI Report Section -->
  <div *ngIf="hasMbtiReport" class="pdf-section">
    <h2 class="pdf-section-title">MBTI Assessment Results</h2>
    <div class="pdf-card">
      <h3 class="pdf-card-title">
        Your MBTI Type: {{ reportData.mbtiReport!.report.mbtiResult }}
      </h3>
      <h4 class="mbti-subtitle">
        {{ reportData.mbtiReport!.analysis.patternName }}
      </h4>
      <p class="mbti-slogan">
        "{{ reportData.mbtiReport!.analysis.patternSlogan }}"
      </p>
    </div>

    <!-- MBTI Section -->
    <div class="chart-stack">
      <!-- MBTI Chart -->
      <div class="chart-full">
        <h3 class="chart-title">MBTI Preferences</h3>
        <div class="chart-container">
          <canvas
            baseChart
            [data]="reportData.mbtiReport!.report.chartData"
            [type]="'bar'"
          >
          </canvas>
        </div>
      </div>

      <!-- MBTI Analysis -->
      <div class="chart-full">
        <h3 class="chart-title">Type Analysis</h3>
        <div class="mbti-analysis-content">
          <div
            class="analysis-text"
            [innerHTML]="reportData.mbtiReport!.analysis.analysisDetail"
          ></div>
        </div>
      </div>
    </div>

    <!-- Page break after MBTI -->
    <div *ngIf="hasMbtiReport" class="page-break"></div>

    <!-- TEAMS Report Section -->
    <div *ngIf="hasTeamsReport" class="pdf-section">
      <h2 class="pdf-section-title">TEAMS Assessment Results</h2>
      <div class="pdf-card">
        <h3 class="pdf-card-title">Your Team Roles</h3>
        <div class="teams-roles">
          <div class="role-card primary">
            <h4 class="role-title primary">Primary Role:</h4>
            <p class="role-name">
              {{ reportData.teamsReport!.analysis.mainCategoryName }}
            </p>
            <p class="role-score">
              Score: {{ reportData.teamsReport!.analysis.mainCategoryValue }}
            </p>
          </div>
          <div
            *ngIf="reportData.teamsReport!.analysis.secondCategoryName"
            class="role-card secondary"
          >
            <h4 class="role-title secondary">Secondary Role:</h4>
            <p class="role-name">
              {{ reportData.teamsReport!.analysis.secondCategoryName }}
            </p>
            <p class="role-score">
              Score: {{ reportData.teamsReport!.analysis.secondCategoryValue }}
            </p>
          </div>
        </div>
        <div
          *ngIf="reportData.teamsReport!.analysis.combinationName"
          class="role-combination"
        >
          <h4 class="role-title">Role Combination:</h4>
          <p class="role-name">
            {{ reportData.teamsReport!.analysis.combinationName }}
          </p>
        </div>
      </div>

      <!-- TEAMS Chart and Table in two columns -->
      <div class="chart-row">
        <!-- TEAMS Chart -->
        <div class="chart-column">
          <h3 class="chart-title">TEAMS Profile</h3>
          <div class="chart-container">
            <canvas
              baseChart
              [data]="reportData.teamsReport!.report.chartData"
              [type]="'line'"
            >
            </canvas>
          </div>
        </div>

        <!-- TEAMS Scores Table -->
        <div class="chart-column">
          <h3 class="chart-title">Role Scores</h3>
          <table class="pdf-table table-responsive">
            <thead>
              <tr>
                <th>Role</th>
                <th>Score</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Theorist (T)</td>
                <td>
                  {{ reportData.teamsReport!.report.categoryTotals["T"] }}
                </td>
              </tr>
              <tr>
                <td>Executor (E)</td>
                <td>
                  {{ reportData.teamsReport!.report.categoryTotals["E"] }}
                </td>
              </tr>
              <tr>
                <td>Analyzer (A)</td>
                <td>
                  {{ reportData.teamsReport!.report.categoryTotals["A"] }}
                </td>
              </tr>
              <tr>
                <td>Manager (M)</td>
                <td>
                  {{ reportData.teamsReport!.report.categoryTotals["M"] }}
                </td>
              </tr>
              <tr>
                <td>Strategist (S)</td>
                <td>
                  {{ reportData.teamsReport!.report.categoryTotals["S"] }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- TEAMS Analysis -->
      <div class="pdf-card analysis-card">
        <h3 class="analysis-title">Analysis</h3>
        <div
          class="analysis-text"
          [innerHTML]="reportData.teamsReport!.analysis.analysisDetail"
        ></div>
        <div
          *ngIf="reportData.teamsReport!.analysis.combinationDetail"
          class="teams-analysis-section"
        >
          <h4 class="teams-analysis-title">Combination Analysis:</h4>
          <div
            class="analysis-text"
            [innerHTML]="reportData.teamsReport!.analysis.combinationDetail"
          ></div>
        </div>
      </div>
    </div>

    <!-- Page break after TEAMS -->
    <div *ngIf="hasTeamsReport" class="page-break"></div>

    <!-- VALUES Report Section -->
    <div *ngIf="hasValuesReport" class="pdf-section">
      <h2 class="pdf-section-title">VALUES Assessment Results</h2>
      <div class="pdf-card">
        <h3 class="pdf-card-title">Your Primary Value</h3>
        <div class="values-primary">
          <h4 class="values-title">
            {{ reportData.valuesReport!.analysis.mainCategoryName }}
          </h4>
          <p class="values-score">
            Score: {{ reportData.valuesReport!.analysis.mainCategoryValue }}
          </p>
        </div>
      </div>

      <!-- VALUES Chart and Table in two columns -->
      <div class="chart-row">
        <!-- VALUES Chart -->
        <div class="chart-column">
          <h3 class="chart-title">VALUES Profile</h3>
          <div class="chart-container">
            <canvas
              baseChart
              [data]="reportData.valuesReport!.report.chartData"
              [type]="'line'"
            >
            </canvas>
          </div>
        </div>

        <!-- VALUES Scores Table -->
        <div class="chart-column">
          <h3 class="chart-title">Value Scores</h3>
          <table class="pdf-table table-responsive">
            <thead>
              <tr>
                <th>Value</th>
                <th>Score</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Loyalty (L)</td>
                <td>
                  {{ reportData.valuesReport!.report.categoryTotals["L"] }}
                </td>
              </tr>
              <tr>
                <td>Equality (E)</td>
                <td>
                  {{ reportData.valuesReport!.report.categoryTotals["E"] }}
                </td>
              </tr>
              <tr>
                <td>Personal Freedom (P)</td>
                <td>
                  {{ reportData.valuesReport!.report.categoryTotals["P"] }}
                </td>
              </tr>
              <tr>
                <td>Justice (J)</td>
                <td>
                  {{ reportData.valuesReport!.report.categoryTotals["J"] }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- VALUES Analysis -->
      <div class="pdf-card analysis-card">
        <h3 class="analysis-title">Value Analysis</h3>
        <div
          class="analysis-text"
          [innerHTML]="reportData.valuesReport!.analysis.analysisDetail"
        ></div>
      </div>
    </div>

    <!-- PDF Footer -->
    <div class="pdf-footer">
      <p>
        Generated on {{ reportData.createdAt | date : "medium" }} | Maxima
        Assessment Platform
      </p>
      <p>© {{ currentYear }} Maxima. All rights reserved.</p>
    </div>
  </div>
</div>
