.pdf-container {
  background-color: white;
  padding: 20px;
  font-family: 'Avenir Next', Avenir, 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.pdf-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.pdf-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.pdf-logo {
  font-size: 28px;
  font-weight: bold;
  color: #3f51b5;
  letter-spacing: 2px;
  text-align: right;
}

.pdf-subtitle {
  font-size: 14px;
  color: #666;
}

.pdf-section {
  margin-bottom: 30px;
  padding: 20px 0;
}

.pdf-section-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
  padding-bottom: 5px;
  border-bottom: 1px solid #e0e0e0;
}

.pdf-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pdf-card-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.pdf-card-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.pdf-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.pdf-summary-item {
  padding: 15px;
  border-radius: 8px;
}

.pdf-summary-item.disc {
  background-color: #e3f2fd;
}

.pdf-summary-item.mbti {
  background-color: #e8f5e9;
}

.pdf-summary-item.teams {
  background-color: #f3e5f5;
}

.pdf-summary-item.values {
  background-color: #fff8e1;
}

.pdf-summary-item h3 {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.pdf-summary-item p {
  font-size: 14px;
  margin-bottom: 5px;
}

.pdf-summary-item .label {
  font-weight: 600;
}

.disc-chart-container {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-stack {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 30px;
  margin-bottom: 30px;
}

.chart-full {
  width: 100%;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.chart-container {
  width: 100%;
  overflow-x: auto;
}

.mbti-analysis-content {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #444;
}

.analysis-text {
  white-space: pre-wrap;
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
}

.chart-column {
  flex: 1;
  min-width: 250px;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-column h4 {
  text-align: center;
  margin-bottom: 10px;
  font-weight: 500;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pdf-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.pdf-table th {
  background-color: #f5f5f5;
  padding: 10px;
  text-align: left;
  font-weight: bold;
  border: 1px solid #e0e0e0;
}

.pdf-table td {
  padding: 10px;
  border: 1px solid #e0e0e0;
}

.pdf-footer {
  margin-top: 30px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
  text-align: center;
  font-size: 12px;
  color: #666;
}

canvas {
  width: 100% !important;
  height: 300px !important;
}

.page-break {
  page-break-after: always;
  height: 0;
  margin: 0;
  padding: 0;
}

@media print {
  .chart-stack,
  .chart-full {
    page-break-inside: avoid;
  }

  .page-break {
    page-break-after: always;
  }
  
  /* Better PDF spacing */
  .pdf-section {
    margin-bottom: 40px;
    page-break-inside: avoid;
  }
  
  .pdf-card {
    page-break-inside: avoid;
    margin-bottom: 20px;
  }
  
  .pattern-analysis-section {
    page-break-inside: avoid;
  }
  
  .chart-row {
    page-break-inside: avoid;
    margin-bottom: 30px;
  }
  
  /* Ensure charts don't break across pages */
  .chart-column {
    page-break-inside: avoid;
  }
  
  /* MBTI analysis content specific for PDF */
  .mbti-analysis-content {
    page-break-inside: avoid;
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
  }
  
  /* Better table handling */
  .pdf-table {
    page-break-inside: auto;
  }
  
  .pdf-table tr {
    page-break-inside: avoid;
  }
}

/* Pattern Analysis styles */
.pattern-analysis-section {
  margin-top: 30px;
  margin-bottom: 40px;
}

.pattern-analysis-title {
  font-size: 18px;
  font-weight: 700;
  color: #1976d2;
  margin-bottom: 20px;
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 2px solid #e3f2fd;
}

.pattern-card {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pattern-card.most {
  background-color: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.pattern-card.least {
  background-color: #fff3e0;
  border-left: 4px solid #ff9800;
}

.pattern-card.difference {
  background-color: #f1f8e9;
  border-left: 4px solid #388e3c;
}

.pattern-header {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
}

.pattern-header.most {
  color: #2e7d32;
}

.pattern-header.least {
  color: #e65100;
}

.pattern-header.difference {
  color: #388e3c;
}

.pattern-slogan {
  font-style: italic;
  margin-bottom: 8px;
  font-weight: 500;
}

.pattern-slogan.most {
  color: #2e7d32;
}

.pattern-slogan.least {
  color: #ef6c00;
}

.pattern-slogan.difference {
  color: #2e7d32;
}

.pattern-description {
  font-size: 12px;
  line-height: 1.6;
  color: #333;
  text-align: justify;
}

/* Support for HTML content in pattern description */
.pattern-description p {
  margin-bottom: 10px;
}

.pattern-description strong, 
.pattern-description b {
  font-weight: 600;
  color: #222;
}

.pattern-description em, 
.pattern-description i {
  font-style: italic;
}

.pattern-description ul,
.pattern-description ol {
  margin: 10px 0;
  padding-left: 20px;
}

.pattern-description li {
  margin-bottom: 5px;
}

.chart-row {
  display: flex;
  gap: 20px;
  margin-top: 30px;
  margin-bottom: 30px;
}

.chart-column {
  flex: 1;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.analysis-row {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.analysis-column {
  flex: 1;
}

/* Chart title styles */
.chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  text-align: center;
}

.chart-container {
  height: 250px;
}

.analysis-content {
  max-height: none;
  overflow: visible;
  min-height: 250px;
}

/* MBTI specific analysis content - no scrolling, full display */
.mbti-analysis-content {
  overflow: visible;
  height: auto;
  max-height: none;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.analysis-text {
  font-size: 12px;
  line-height: 1.6;
  color: #333;
  text-align: justify;
}

/* Support for HTML content in analysis text */
.analysis-text p {
  margin-bottom: 10px;
}

.analysis-text strong, 
.analysis-text b {
  font-weight: 600;
  color: #222;
}

.analysis-text em, 
.analysis-text i {
  font-style: italic;
}

.analysis-text ul,
.analysis-text ol {
  margin: 10px 0;
  padding-left: 20px;
}

.analysis-text li {
  margin-bottom: 5px;
}

.pattern-text {
  text-align: center;
  margin-top: 10px;
  font-weight: 500;
  font-size: 12px;
  color: #555;
}

.table-section {
  margin-top: 20px;
}

/* TEAMS role cards */
.teams-roles {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.role-card {
  padding: 15px;
  border-radius: 8px;
  flex: 1;
  min-width: 200px;
}

.role-card.primary {
  background-color: #f3e5f5;
}

.role-card.secondary {
  background-color: #e8eaf6;
}

.role-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}

.role-title.primary {
  color: #6a1b9a;
}

.role-title.secondary {
  color: #3949ab;
}

.role-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.role-score {
  font-size: 14px;
  color: #666;
}

.role-combination {
  margin-top: 15px;
}

/* VALUES styles */
.values-primary {
  background-color: #fff8e1;
  border-left: 4px solid #ff8f00;
}

.values-title {
  font-size: 16px;
  font-weight: 500;
  color: #fc9712;
  margin-bottom: 5px;
}

.values-score {
  font-size: 14px;
  color: #0f0f0f;
}

.table-responsive {
  width: 100%;
}

.analysis-card {
  margin-top: 20px;
}

.analysis-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

/* MBTI specific styles */
.mbti-subtitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}

.mbti-slogan {
  font-style: italic;
  margin-bottom: 15px;
  color: #666;
}

/* Teams analysis section */
.teams-analysis-section {
  margin-top: 15px;
}

.teams-analysis-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}
