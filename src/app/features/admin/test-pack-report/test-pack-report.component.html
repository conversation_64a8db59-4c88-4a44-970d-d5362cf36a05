<div class="max-w-5xl mx-auto px-4 py-8" [@fadeAnimation]>
  <!-- Container for PDF component -->
  <div #pdfContainer></div>
  <!-- Header -->
  <div class="flex justify-between items-center mb-6" [@slideInAnimation]>
    <div>
      <h1 class="text-2xl font-bold">Test Pack Report</h1>
      <p *ngIf="reportData">
        User: {{ reportData.username }} | Created:
        {{ reportData.createdAt | date : "medium" }}
      </p>
    </div>
    <div class="flex space-x-2">
      <button
        mat-raised-button
        color="primary"
        (click)="exportToPdf()"
        [disabled]="isLoading || !reportData"
        matTooltip="Export report to PDF"
      >
        <mat-icon>picture_as_pdf</mat-icon>
        Export to PDF
      </button>
      <button
        mat-raised-button
        color="accent"
        (click)="sendReportByEmail()"
        [disabled]="isLoading || !reportData || isSendingEmail"
        matTooltip="Send report to user's email"
      >
        <mat-icon>email</mat-icon>
        {{ isSendingEmail ? "Sending..." : "Send by Email" }}
      </button>
      <button mat-button (click)="goBack()" matTooltip="Return to results page">
        <mat-icon>arrow_back</mat-icon>
        Back
      </button>
    </div>
  </div>
  <!-- Loading State -->
  <div
    *ngIf="isLoading"
    class="flex justify-center items-center py-12"
    [@fadeAnimation]
  >
    <mat-spinner diameter="50"></mat-spinner>
    <span class="ml-4">Loading test pack report...</span>
  </div>
  <!-- Error State -->
  <div
    *ngIf="error"
    class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6"
    [@slideInAnimation]
  >
    <div class="flex">
      <mat-icon class="text-red-500 mr-2">error</mat-icon>
      <span>{{ error }}</span>
    </div>
  </div>
  <!-- Report Content -->
  <div
    *ngIf="reportData && !isLoading && !error"
    #reportContent
    [@fadeAnimation]
  >
    <mat-card class="mb-6" [@cardAnimation]>
      <mat-card-header>
        <mat-card-title>Test Pack Summary</mat-card-title>
        <mat-card-subtitle>Combined results from all tests</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div
          class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"
          [@listAnimation]="reportData ? 'in' : 'out'"
        >
          <div *ngIf="hasDiscReport" class="bg-blue-50 p-4 rounded-md">
            <h3 class="font-semibold text-blue-800">DISC Assessment</h3>
            <p *ngIf="reportData.discReport">
              <span class="font-medium">MOST Pattern:</span>
              {{ reportData.discReport.reportData.mostString }}<br />
              <span class="font-medium">LEAST Pattern:</span>
              {{ reportData.discReport.reportData.leastString }}<br />
              <span class="font-medium">DIFFERENCE Pattern:</span>
              {{ reportData.discReport.reportData.differenceString }}
            </p>
          </div>
          <div *ngIf="hasMbtiReport" class="bg-green-50 p-4 rounded-md">
            <h3 class="font-semibold text-green-800">MBTI Assessment</h3>
            <p *ngIf="reportData.mbtiReport">
              <span class="font-medium">Type:</span>
              {{ reportData.mbtiReport.report.mbtiResult }}<br />
              <span class="font-medium">Name:</span>
              {{ reportData.mbtiReport.analysis.patternName }}<br />
              <span class="font-medium">Slogan:</span>
              {{ reportData.mbtiReport.analysis.patternSlogan }}
            </p>
          </div>
          <div *ngIf="hasTeamsReport" class="bg-purple-50 p-4 rounded-md">
            <h3 class="font-semibold text-purple-800">TEAMS Assessment</h3>
            <p
              *ngIf="reportData.teamsReport && reportData.teamsReport.analysis"
            >
              <span class="font-medium">Primary Role:</span>
              {{ reportData.teamsReport.analysis.mainCategoryName }}<br />
              <span *ngIf="reportData.teamsReport.analysis.secondCategoryName">
                <span class="font-medium">Secondary Role:</span>
                {{ reportData.teamsReport.analysis.secondCategoryName }}<br />
              </span>
              <span *ngIf="reportData.teamsReport.analysis.combinationName">
                <span class="font-medium">Combination:</span>
                {{ reportData.teamsReport.analysis.combinationName }}
              </span>
            </p>
          </div>
          <div *ngIf="hasValuesReport" class="bg-amber-50 p-4 rounded-md">
            <h3 class="font-semibold text-amber-800">VALUES Assessment</h3>
            <p *ngIf="reportData.valuesReport">
              <span class="font-medium">Primary Value:</span>
              {{ reportData.valuesReport.analysis.mainCategoryName }}<br />
              <span class="font-medium">Score:</span>
              {{ reportData.valuesReport.analysis.mainCategoryValue }}
            </p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
    <!-- Detailed Reports -->
    <mat-card [@cardAnimation]>
      <mat-card-header>
        <mat-card-title>Detailed Test Results</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <mat-tab-group dynamicHeight (selectedTabChange)="onTabChange($event)">
          <!-- DISC Tab -->
          <mat-tab label="DISC" *ngIf="hasDiscReport">
            <div class="p-4" [@tabAnimation] *ngIf="activeTabIndex === 0">
              <h2 class="text-xl font-bold mb-4">DISC Assessment Results</h2>
              <div class="disc-chart-container disc-three-charts mb-6">
                <h3 class="text-lg font-semibold mb-2">DISC Profiles</h3>
                <div class="chart-row">
                  <div class="chart-column">
                    <h4>MOST (Natural Tendencies)</h4>
                    <div class="chart-container">
                      <canvas
                        baseChart
                        [data]="reportData.discReport!.reportData.mostChartData"
                        [options]="
                          reportData.discReport!.reportData.mostChartOptions
                        "
                        [type]="reportData.discReport!.reportData.chartType"
                      >
                      </canvas>
                    </div>
                    <p class="text-center mt-2 font-medium">
                      Pattern:
                      {{ reportData.discReport!.reportData.mostString }}
                    </p>
                  </div>
                  <div class="chart-column">
                    <h4>LEAST (Avoided Behaviors)</h4>
                    <div class="chart-container">
                      <canvas
                        baseChart
                        [data]="
                          reportData.discReport!.reportData.leastChartData
                        "
                        [options]="
                          reportData.discReport!.reportData.leastChartOptions
                        "
                        [type]="reportData.discReport!.reportData.chartType"
                      >
                      </canvas>
                    </div>
                    <p class="text-center mt-2 font-medium">
                      Pattern:
                      {{ reportData.discReport!.reportData.leastString }}
                    </p>
                  </div>
                  <div class="chart-column">
                    <h4>DIFFERENCE (Adaptations)</h4>
                    <div class="chart-container">
                      <canvas
                        baseChart
                        [data]="
                          reportData.discReport!.reportData.differenceChartData
                        "
                        [options]="
                          reportData.discReport!.reportData
                            .differenceChartOptions
                        "
                        [type]="reportData.discReport!.reportData.chartType"
                      >
                      </canvas>
                    </div>
                    <p class="text-center mt-2 font-medium">
                      Pattern:
                      {{ reportData.discReport!.reportData.differenceString }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th
                        class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left"
                      >
                        Category
                      </th>
                      <th
                        class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left"
                      >
                        MOST
                      </th>
                      <th
                        class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left"
                      >
                        LEAST
                      </th>
                      <th
                        class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left"
                      >
                        DIFFERENCE
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let item of reportData.discReport!.reportData.dataSource
                      "
                    >
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{ item.category }}
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{ item.most }}
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{ item.least }}
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{ item.difference }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Analyzer Data Section -->
              <div class="mt-8 space-y-6">
                <h3 class="text-lg font-semibold mb-4">Pattern Analysis</h3>

                <!-- MOST Pattern Analysis -->
                <div
                  *ngIf="reportData.discReport!.reportData.mostAnalyzer"
                  class="bg-blue-50 p-6 rounded-lg"
                >
                  <h4 class="text-md font-semibold text-blue-800 mb-3">
                    MOST Pattern:
                    {{ reportData.discReport!.reportData.mostAnalyzer.pattern }}
                    <span class="text-sm font-normal ml-2"
                      >({{
                        reportData.discReport!.reportData.mostAnalyzer
                          .patternName
                      }})</span
                    >
                  </h4>
                  <p class="italic text-blue-700 mb-3">
                    "{{
                      reportData.discReport!.reportData.mostAnalyzer
                        .patternSlogan
                    }}"
                  </p>
                  <div class="text-gray-700 leading-relaxed">
                    {{
                      reportData.discReport!.reportData.mostAnalyzer.description
                    }}
                  </div>
                </div>

                <!-- LEAST Pattern Analysis -->
                <div
                  *ngIf="reportData.discReport!.reportData.leastAnalyzer"
                  class="bg-orange-50 p-6 rounded-lg"
                >
                  <h4 class="text-md font-semibold text-orange-800 mb-3">
                    LEAST Pattern:
                    {{
                      reportData.discReport!.reportData.leastAnalyzer.pattern
                    }}
                    <span class="text-sm font-normal ml-2"
                      >({{
                        reportData.discReport!.reportData.leastAnalyzer
                          .patternName
                      }})</span
                    >
                  </h4>
                  <p class="italic text-orange-700 mb-3">
                    "{{
                      reportData.discReport!.reportData.leastAnalyzer
                        .patternSlogan
                    }}"
                  </p>
                  <div class="text-gray-700 leading-relaxed">
                    {{
                      reportData.discReport!.reportData.leastAnalyzer
                        .description
                    }}
                  </div>
                </div>

                <!-- DIFFERENCE Pattern Analysis -->
                <div
                  *ngIf="reportData.discReport!.reportData.differenceAnalyzer"
                  class="bg-green-50 p-6 rounded-lg"
                >
                  <h4 class="text-md font-semibold text-green-800 mb-3">
                    DIFFERENCE Pattern:
                    {{
                      reportData.discReport!.reportData.differenceAnalyzer
                        .pattern
                    }}
                    <span class="text-sm font-normal ml-2"
                      >({{
                        reportData.discReport!.reportData.differenceAnalyzer
                          .patternName
                      }})</span
                    >
                  </h4>
                  <p class="italic text-green-700 mb-3">
                    "{{
                      reportData.discReport!.reportData.differenceAnalyzer
                        .patternSlogan
                    }}"
                  </p>
                  <div class="text-gray-700 leading-relaxed">
                    {{
                      reportData.discReport!.reportData.differenceAnalyzer
                        .description
                    }}
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
          <!-- MBTI Tab -->
          <mat-tab label="MBTI" *ngIf="hasMbtiReport">
            <div
              class="p-4"
              [@tabAnimation]
              *ngIf="activeTabIndex === (hasDiscReport ? 1 : 0)"
            >
              <h2 class="text-xl font-bold mb-4">MBTI Assessment Results</h2>
              <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                <h3 class="text-lg font-semibold mb-2">
                  Your MBTI Type: {{ reportData.mbtiReport!.report.mbtiResult }}
                </h3>
                <h4 class="text-md font-medium mb-1">
                  {{ reportData.mbtiReport!.analysis.patternName }}
                </h4>
                <p class="italic mb-4">
                  "{{ reportData.mbtiReport!.analysis.patternSlogan }}"
                </p>
                <div class="mt-4">
                  <h4 class="text-md font-medium mb-2">Type Analysis:</h4>
                  <p
                    class="whitespace-pre-line"
                    [innerHTML]="
                      convertMarkdown(
                        reportData.mbtiReport!.analysis.analysisDetail
                      )
                    "
                  ></p>
                </div>
              </div>
              <div
                class="chart-container bg-white p-4 rounded-lg shadow-sm mb-6"
              >
                <h3 class="text-lg font-semibold mb-2">MBTI Preferences</h3>
                <div style="height: 300px">
                  <canvas
                    baseChart
                    [data]="reportData.mbtiReport!.report.chartData"
                    [type]="'bar'"
                  >
                  </canvas>
                </div>
              </div>
            </div>
          </mat-tab>
          <!-- TEAMS Tab -->
          <mat-tab label="TEAMS" *ngIf="hasTeamsReport">
            <div
              class="p-4"
              [@tabAnimation]
              *ngIf="
                activeTabIndex ===
                (hasDiscReport
                  ? hasMbtiReport
                    ? 2
                    : 1
                  : hasMbtiReport
                  ? 1
                  : 0)
              "
            >
              <h2 class="text-xl font-bold mb-4">TEAMS Assessment Results</h2>
              <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                <h3 class="text-lg font-semibold mb-2">Your Team Roles</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div class="bg-purple-50 p-4 rounded-md">
                    <h4 class="font-medium text-purple-800">Primary Role:</h4>
                    <p class="text-lg font-bold">
                      {{ reportData.teamsReport!.analysis.mainCategoryName }}
                    </p>
                    <p class="text-sm">
                      Score:
                      {{ reportData.teamsReport!.analysis.mainCategoryValue }}
                    </p>
                  </div>
                  <div
                    *ngIf="reportData.teamsReport!.analysis.secondCategoryName"
                    class="bg-indigo-50 p-4 rounded-md"
                  >
                    <h4 class="font-medium text-indigo-800">Secondary Role:</h4>
                    <p class="text-lg font-bold">
                      {{ reportData.teamsReport!.analysis.secondCategoryName }}
                    </p>
                    <p
                      class="text-sm"
                      *ngIf="
                        reportData.teamsReport!.analysis.secondCategoryValue
                      "
                    >
                      Score:
                      {{ reportData.teamsReport!.analysis.secondCategoryValue }}
                    </p>
                  </div>
                </div>
                <div
                  *ngIf="reportData.teamsReport!.analysis.combinationName"
                  class="mt-4"
                >
                  <h4 class="font-medium">Role Combination:</h4>
                  <p class="text-lg font-bold">
                    {{ reportData.teamsReport!.analysis.combinationName }}
                  </p>
                </div>
                <div class="mt-4">
                  <h4 class="text-md font-medium mb-2">Analysis:</h4>
                  <p
                    class="whitespace-pre-line"
                    [innerHTML]="
                      convertMarkdown(
                        reportData.teamsReport!.analysis.analysisDetail
                      )
                    "
                  ></p>
                </div>
                <div
                  *ngIf="reportData.teamsReport!.analysis.combinationDetail"
                  class="mt-4"
                >
                  <h4 class="text-md font-medium mb-2">
                    Combination Analysis:
                  </h4>
                  <p
                    class="whitespace-pre-line"
                    [innerHTML]="
                      convertMarkdown(
                        reportData.teamsReport!.analysis.combinationDetail
                      )
                    "
                  ></p>
                </div>
              </div>
              <div
                class="chart-container bg-white p-4 rounded-lg shadow-sm mb-6"
              >
                <h3 class="text-lg font-semibold mb-2">TEAMS Profile</h3>
                <div style="height: 300px">
                  <canvas
                    baseChart
                    [data]="reportData.teamsReport!.report.chartData"
                    [type]="'line'"
                  >
                  </canvas>
                </div>
              </div>
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th
                        class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left"
                      >
                        Role
                      </th>
                      <th
                        class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left"
                      >
                        Score
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td class="px-4 py-2 border-b border-gray-200">
                        Theorist (T)
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{ reportData.teamsReport!.report.categoryTotals["T"] }}
                      </td>
                    </tr>
                    <tr>
                      <td class="px-4 py-2 border-b border-gray-200">
                        Executor (E)
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{ reportData.teamsReport!.report.categoryTotals["E"] }}
                      </td>
                    </tr>
                    <tr>
                      <td class="px-4 py-2 border-b border-gray-200">
                        Analyzer (A)
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{ reportData.teamsReport!.report.categoryTotals["A"] }}
                      </td>
                    </tr>
                    <tr>
                      <td class="px-4 py-2 border-b border-gray-200">
                        Manager (M)
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{ reportData.teamsReport!.report.categoryTotals["M"] }}
                      </td>
                    </tr>
                    <tr>
                      <td class="px-4 py-2 border-b border-gray-200">
                        Strategist (S)
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{ reportData.teamsReport!.report.categoryTotals["S"] }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </mat-tab>
          <!-- VALUES Tab -->
          <mat-tab label="VALUES" *ngIf="hasValuesReport">
            <div
              class="p-4"
              [@tabAnimation]
              *ngIf="
                activeTabIndex ===
                (hasDiscReport
                  ? hasMbtiReport
                    ? hasTeamsReport
                      ? 3
                      : 2
                    : hasTeamsReport
                    ? 2
                    : 1
                  : hasMbtiReport
                  ? hasTeamsReport
                    ? 2
                    : 1
                  : hasTeamsReport
                  ? 1
                  : 0)
              "
            >
              <h2 class="text-xl font-bold mb-4">VALUES Assessment Results</h2>
              <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                <h3 class="text-lg font-semibold mb-2">Your Primary Value</h3>
                <div class="bg-amber-50 p-4 rounded-md inline-block">
                  <h4 class="font-medium text-amber-800">
                    {{ reportData.valuesReport!.analysis.mainCategoryName }}
                  </h4>
                  <p class="text-sm">
                    Score:
                    {{ reportData.valuesReport!.analysis.mainCategoryValue }}
                  </p>
                </div>
                <div class="mt-4">
                  <h4 class="text-md font-medium mb-2">Value Analysis:</h4>
                  <p
                    class="whitespace-pre-line"
                    [innerHTML]="
                      convertMarkdown(
                        reportData.valuesReport!.analysis.analysisDetail
                      )
                    "
                  ></p>
                </div>
              </div>
              <div
                class="chart-container bg-white p-4 rounded-lg shadow-sm mb-6"
              >
                <h3 class="text-lg font-semibold mb-2">VALUES Profile</h3>
                <div style="height: 300px">
                  <canvas
                    baseChart
                    [data]="reportData.valuesReport!.report.chartData"
                    [type]="'line'"
                  >
                  </canvas>
                </div>
              </div>
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th
                        class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left"
                      >
                        Value
                      </th>
                      <th
                        class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left"
                      >
                        Score
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td class="px-4 py-2 border-b border-gray-200">
                        Loyalty (L)
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{
                          reportData.valuesReport!.report.categoryTotals["L"]
                        }}
                      </td>
                    </tr>
                    <tr>
                      <td class="px-4 py-2 border-b border-gray-200">
                        Equality (E)
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{
                          reportData.valuesReport!.report.categoryTotals["E"]
                        }}
                      </td>
                    </tr>
                    <tr>
                      <td class="px-4 py-2 border-b border-gray-200">
                        Personal Freedom (P)
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{
                          reportData.valuesReport!.report.categoryTotals["P"]
                        }}
                      </td>
                    </tr>
                    <tr>
                      <td class="px-4 py-2 border-b border-gray-200">
                        Justice (J)
                      </td>
                      <td class="px-4 py-2 border-b border-gray-200">
                        {{
                          reportData.valuesReport!.report.categoryTotals["J"]
                        }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>
    </mat-card>
  </div>
</div>
