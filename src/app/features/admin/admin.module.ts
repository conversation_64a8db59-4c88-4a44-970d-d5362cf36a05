import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { AdminRoutingModule } from 'src/app/features/admin/admin-routing.module';
import { ManageTestAccessComponent } from './manage-test-access/manage-test-access.component';
import { TestPackResultsComponent } from './test-pack-results/test-pack-results.component';
import { TestPackReportComponent } from './test-pack-report/test-pack-report.component';
import { TestReportAiComponent } from './test-report-ai/test-report-ai.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    SharedModule,
    AdminRoutingModule,
    // If these components are standalone, they should be imported, not declared
    ManageTestAccessComponent,
    TestPackResultsComponent,
    TestPackReportComponent,
    TestReportAiComponent,
  ],
})
export class AdminModule {}
