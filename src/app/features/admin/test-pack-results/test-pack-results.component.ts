import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/service/auth.service';
import { TestManagerService } from 'src/app/service/test-manager.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

interface TestPackRecord {
  id: number;
  createdAt: string;
  status: 'open' | 'finish';
  users?: {
    data: Array<{
      id: number;
      attributes: {
        username: string;
        email: string;
        provider: string;
        confirmed: boolean;
        blocked: boolean;
      };
    }>;
  };
  tests: {
    data: Array<any>;
  };
}

@Component({
  selector: 'app-test-pack-results',
  templateUrl: './test-pack-results.component.html',
  standalone: true,
  imports: [CommonModule, MatTableModule, MatPaginatorModule, MatButtonModule],
})
export class TestPackResultsComponent implements OnInit {
  displayedColumns: string[] = [];
  dataSource = new MatTableDataSource<TestPackRecord>();
  isLoading = true;
  error: string | null = null;
  isAdmin = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private testManager: TestManagerService,
    private authService: AuthService,
    private router: Router,
    private http: HttpClient
  ) {}

  ngOnInit() {
    // Cek role user dari token
    const user = this.authService.getCurrentUser();
    this.isAdmin = user.role === 'admin';

    // Set displayedColumns berdasarkan role
    this.displayedColumns = this.isAdmin
      ? ['createdAt', 'status', 'users', 'actions']
      : ['createdAt', 'status', 'actions'];

    this.loadTestPackResults();
  }

  loadTestPackResults() {
    this.isLoading = true;
    const user = this.authService.getCurrentUser();

    // Jika admin, ambil semua record. Jika member, ambil record user tersebut saja
    const url = this.isAdmin
      ? `${this.testManager.getTestPackUrl()}?populate=*`
      : `${this.testManager.getTestPackUrl()}?populate=*&filters[users][id][$eq]=${
          user.id
        }`;

    this.testManager.getTestPackResults(url).subscribe({
      next: (results: any) => {
        const testPackRecords: TestPackRecord[] = results.data.map(
          (item: any) => ({
            id: item.id,
            createdAt: item.attributes.createdAt,
            status: item.attributes.status,
            users: item.attributes.users,
            tests: item.attributes.tests,
          })
        );

        this.dataSource.data = testPackRecords;
        this.dataSource.paginator = this.paginator;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading test pack results:', error);
        this.error = 'Gagal memuat data test pack';
        this.isLoading = false;
      },
    });
  }

  generateReport(testPackId: number) {
    // Navigate to the report page with the correct path
    this.router.navigate(['/home/<USER>/test-pack-report', testPackId]);
  }

  generateAiReport(testPackId: number) {
    // Navigate to the AI report page with the correct path
    this.router.navigate(['/home/<USER>/test-report-ai', testPackId]);
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'open':
        return 'bg-yellow-100 text-yellow-800';
      case 'finish':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'open':
        return 'Dalam Proses';
      case 'finish':
        return 'Selesai';
      default:
        return status;
    }
  }

  getUserName(users: any): string {
    if (users?.data?.[0]?.attributes?.username) {
      return users.data[0].attributes.username;
    }
    return 'N/A';
  }
}
