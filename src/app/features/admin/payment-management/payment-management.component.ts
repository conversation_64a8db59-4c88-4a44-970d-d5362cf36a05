import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment.development';
import { catchError, finalize } from 'rxjs/operators';
import { of } from 'rxjs';

interface Payment {
  id: number;
  attributes: {
    externalId: string;
    tripayId: string;
    amount: number;
    status: 'PENDING' | 'PAID' | 'FAILED' | 'EXPIRED';
    paymentMethod?: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    paidAt?: string;
    user?: {
      data: {
        id: number;
        attributes: {
          username: string;
          email: string;
        };
      };
    };
    testPack?: {
      data: {
        id: number;
        attributes: {
          status: string;
        };
      };
    };
  };
}

interface PaymentResponse {
  data: Payment[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

@Component({
  selector: 'app-payment-management',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatMenuModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './payment-management.component.html',
  styleUrls: ['./payment-management.component.css'],
})
export class PaymentManagementComponent implements OnInit {
  displayedColumns: string[] = [
    'id',
    'externalId',
    'amount',
    'status',
    'createdAt',
    'paidAt',
    'user',
    'testPack',
    'actions',
  ];
  dataSource: Payment[] = [];
  isLoading = true;
  error: string | null = null;

  // Pagination
  totalItems = 0;
  pageSize = 10;
  currentPage = 1;
  pageSizeOptions = [5, 10, 25, 50];

  // Filtering
  statusFilter: string = '';
  searchQuery: string = '';

  constructor(private http: HttpClient, private snackBar: MatSnackBar) {}

  ngOnInit(): void {
    this.loadPayments();
  }

  loadPayments(): void {
    this.isLoading = true;

    // Build query parameters
    let queryParams = `?populate=user,testPack&pagination[page]=${this.currentPage}&pagination[pageSize]=${this.pageSize}`;

    // Add filters if set
    if (this.statusFilter) {
      queryParams += `&filters[status][$eq]=${this.statusFilter}`;
    }

    if (this.searchQuery) {
      queryParams += `&filters[$or][0][externalId][$containsi]=${this.searchQuery}`;
      queryParams += `&filters[$or][1][user][username][$containsi]=${this.searchQuery}`;
      queryParams += `&filters[$or][2][description][$containsi]=${this.searchQuery}`;
    }

    this.http
      .get<PaymentResponse>(`${environment.url}payments${queryParams}`)
      .pipe(
        catchError((error) => {
          console.error('Error loading payments:', error);
          this.error = 'Failed to load payments. Please try again.';
          return of({
            data: [],
            meta: {
              pagination: { page: 1, pageSize: 10, pageCount: 0, total: 0 },
            },
          } as PaymentResponse);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((response) => {
        this.dataSource = response.data;
        this.totalItems = response.meta.pagination.total;
      });
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadPayments();
  }

  onSortChange(sort: Sort): void {
    // Implement sorting logic here
    this.loadPayments();
  }

  applyFilters(): void {
    this.currentPage = 1; // Reset to first page when filtering
    this.loadPayments();
  }

  resetFilters(): void {
    this.statusFilter = '';
    this.searchQuery = '';
    this.currentPage = 1;
    this.loadPayments();
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PAID':
        return 'status-paid';
      case 'PENDING':
        return 'status-pending';
      case 'FAILED':
        return 'status-failed';
      case 'EXPIRED':
        return 'status-expired';
      default:
        return '';
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  formatAmount(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  viewPaymentDetails(payment: Payment): void {
    // Implement view details functionality
    console.log('View payment details:', payment);
  }

  updatePaymentStatus(
    payment: Payment,
    newStatus: 'PAID' | 'FAILED' | 'EXPIRED'
  ): void {
    this.isLoading = true;

    const updateData = {
      data: {
        status: newStatus,
        paidAt: newStatus === 'PAID' ? new Date().toISOString() : null,
      },
    };

    this.http
      .put(`${environment.url}payments/${payment.id}`, updateData)
      .pipe(
        catchError((error) => {
          console.error('Error updating payment status:', error);
          this.snackBar.open('Failed to update payment status', 'Close', {
            duration: 3000,
            panelClass: ['error-snackbar'],
          });
          return of(null);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((response) => {
        if (response) {
          this.snackBar.open(
            `Payment status updated to ${newStatus}`,
            'Close',
            {
              duration: 3000,
              panelClass: ['success-snackbar'],
            }
          );
          this.loadPayments(); // Reload the data
        }
      });
  }
}
