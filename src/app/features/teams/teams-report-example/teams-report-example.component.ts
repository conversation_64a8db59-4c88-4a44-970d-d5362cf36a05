import { Component, OnInit } from '@angular/core';
import { TeamsService, TeamsReport, TeamsAnalysis } from 'src/app/service/teams.service';

@Component({
  selector: 'app-teams-report-example',
  templateUrl: './teams-report-example.component.html',
  styleUrls: ['./teams-report-example.component.css']
})
export class TeamsReportExampleComponent implements OnInit {
  // Contoh 1: Menggunakan answerId
  answerId: number = 1; // Ganti dengan ID yang valid

  // Contoh 2: Menggunakan data yang sudah disiapkan
  teamsReport?: TeamsReport;
  teamsAnalysis?: TeamsAnalysis;

  // Loading state
  loading = false;
  error = false;
  errorMessage = '';

  constructor(private teamsService: TeamsService) { }

  ngOnInit(): void {
    // Contoh menyiapkan data secara manual
    this.prepareManualData();
  }

  /**
   * Menyiapkan data laporan dan analisis secara manual
   */
  private prepareManualData(): void {
    // Contoh data kategori
    const categoryTotals = {
      T: 35,
      E: 25,
      A: 40,
      M: 20,
      S: 30
    };

    // Buat data chart
    const chartData = this.teamsService.getTeamsChartData(categoryTotals);
    
    // Dapatkan kategori tertinggi
    const highestCategories = this.teamsService.getHighestCategory(categoryTotals);

    // Buat laporan
    this.teamsReport = {
      categoryTotals,
      highestCategories,
      chartData
    };

    // Buat analisis
    this.teamsAnalysis = {
      mainCategory: 'A',
      mainCategoryName: 'Analyzer',
      mainCategoryValue: 40,
      secondCategory: 'T',
      secondCategoryName: 'Theorist',
      secondCategoryValue: 35,
      combinationName: 'Analyzer - Theorist',
      analysisTitle: 'Analyzer',
      analysisDetail: 'Anda memiliki kecenderungan sebagai seorang Analyzer. Analyzer adalah orang yang sangat detail dan analitis dalam pendekatan mereka. Mereka suka mengumpulkan data, menganalisis informasi, dan membuat keputusan berdasarkan fakta.',
      combinationDetail: 'Kombinasi Analyzer dan Theorist menunjukkan kemampuan yang kuat dalam pemikiran konseptual dan analitis. Anda cenderung memiliki pendekatan yang sistematis dan mendalam terhadap masalah, dengan kemampuan untuk mengembangkan teori dan mengujinya secara menyeluruh.'
    };
  }
}
