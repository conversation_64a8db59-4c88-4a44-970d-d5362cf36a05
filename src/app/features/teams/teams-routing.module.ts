import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TeamsTestPageComponent } from './teams-test-page/teams-test-page.component';
import { TeamsResultsComponent } from './teams-results/teams-results.component';
import { TeamsDetailReportComponent } from './teams-detail-report/teams-detail-report.component';
// import { TeamsReportExampleComponent } from './teams-report-example/teams-report-example.component';

const routes: Routes = [
  { path: '', component: TeamsTestPageComponent },
  { path: 'results', component: TeamsResultsComponent },
  { path: 'report/:id', component: TeamsDetailReportComponent },
  // { path: 'example', component: TeamsReportExampleComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TeamsRoutingModule {}
