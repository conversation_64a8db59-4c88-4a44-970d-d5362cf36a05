import { Component, OnInit, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ChartOptions, ChartType, ChartData } from 'chart.js';
import {
  TeamsService,
  TeamsReport,
  TeamsAnalysis,
} from 'src/app/service/teams.service';

@Component({
  selector: 'app-teams-detail-report',
  templateUrl: './teams-detail-report.component.html',
  styleUrls: ['./teams-detail-report.component.css'],
})
export class TeamsDetailReportComponent implements OnInit {
  // Input properties untuk menerima data dari komponen lain
  @Input() answerId?: number;
  @Input() teamsReport?: TeamsReport;
  @Input() teamsAnalysis?: TeamsAnalysis;

  // Properties untuk tampilan
  judulReport = 'Report Teams';
  keterangan = 'Keterangan';
  judulHasil = 'Judul Hasil';
  keteranganHasil = 'Keterangan Hasil';

  public teamsChartData: ChartData<'line'> | undefined;
  public teamsLabels: string[] = ['T', 'E', 'A', 'M', 'S'];
  public chartOptions: ChartOptions;
  public ChartType: ChartType = 'line';
  public chartLegend = false;

  // Loading state
  loading = false;
  error = false;
  errorMessage = '';

  constructor(
    private teamsService: TeamsService,
    private route: ActivatedRoute
  ) {
    // Gunakan opsi chart default dari service
    this.chartOptions = this.teamsService.getTeamsChartOptions();
  }

  ngOnInit(): void {
    // Jika teamsReport dan teamsAnalysis sudah disediakan melalui @Input, gunakan itu
    if (this.teamsReport && this.teamsAnalysis) {
      this.updateReportData(this.teamsReport, this.teamsAnalysis);
      return;
    }

    // Jika answerId disediakan melalui @Input, gunakan itu
    let idToUse = this.answerId;

    // Jika tidak ada answerId dari @Input, coba ambil dari route params
    if (!idToUse && this.route.snapshot.params['id']) {
      idToUse = this.route.snapshot.params['id'];
    }

    // Jika ada ID yang valid, ambil data laporan lengkap
    if (idToUse) {
      this.loading = true;
      this.error = false;

      this.teamsService.getCompleteTeamsReport(idToUse).subscribe({
        next: (data) => {
          this.updateReportData(data.report, data.analysis);
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading TEAMS report:', error);
          this.loading = false;
          this.error = true;
          this.errorMessage =
            'Terjadi kesalahan saat memuat laporan. Silakan coba lagi nanti.';
        },
      });
    }
  }

  /**
   * Memperbarui data laporan yang ditampilkan
   * @param report Data laporan TEAMS
   * @param analysis Data analisis TEAMS
   */
  private updateReportData(report: TeamsReport, analysis: TeamsAnalysis): void {
    // Update chart data
    this.teamsChartData = report.chartData;

    // Update analysis data
    this.judulHasil = analysis.analysisTitle;
    this.keteranganHasil = analysis.analysisDetail;

    // Tambahkan detail kombinasi jika ada
    if (analysis.combinationDetail) {
      const combinationName = analysis.combinationName || 'Kombinasi';
      this.keteranganHasil += `\n\n**Kombinasi: ${combinationName}**\n\n${analysis.combinationDetail}`;
    }
  }
}
