<div class="report-container">
  <!-- Loading indicator -->
  <div *ngIf="loading" class="loading-container">
    <div class="spinner"></div>
    <p class="loading-text">Memuat laporan...</p>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="error-container">
    <div class="error-icon">⚠️</div>
    <h3 class="error-title"><PERSON><PERSON><PERSON><PERSON></h3>
    <p class="error-message">{{ errorMessage }}</p>
    <button class="retry-button" (click)="ngOnInit()">Coba Lagi</button>
  </div>

  <!-- Report content -->
  <app-report-detail
    *ngIf="!loading && !error && teamsChartData"
    [judulReport]="judulReport"
    [keterangan]="keterangan"
    [chartData]="teamsChartData"
    [chartOptions]="chartOptions"
    [ChartType]="ChartType"
    [judulHasil]="judulHasil"
    [keteranganHasil]="keteranganHasil"
  ></app-report-detail>
</div>
