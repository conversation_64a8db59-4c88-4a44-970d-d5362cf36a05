/* Modern Teams Assessment Page Styles */
/* Inherits from DISC styles with Teams-specific customizations */

@import '../../../features/disc/disc-test-page/disc-test-page.component.css';

/* Teams-specific color overrides */
.assessment-title {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.question-number {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.progress-bar {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.answer-option.selected {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.answer-option.selected::after {
  color: #10b981;
}

.answer-option.selected .answer-text {
  color: #047857;
}

.nav-button-primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.nav-button-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.timer-value {
  color: #10b981;
}