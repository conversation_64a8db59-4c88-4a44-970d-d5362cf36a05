import { Component, OnInit } from '@angular/core';
import { Questions } from 'src/app/interfaces/questions';

import { AuthService } from 'src/app/service/auth.service';
import { TeamsService } from 'src/app/service/teams.service';
import { TestManagerService } from 'src/app/service/test-manager.service';

@Component({
  selector: 'app-teams-test-page',
  templateUrl: './teams-test-page.component.html',
  styleUrls: ['./teams-test-page.component.css'],
})
// TeamsTestPageComponent
export class TeamsTestPageComponent implements OnInit {
  pertanyaan: Questions[] = [];
  answers: { id: number; value: number; answer: string; category: string }[] =
    [];

  constructor(
    private teamsQuestion: TeamsService,
    private authService: AuthService,
    private testManager: TestManagerService
  ) {}

  ngOnInit(): void {
    this.teamsQuestion.getQuestion().subscribe((response: any) => {
      this.pertanyaan = response.data;
    });
  }

  handleSubmitAnswers(answers: any[]) {
    //
    // Anda bisa melakukan proses lebih lanjut di sini, misalnya mengirim jawaban ke server
    this.submitAnswers(answers);
  }

  submitAnswers(answers: any[]) {
    const userId = this.authService.getCurrentUser().id;
    const currentTest = this.testManager.getCurrentTest();

    // Gunakan testManagerId saat menyimpan jawaban
    this.teamsQuestion.saveAnswer(userId, answers, currentTest?.id).subscribe(
      (response: any) => {
        const resultId = response.data?.id;
        // Gunakan resultId saat menandai test selesai
        this.testManager.markCurrentTestComplete(resultId);
        this.testManager.moveToNextTest();
      },
      (error) => {
        console.error('Error saving TEAMS answers:', error);
      }
    );
  }
}
