import { NgModule } from '@angular/core';

import { TeamsDetailReportComponent } from './teams-detail-report/teams-detail-report.component';
import { TeamsResultsComponent } from './teams-results/teams-results.component';
import { TeamsTestPageComponent } from './teams-test-page/teams-test-page.component';
import { TeamsReportExampleComponent } from './teams-report-example/teams-report-example.component';
import { TeamsRoutingModule } from './teams-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';

@NgModule({
  declarations: [
    TeamsTestPageComponent,
    TeamsResultsComponent,
    TeamsDetailReportComponent,
    TeamsReportExampleComponent,
  ],
  imports: [SharedModule, TeamsRoutingModule],
})
export class TeamsModule {}
