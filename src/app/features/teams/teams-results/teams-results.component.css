/* Modern Teams Results Page Styles */
/* Inherits from DISC results styles with Teams-specific customizations */

@import '../../../features/disc/disc-results/disc-results.component.css';

/* Teams-specific color overrides */
.results-title,
.score-value {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.result-card::before {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.result-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.action-button-primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.action-button-primary:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

/* Team Role Badge */
.team-role-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-6);
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: var(--color-white);
  border-radius: var(--radius-full);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0 auto var(--spacing-4);
  box-shadow: var(--shadow-lg);
}