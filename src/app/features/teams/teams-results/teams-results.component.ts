import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';

import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/service/auth.service';
import { TeamsService } from 'src/app/service/teams.service';

@Component({
  selector: 'app-teams-results',
  templateUrl: './teams-results.component.html',
  styleUrls: ['./teams-results.component.css'],
})
export class TeamsResultsComponent implements OnInit {
  displayedColumns: string[] = ['createdAt', 'detail'];
  dataSource = new MatTableDataSource<any>();
  isAdmin = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private teamService: TeamsService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    // Cek apakah user adalah admin
    const user = this.authService.getCurrentUser();
    this.isAdmin = user.role === 'admin';

    // Jika admin, tambahkan kolom username
    if (this.isAdmin) {
      this.displayedColumns = ['username', 'createdAt', 'detail'];
    }

    this.loadTeamResults();
  }

  loadTeamResults() {
    const user = this.authService.getCurrentUser();

    // Jika admin, ambil semua hasil
    if (this.isAdmin) {
      this.teamService.getAllResults().subscribe((results: any) => {
        const teamsResults = results.data.map((item: any) => ({
          id: item.id,
          createdAt: item.attributes.createdAt,
          username: item.attributes.user?.data?.attributes?.username || 'N/A',
        }));
        this.dataSource.data = teamsResults;
        this.dataSource.paginator = this.paginator;
      });
    } else {
      // Jika bukan admin, hanya ambil hasil user tersebut
      this.teamService.getResult(user.id).subscribe((results: any) => {
        const teamsResults = results.data.map((item: any) => ({
          id: item.id,
          createdAt: item.attributes.createdAt,
        }));
        this.dataSource.data = teamsResults;
        this.dataSource.paginator = this.paginator;
      });
    }
  }

  viewDetail(resultId: number) {
    if (resultId) {
      this.router.navigate(['/home/<USER>/report/', resultId]);
    }
  }
}
