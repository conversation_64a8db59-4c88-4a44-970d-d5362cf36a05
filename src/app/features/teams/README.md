# TEAMS Report Component

Komponen ini digunakan untuk menampilkan laporan TEAMS (<PERSON><PERSON>, Executor, <PERSON><PERSON><PERSON>, Manager, Strategist) berdasarkan hasil tes TEAMS.

## Cara Penggunaan

Ada tiga cara untuk menggunakan komponen `TeamsDetailReportComponent`:

### 1. Menggunakan ID Jawaban

Jika Anda memiliki ID jawaban TEAMS, Anda dapat menggunakan komponen dengan cara berikut:

```html
<app-teams-detail-report [answerId]="answerId"></app-teams-detail-report>
```

Dimana `answerId` adalah ID jawaban TEAMS yang ingin ditampilkan laporannya.

### 2. Menggunakan Data Laporan yang Sudah Disiapkan

Jika Anda sudah memiliki data laporan dan analisis TEAMS, Anda dapat menggunakan komponen dengan cara berikut:

```html
<app-teams-detail-report 
  [teamsReport]="teamsReport" 
  [teamsAnalysis]="teamsAnalysis">
</app-teams-detail-report>
```

Dimana:
- `teamsReport` adalah objek yang berisi data laporan TEAMS dengan struktur `TeamsReport`
- `teamsAnalysis` adalah objek yang berisi data analisis TEAMS dengan struktur `TeamsAnalysis`

### 3. Menggunakan Route Parameter

Anda juga dapat mengakses komponen melalui route dengan parameter ID:

```
/home/<USER>/report/:id
```

## Service

Komponen ini menggunakan `TeamsService` untuk memproses data laporan TEAMS. Service ini menyediakan beberapa metode utama:

1. `calculateReport(answers)`: Menghitung total nilai untuk setiap kategori TEAMS
2. `getTeamsChartData(categoryTotals)`: Mendapatkan data chart berdasarkan hasil perhitungan
3. `getTeamsChartOptions()`: Mendapatkan opsi chart default untuk TEAMS
4. `getHighestCategory(categoryTotals)`: Mendapatkan kategori dengan nilai tertinggi
5. `getTeamsReportById(answerId)`: Mendapatkan laporan TEAMS lengkap berdasarkan ID jawaban
6. `getTeamsAnalysis(highestCategories)`: Mendapatkan analisis TEAMS lengkap berdasarkan kategori tertinggi
7. `getCompleteTeamsReport(answerId)`: Mendapatkan laporan dan analisis TEAMS lengkap berdasarkan ID jawaban

## Contoh Penggunaan

Anda dapat melihat contoh penggunaan komponen di `TeamsReportExampleComponent` yang dapat diakses melalui rute `/home/<USER>/example`.

## Struktur Data

### TeamsReport

```typescript
interface TeamsReport {
  categoryTotals: Record<Category, number>;
  highestCategories: CategoryResult[];
  chartData: ChartData<'line'>;
}
```

### TeamsAnalysis

```typescript
interface TeamsAnalysis {
  mainCategory: string;
  mainCategoryName: string;
  mainCategoryValue: number;
  secondCategory?: string;
  secondCategoryName?: string;
  secondCategoryValue?: number;
  combinationName?: string;
  analysisTitle: string;
  analysisDetail: string;
  combinationDetail?: string;
}
```

## Keuntungan

Dengan menggunakan komponen ini, Anda dapat:

1. Menampilkan laporan TEAMS dengan mudah di berbagai bagian aplikasi
2. Menghindari duplikasi kode untuk menampilkan laporan TEAMS
3. Memisahkan logika bisnis dari tampilan
4. Menggunakan kembali komponen dengan data yang berbeda
