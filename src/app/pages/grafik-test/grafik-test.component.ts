import { Component } from '@angular/core';
import { ChartOptions, ChartData, ChartType } from 'chart.js';

@Component({
  selector: 'app-grafik-test',
  templateUrl: './grafik-test.component.html',
  styleUrls: ['./grafik-test.component.css'],
})
export class GrafikTestComponent {
  // Tipe data dan konfigurasi grafik garis
  public lineChartData: ChartData<'line'> = {
    labels: [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
      '13',
      '14',
      '15',
      '16',
      '17',
      '18',
      '19',
      '20',
    ],
    datasets: [
      {
        label: 'Variabel D',
        data: [1, 2, 7, 9, 10, 11, 13, 15, 17, 20, 21, 23, 24, 25, 26, 28],
        borderColor: 'blue',
        backgroundColor: 'rgba(0, 0, 255, 0.1)',
        fill: false,
        tension: 0.4,
      },
      {
        label: 'Variabel I',
        data: [2, 5, 8, 11, 14, 17, 19, 23, 24, 25, 26, 28],
        borderColor: 'red',
        backgroundColor: 'rgba(255, 0, 0, 0.1)',
        fill: false,
        tension: 0.4,
      },
      {
        label: 'Variabel S',
        data: [4, 7, 8, 12, 13, 15, 16, 19, 20, 21, 24, 25, 27, 28],
        borderColor: 'green',
        backgroundColor: 'rgba(0, 255, 0, 0.1)',
        fill: false,
        tension: 0.4,
      },
      {
        label: 'Variabel C',
        data: [7, 8, 12, 15, 19, 21, 23, 25, 27, 28],
        borderColor: 'purple',
        backgroundColor: 'rgba(128, 0, 128, 0.1)',
        fill: false,
        tension: 0.4,
      },
    ],
  };

  // Opsi konfigurasi grafik
  public lineChartOptions: ChartOptions<'line'> = {
    responsive: true,
    scales: {
      y: {
        min: 1,
        max: 28,
        ticks: {
          stepSize: 1,
          color: '#333',
        },
      },
      x: {
        ticks: {
          color: '#333',
        },
      },
    },
    plugins: {
      legend: { display: true, position: 'top' },
    },
  };

  public lineChartType: ChartType = 'line' as const;
}
