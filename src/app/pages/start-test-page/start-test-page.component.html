<div class="max-w-4xl mx-auto px-4 py-8">
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-4">
      Selamat Datang di Assessment Center
    </h1>
    <p class="text-gray-600">
      Silakan selesaikan test yang tersedia untuk Anda:
    </p>
  </div>

  <!-- Overall Progress -->
  <div class="mb-8">
    <div class="flex justify-between mb-2">
      <span class="text-sm text-gray-600">Total Progress</span>
      <span class="text-sm text-gray-600"
        >{{ getCompletionPercentage() | number : "1.0-0" }}%</span
      >
    </div>
    <div class="h-2 bg-gray-200 rounded-full">
      <div
        class="h-2 bg-green-500 rounded-full transition-all duration-300"
        [style.width]="getCompletionPercentage() + '%'"
      ></div>
    </div>
  </div>

  <!-- Success Message -->
  <div
    *ngIf="showSuccessMessage"
    class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-8"
    role="alert"
  >
    <strong class="font-bold">Selamat!</strong>
    <span class="block sm:inline"> Anda telah menyelesaikan semua test.</span>
    <span class="block sm:inline">
      Anda akan diarahkan ke halaman utama dalam {{ countdown }} detik...
    </span>
  </div>

  <!-- Test List with Checkboxes -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <h2 class="text-xl font-semibold mb-4">Daftar Test:</h2>
    <div class="space-y-4">
      <div
        *ngFor="let test of availableTests"
        class="flex items-center p-4 border rounded-lg"
        [class.bg-green-50]="test.isCompleted"
        [class.border-green-200]="test.isCompleted"
        [class.border-gray-200]="!test.isCompleted"
      >
        <div class="flex-1">
          <div class="flex items-center">
            <div class="w-6 h-6 mr-4">
              <div
                *ngIf="test.isCompleted"
                class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"
              >
                <svg
                  class="w-4 h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 13l4 4L19 7"
                  ></path>
                </svg>
              </div>
              <div
                *ngIf="!test.isCompleted"
                class="w-6 h-6 border-2 border-gray-300 rounded-full"
              ></div>
            </div>
            <div>
              <span
                class="font-medium"
                [class.text-green-700]="test.isCompleted"
              >
                {{ test.type }}
              </span>
              <div class="text-sm text-gray-500">Urutan: {{ test.order }}</div>
            </div>
          </div>
        </div>
        <span
          class="text-sm"
          [class.text-green-600]="test.isCompleted"
          [class.text-gray-500]="!test.isCompleted"
        >
          {{ test.isCompleted ? "Selesai" : "Belum dikerjakan" }}
        </span>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="text-center py-4">
        <div
          class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"
        ></div>
      </div>

      <!-- Error State -->
      <div *ngIf="error" class="text-red-500 text-center py-4">
        {{ error }}
      </div>

      <!-- No Tests Available -->
      <div
        *ngIf="!isLoading && !error && availableTests.length === 0"
        class="text-center py-4 text-gray-500"
      >
        Tidak ada test yang tersedia untuk Anda saat ini.
      </div>
    </div>
  </div>

  <!-- Action Button -->
  <div class="text-center">
    <button
      *ngIf="!isAllTestsCompleted() && availableTests.length > 0"
      (click)="startTests()"
      [disabled]="isLoading"
      class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
    >
      {{ isLoading ? "Memuat..." : "Lanjutkan Test" }}
    </button>

    <div *ngIf="isAllTestsCompleted()" class="text-green-600 font-medium">
      Selamat! Anda telah menyelesaikan semua test.
    </div>
  </div>
</div>
