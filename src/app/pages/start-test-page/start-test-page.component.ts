import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TestManagerService } from 'src/app/service/test-manager.service';
import { AuthService } from 'src/app/service/auth.service';
import { TestAccess } from 'src/app/interfaces/test-access';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-start-test-page',
  templateUrl: './start-test-page.component.html',
  standalone: true,
  imports: [CommonModule],
})
export class StartTestPageComponent implements OnInit, OnDestroy {
  availableTests: TestAccess[] = [];
  isLoading = true;
  error: string | null = null;
  showSuccessMessage = false;
  countdown = 5;
  private statusSubscription: Subscription;
  private successTimer: any;
  private countdownTimer: any;
  private allTestsCompleted = false;

  constructor(
    private testManager: TestManagerService,
    private authService: AuthService,
    private router: Router
  ) {
    // Subscribe ke perubahan status
    this.statusSubscription = this.testManager
      .getTestStatusChanges()
      .subscribe(() => {
        if (!this.allTestsCompleted) {
          this.loadTests();
        }
      });
  }

  ngOnInit() {
    this.loadTests();
  }

  ngOnDestroy() {
    // Unsubscribe untuk mencegah memory leak
    if (this.statusSubscription) {
      this.statusSubscription.unsubscribe();
    }
    if (this.successTimer) {
      clearTimeout(this.successTimer);
    }
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  }

  startCountdown() {
    this.countdown = 5;
    this.countdownTimer = setInterval(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        clearInterval(this.countdownTimer);
        this.router.navigate(['/home']);
      }
    }, 1000);
  }

  loadTests() {
    const userId = this.authService.getCurrentUser().id;
    this.testManager.getUserTestAccess(userId).subscribe({
      next: (tests) => {
        // Filter hanya test pack yang belum completed dan masih memiliki test yang belum selesai
        this.availableTests = tests.filter((test) => !test.isCompleted);
        this.testManager.initializeTests(this.availableTests);
        this.isLoading = false;

        // Cek apakah tidak ada test pack yang open (semua sudah selesai atau tidak ada)
        if (this.availableTests.length === 0) {
          // Tidak ada test pack yang aktif, redirect ke results
          this.router.navigate(['/home/<USER>']);
          return;
        }

        // Cek apakah semua test sudah selesai
        if (this.isAllTestsCompleted() && !this.allTestsCompleted) {
          this.allTestsCompleted = true;
          // Update status test pack terlebih dahulu
          const testPackId = this.availableTests[0]?.test_packs;
          if (testPackId) {
            this.testManager.updateTestPackStatus(testPackId).subscribe({
              next: () => {
                // Setelah status test pack diupdate, tampilkan pesan sukses dan mulai countdown
                this.showSuccessMessage = true;
                this.startCountdown();
              },
              error: (error) => {
                console.error('Error updating test pack status:', error);
                this.error = 'Gagal mengupdate status test pack';
              },
            });
          }
        }
      },
      error: (err) => {
        console.error('Error loading tests:', err);
        this.error = 'Gagal memuat data test';
        this.isLoading = false;
      },
    });
  }

  getNextIncompleteTest(): TestAccess | null {
    // Cari test yang belum selesai dengan urutan terendah
    return (
      this.availableTests
        .filter((test) => !test.isCompleted)
        .sort((a, b) => a.order - b.order)[0] || null
    );
  }

  startTests() {
    const nextTest = this.getNextIncompleteTest();
    if (nextTest) {
      this.router.navigate([nextTest.route]);
    }
  }

  // Menghitung persentase penyelesaian
  getCompletionPercentage(): number {
    const completed = this.availableTests.filter(
      (test) => test.isCompleted
    ).length;
    return (completed / this.availableTests.length) * 100;
  }

  // Mengecek apakah semua test sudah selesai
  isAllTestsCompleted(): boolean {
    return this.availableTests.every((test) => test.isCompleted);
  }
}
