/* Modern Dynamic Page Design */

/* Main Container */
.main-container {
  background: var(--color-white);
  min-height: 100vh;
  position: relative;
}

/* Content Container */
.dynamic-content {
  position: relative;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0;
}

/* Consistent Spacing Between Sections */
.dynamic-content > * {
  margin-bottom: 0;
}

.dynamic-content > *:last-child {
  margin-bottom: 0;
}

/* Global Component Reset - Minimal Override */
::ng-deep app-header,
::ng-deep app-title,
::ng-deep app-about-section,
::ng-deep app-feature-grid,
::ng-deep app-benefits-list,
::ng-deep app-statistics,
::ng-deep app-cta-section {
  margin: 0;
  padding: 2rem 0;
}

/* Typography - Clean & Readable */
::ng-deep h1,
::ng-deep h2,
::ng-deep .section-title,
::ng-deep .professional-heading {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.2;
  margin-bottom: 1rem;
  text-align: center;
}

::ng-deep h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

::ng-deep p {
  font-size: 1.125rem;
  line-height: 1.6;
  color: #64748b;
  margin-bottom: 1rem;
}

/* Grid Layouts - Simple & Responsive */
::ng-deep .grid,
::ng-deep .tests-grid,
::ng-deep .features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

/* Cards - Clean Design */
::ng-deep .card,
::ng-deep .test-card,
::ng-deep .feature-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 2rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

::ng-deep .card:hover,
::ng-deep .test-card:hover,
::ng-deep .feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #6366f1;
}

/* Icons - Simple Design */
::ng-deep .icon,
::ng-deep .test-icon,
::ng-deep .feature-icon {
  width: 3rem;
  height: 3rem;
  background: #6366f1;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: white;
}

/* Buttons - Clean Style */
::ng-deep .btn,
::ng-deep .test-button,
::ng-deep button {
  background: #6366f1;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  display: inline-block;
  text-decoration: none;
}

::ng-deep .btn:hover,
::ng-deep .test-button:hover,
::ng-deep button:hover {
  background: #4f46e5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Modern Loading State */
.loading-container {
  background: var(--color-white);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: var(--spacing-8);
}

.loading-spinner {
  width: var(--spacing-16);
  height: var(--spacing-16);
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--color-gray-600);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  text-align: center;
  max-width: 400px;
  line-height: var(--line-height-relaxed);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fade In Animation - Subtle */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dynamic-content {
    padding: 1rem;
  }
  
  .dynamic-content > * {
    margin-bottom: 3rem;
  }
  
  ::ng-deep h1,
  ::ng-deep h2,
  ::ng-deep .section-title,
  ::ng-deep .professional-heading {
    font-size: 2rem;
  }
  
  ::ng-deep .grid,
  ::ng-deep .tests-grid,
  ::ng-deep .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  ::ng-deep .card,
  ::ng-deep .test-card,
  ::ng-deep .feature-card {
    padding: 1.5rem;
  }
}
