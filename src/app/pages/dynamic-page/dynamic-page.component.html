<!-- Dynamic Navbar -->
<app-dynamic-navbar></app-dynamic-navbar>

<!-- Loading State -->
<div
  *ngIf="isLoading"
  class="min-h-screen loading-container flex items-center justify-center"
>
  <div class="text-center">
    <div class="loading-spinner mx-auto"></div>
    <p class="mt-6 text-gray-600 text-lg font-medium">Memuat halaman...</p>
  </div>
</div>

<!-- Main Content -->
<div *ngIf="!isLoading" id="main-content" class="min-h-screen main-container">
  <!-- Dynamic Content Section -->
  <div *ngIf="pageData && pageData.length > 0" class="dynamic-content fade-in">
    <div *ngFor="let component of pageData">
      <!-- Render Header Component -->
      <app-header
        *ngIf="component.__component === 'component.header'"
        [title]="component.title"
        [description]="component.description"
        [imgUrl]="
          'http://localhost:1337' +
          component.img?.data?.attributes?.formats?.large?.url
        "
      >
      </app-header>

      <!-- Render Title Component -->
      <app-title
        *ngIf="component.__component === 'component.title'"
        [title]="component.title"
      >
      </app-title>

      <!-- Render TwoButton Component -->
      <app-two-button
        *ngIf="component.__component === 'component.2-button'"
        [button1Title]="component.firstButton.title"
        [button1Url]="component.firstButton.url"
        [button2Title]="component.secondButton.title"
        [button2Url]="component.secondButton.url"
      >
      </app-two-button>

      <!-- Render Text Block Component -->
      <app-text-block
        *ngIf="component.__component === 'component.text-block'"
        [title]="component.title"
        [content]="component.content"
        [alignment]="component.alignment"
      >
      </app-text-block>

      <!-- Render Feature Grid Component -->
      <app-feature-grid
        *ngIf="component.__component === 'component.feature-grid'"
        [title]="component.title"
        [subtitle]="component.subtitle"
        [features]="component.features"
      >
      </app-feature-grid>

      <!-- Render Benefits List Component -->
      <app-benefits-list
        *ngIf="component.__component === 'component.benefits-list'"
        [title]="component.title"
        [subtitle]="component.subtitle"
        [benefits]="component.benefits"
        [highlightColor]="component.highlightColor"
      >
      </app-benefits-list>

      <!-- Render Statistics Component -->
      <app-statistics
        *ngIf="component.__component === 'component.statistics'"
        [title]="component.title"
        [subtitle]="component.subtitle"
        [stats]="component.stats"
      >
      </app-statistics>

      <!-- Render CTA Section Component -->
      <app-cta-section
        *ngIf="component.__component === 'component.cta-section'"
        [title]="component.title"
        [description]="component.description"
        [buttonText]="component.buttonText"
        [buttonUrl]="component.buttonUrl"
      >
      </app-cta-section>

      <!-- Test Selection Section removed - available via navbar menu "Paket Test" -->

      <!-- Render About Section Component -->
      <app-about-section
        *ngIf="component.__component === 'component.about-section'"
        [title]="component.title"
        [description]="component.description"
        [features]="component.features"
        [trustIndicators]="component.trustIndicators"
      >
      </app-about-section>
    </div>
  </div>
</div>

<!-- Footer -->
<app-dynamic-footer></app-dynamic-footer>
