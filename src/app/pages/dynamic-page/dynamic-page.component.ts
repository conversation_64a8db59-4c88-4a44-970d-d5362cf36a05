import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ParserService } from 'src/app/service/parser.service';
import { DynamicNavbarComponent } from 'src/app/components/dynamic-navbar/dynamic-navbar.component';
import { DynamicFooterComponent } from 'src/app/components/dynamic-footer/dynamic-footer.component';
import { HeaderComponent } from 'src/app/components/header/header.component';
import { TitleComponent } from 'src/app/components/title/title.component';
import { TwoButtonComponent } from 'src/app/components/two-button/two-button.component';
import { TextBlockComponent } from 'src/app/components/text-block/text-block.component';
import { FeatureGridComponent } from 'src/app/components/feature-grid/feature-grid.component';
import { BenefitsListComponent } from 'src/app/components/benefits-list/benefits-list.component';
import { StatisticsComponent } from 'src/app/components/statistics/statistics.component';
import { CtaSectionComponent } from 'src/app/components/cta-section/cta-section.component';
import { AboutSectionComponent } from 'src/app/components/about-section/about-section.component';

@Component({
  selector: 'app-dynamic-page',
  templateUrl: './dynamic-page.component.html',
  styleUrls: ['./dynamic-page.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    DynamicNavbarComponent,
    DynamicFooterComponent,
    HeaderComponent,
    TitleComponent,
    TwoButtonComponent,
    TextBlockComponent,
    FeatureGridComponent,
    BenefitsListComponent,
    StatisticsComponent,
    CtaSectionComponent,
    AboutSectionComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DynamicPageComponent implements OnInit {
  pageData: any;
  isLoading = true;
  pageSlug = 'home-page'; // default page

  constructor(
    private data: ParserService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    // Check if there's a page parameter in the route
    this.route.queryParams.subscribe((params) => {
      this.pageSlug = params['page'] || 'home-page';
      this.loadPageData();
    });
  }

  loadPageData() {
    this.isLoading = true;
    this.data.getPageData(this.pageSlug).subscribe((data: any) => {
      this.pageData = data.data.attributes.content;
      this.isLoading = false;

      // Scroll to top of the page content after data is loaded
      this.scrollToTop();
    });
  }

  private scrollToTop() {
    // Scroll to main content area with smooth animation
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    } else {
      // Fallback: scroll to top of the page
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth',
      });
    }
  }

  login() {
    this.router.navigate(['/login']); // Arahkan ke halaman login
  }
}
