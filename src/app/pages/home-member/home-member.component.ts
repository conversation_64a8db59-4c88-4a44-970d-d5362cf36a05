import { Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';

@Component({
  selector: 'app-home-member',
  templateUrl: './home-member.component.html',
  styleUrls: ['./home-member.component.css'],
})
export class HomeMemberComponent implements OnInit {
  constructor(private router: Router) {}

  ngOnInit(): void {}

  // Navigasi ke halaman kuesioner
  goToQuestionnaire() {
    this.router.navigate(['/disc']);
  }

  // Navigasi ke halaman hasil
  goToResults() {
    this.router.navigate(['/home/<USER>']);
  }
}
