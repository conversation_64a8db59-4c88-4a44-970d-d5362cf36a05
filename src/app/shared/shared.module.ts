import { LogoutComponent } from './../auth/logout/logout.component';
import { NgChartsModule } from 'ng2-charts';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Material Modules
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';

// Import shared components

import { QuestionStepperComponent } from '../components/question-stepper/question-stepper.component';
import { ReportDetailComponent } from '../components/report-detail/report-detail.component';
import { QuestionaireWithSliderComponent } from '../components/questionaire-with-slider/questionaire-with-slider.component';
import { MatPaginatorModule } from '@angular/material/paginator';
import { QuestionaireWithRadioComponent } from '../components/questionaire-with-radio/questionaire-with-radio.component';
import { DiscChartComponent } from '../components/disc-chart/disc-chart.component';

@NgModule({
  declarations: [
    ReportDetailComponent,
    QuestionStepperComponent,
    QuestionaireWithSliderComponent,
    QuestionaireWithRadioComponent,
    ReportDetailComponent,
    DiscChartComponent,
    LogoutComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatTableModule,
    MatPaginatorModule,
    NgChartsModule,
  ],
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatTableModule,
    MatPaginatorModule,

    // Export shared components

    ReportDetailComponent,
    QuestionStepperComponent,
    QuestionaireWithSliderComponent,
    QuestionaireWithRadioComponent,
    ReportDetailComponent,
    DiscChartComponent,
    LogoutComponent,
  ],
})
export class SharedModule {}
