export interface TripayWebhookPayload {
  reference: string;
  merchant_ref: string;
  payment_method: string;
  payment_method_code: string;
  total_amount: number;
  fee_merchant: number;
  fee_customer: number;
  total_fee: number;
  amount_received: number;
  is_closed_payment: boolean;
  status: string;
  paid_at: number;
  note?: string;
}

export interface TripayWebhookRequest {
  event: string;
  data: TripayWebhookPayload;
}

export interface TripayCallbackSignature {
  json: string;
  signature: string;
}
