{"kind": "collectionType", "collectionName": "question_groups", "info": {"singularName": "question-group", "pluralName": "question-groups", "displayName": "QuestionGroup", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"questions": {"type": "relation", "relation": "manyToMany", "target": "api::question.question", "inversedBy": "question_groups"}, "selectedMost": {"type": "string"}, "selectedLeast": {"type": "string"}, "warning": {"type": "string"}}}