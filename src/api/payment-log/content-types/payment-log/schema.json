{"kind": "collectionType", "collectionName": "payment_logs", "info": {"singularName": "payment-log", "pluralName": "payment-logs", "displayName": "Payment Log"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"action": {"type": "string", "required": true}, "previousStatus": {"type": "string"}, "newStatus": {"type": "string"}, "metadata": {"type": "json"}, "ipAddress": {"type": "string"}, "userAgent": {"type": "string"}}}