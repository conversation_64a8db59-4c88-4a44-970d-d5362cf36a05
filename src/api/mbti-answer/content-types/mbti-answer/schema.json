{"kind": "collectionType", "collectionName": "mbti_answers", "info": {"singularName": "mbti-answer", "pluralName": "mbti-answers", "displayName": "MBTI Answer", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "mbti_answers"}, "answer": {"type": "json"}, "testManagerId": {"type": "integer"}}}