{"kind": "collectionType", "collectionName": "team_questions", "info": {"singularName": "team-question", "pluralName": "team-questions", "displayName": "Team Question", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"question": {"type": "string"}, "category": {"type": "enumeration", "enum": ["T", "E", "A", "M", "S"]}, "answer_type": {"type": "relation", "relation": "manyToOne", "target": "api::answer-type.answer-type", "inversedBy": "team_questions"}}}