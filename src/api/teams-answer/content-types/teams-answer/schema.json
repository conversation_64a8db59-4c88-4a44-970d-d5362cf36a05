{"kind": "collectionType", "collectionName": "teams_answers", "info": {"singularName": "teams-answer", "pluralName": "teams-answers", "displayName": "Teams Answer", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"answer": {"type": "json"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "teams_answers"}, "testManagerId": {"type": "integer"}}}