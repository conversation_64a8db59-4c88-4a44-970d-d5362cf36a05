{"kind": "collectionType", "collectionName": "test_managers", "info": {"singularName": "test-manager", "pluralName": "test-managers", "displayName": "Test Manager", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"type": {"type": "enumeration", "enum": ["DISC", "MBTI", "VALUES", "TEAMS"]}, "isCompleted": {"type": "boolean", "default": false}, "order": {"type": "integer"}, "route": {"type": "string"}, "test_packs": {"type": "relation", "relation": "manyToMany", "target": "api::test-pack.test-pack", "mappedBy": "tests"}, "resultId": {"type": "integer"}}}