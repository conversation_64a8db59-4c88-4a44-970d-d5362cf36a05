{"kind": "collectionType", "collectionName": "answer_types", "info": {"singularName": "answer-type", "pluralName": "answer-types", "displayName": "Answer Type"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "AnswerOption": {"type": "json"}, "team_questions": {"type": "relation", "relation": "oneToMany", "target": "api::team-question.team-question", "mappedBy": "answer_type"}, "values_questions": {"type": "relation", "relation": "oneToMany", "target": "api::values-question.values-question", "mappedBy": "answer_type"}}}