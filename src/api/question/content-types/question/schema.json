{"kind": "collectionType", "collectionName": "questions", "info": {"singularName": "question", "pluralName": "questions", "displayName": "Question"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"text": {"type": "string"}, "leastValue": {"type": "enumeration", "enum": ["V", "D", "I", "S", "C"]}, "mostValue": {"type": "enumeration", "enum": ["V", "D", "I", "S", "C"]}, "question_groups": {"type": "relation", "relation": "manyToMany", "target": "api::question-group.question-group", "mappedBy": "questions"}}}