{"kind": "collectionType", "collectionName": "disc_answers", "info": {"singularName": "disc-answer", "pluralName": "disc-answers", "displayName": "DISC Answer", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"answers": {"type": "json"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "disc_answers"}, "testManagerId": {"type": "integer"}}}