{"kind": "collectionType", "collectionName": "values_answers", "info": {"singularName": "values-answer", "pluralName": "values-answers", "displayName": "Values Answer", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"answer": {"type": "json"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "values_answers"}, "testManagerId": {"type": "integer"}}}