{"kind": "collectionType", "collectionName": "values_questions", "info": {"singularName": "values-question", "pluralName": "values-questions", "displayName": "Values Question", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"question": {"type": "string"}, "category": {"type": "enumeration", "enum": ["L", "E", "P", "J"]}, "answer_type": {"type": "relation", "relation": "manyToOne", "target": "api::answer-type.answer-type", "inversedBy": "values_questions"}}}