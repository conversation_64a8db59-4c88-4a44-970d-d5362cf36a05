{"kind": "collectionType", "collectionName": "test_packs", "info": {"singularName": "test-pack", "pluralName": "test-packs", "displayName": "Test Pack", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"users": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "inversedBy": "test_packs"}, "tests": {"type": "relation", "relation": "manyToMany", "target": "api::test-manager.test-manager", "inversedBy": "test_packs"}, "status": {"type": "string", "default": "open"}, "payment": {"type": "relation", "relation": "manyToMany", "target": "api::payment.payment", "inversedBy": "testPack"}, "paymentStatus": {"type": "enumeration", "enum": ["UNPAID", "PAID", "EXPIRED"], "default": "UNPAID", "required": true}, "activatedAt": {"type": "datetime"}, "expiryDate": {"type": "datetime"}}}