{"kind": "collectionType", "collectionName": "payments", "info": {"singularName": "payment", "pluralName": "payments", "displayName": "Payment"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"externalId": {"type": "string", "required": true, "unique": true}, "xenditId": {"type": "string"}, "amount": {"type": "decimal", "required": true}, "status": {"type": "enumeration", "enum": ["PENDING", "PAID", "FAILED", "EXPIRED"], "default": "PENDING", "required": true}, "paymentMethod": {"type": "string"}, "paymentUrl": {"type": "string"}, "description": {"type": "string"}, "paidAt": {"type": "datetime"}, "users": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "inversedBy": "payments"}, "testPack": {"type": "relation", "relation": "manyToMany", "target": "api::test-pack.test-pack", "mappedBy": "payment"}}}