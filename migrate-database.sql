-- Database Migration Script for Maxima API
-- Fixing payment schema and relationships

USE `maxima-api`;

-- 1. First, let's check current table structure
SELECT 'Current payments table structure:' as info;
DESCRIBE payments;

-- 2. Remove duplicate paidAt column (keep paid_at)
SET @paidAt_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'maxima-api'
    AND TABLE_NAME = 'payments'
    AND COLUMN_NAME = 'paidAt'
);

SET @sql = IF(@paidAt_exists > 0,
    'ALTER TABLE payments DROP COLUMN paidAt;',
    'SELECT "Column paidAt does not exist, skipping drop" as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. Migrate data from many-to-many tables to foreign key columns
SELECT 'Migrating user relationships...' as info;

-- Update user_id from payments_user_links
UPDATE payments p
JOIN payments_user_links pul ON p.id = pul.payment_id
SET p.user_id = pul.user_id;

SELECT 'Migrating test pack relationships...' as info;

-- Update test_pack_id from payments_test_pack_links
UPDATE payments p
JOIN payments_test_pack_links ptpl ON p.id = ptpl.payment_id
SET p.test_pack_id = ptpl.test_pack_id;

-- 4. Show migration results
SELECT 'Migration results:' as info;
SELECT
    COUNT(*) as total_payments,
    COUNT(user_id) as payments_with_user,
    COUNT(test_pack_id) as payments_with_test_pack
FROM payments;

-- 5. Show sample migrated data
SELECT 'Sample migrated data:' as info;
SELECT id, external_id, amount, status, paid_at, user_id, test_pack_id, created_at
FROM payments
WHERE user_id IS NOT NULL OR test_pack_id IS NOT NULL
LIMIT 5;

-- 6. Drop old many-to-many relationship tables
SELECT 'Dropping old relationship tables...' as info;
DROP TABLE IF EXISTS payments_user_links;
DROP TABLE IF EXISTS payments_test_pack_links;

-- 7. Show final table structure
SELECT 'Final payments table structure:' as info;
DESCRIBE payments;

SELECT 'Migration completed successfully!' as result;
