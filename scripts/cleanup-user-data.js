/**
 * <PERSON>ript to cleanup user data via Strapi API
 * Run with: node scripts/cleanup-user-data.js
 */

const fetch = require('node-fetch');

async function cleanupUserData() {
  const userEmail = '<EMAIL>';
  const strapiUrl = 'http://localhost:1337/api';
  
  try {
    console.log(`🔍 Starting cleanup for user: ${userEmail}`);
    
    // 1. Find user by email
    const usersResponse = await fetch(`${strapiUrl}/users?filters[email][$eq]=${userEmail}`);
    const usersData = await usersResponse.json();
    
    if (!usersData || usersData.length === 0) {
      console.log(`❌ User with email ${userEmail} not found`);
      return;
    }
    
    const userId = usersData[0].id;
    console.log(`✅ Found user: ${usersData[0].username} (ID: ${userId})`);
    
    // 2. Find and delete test packs for this user
    const testPacksResponse = await fetch(
      `${strapiUrl}/test-packs?populate=*&filters[users][id][$eq]=${userId}`
    );
    const testPacksData = await testPacksResponse.json();
    const testPacks = testPacksData.data || [];
    
    console.log(`📦 Found ${testPacks.length} test pack(s) for user`);
    
    // Delete test packs and related test managers
    for (const testPack of testPacks) {
      console.log(`🗑️ Deleting test pack: ${testPack.id}`);
      
      // Delete test managers first if any
      if (testPack.attributes.tests?.data) {
        for (const test of testPack.attributes.tests.data) {
          console.log(`  - Deleting test manager: ${test.id}`);
          await fetch(`${strapiUrl}/test-managers/${test.id}`, {
            method: 'DELETE'
          });
        }
      }
      
      // Delete the test pack
      await fetch(`${strapiUrl}/test-packs/${testPack.id}`, {
        method: 'DELETE'
      });
    }
    
    // 3. Find and delete payments for this user
    const paymentsResponse = await fetch(
      `${strapiUrl}/payments?filters[users][id][$eq]=${userId}`
    );
    const paymentsData = await paymentsResponse.json();
    const payments = paymentsData.data || [];
    
    console.log(`💳 Found ${payments.length} payment(s) for user`);
    
    for (const payment of payments) {
      console.log(`🗑️ Deleting payment: ${payment.id} (${payment.attributes.externalId})`);
      await fetch(`${strapiUrl}/payments/${payment.id}`, {
        method: 'DELETE'
      });
    }
    
    // 4. Delete test results
    const resultTypes = [
      'disc-results',
      'mbti-results', 
      'values-results',
      'teams-results'
    ];
    
    let totalResults = 0;
    for (const resultType of resultTypes) {
      try {
        const response = await fetch(
          `${strapiUrl}/${resultType}?filters[user][id][$eq]=${userId}`
        );
        const data = await response.json();
        const results = data.data || [];
        
        if (results.length > 0) {
          console.log(`📊 Found ${results.length} ${resultType} for user`);
          
          for (const result of results) {
            console.log(`🗑️ Deleting ${resultType}: ${result.id}`);
            await fetch(`${strapiUrl}/${resultType}/${result.id}`, {
              method: 'DELETE'
            });
          }
          totalResults += results.length;
        }
      } catch (err) {
        console.log(`⚠️ Could not process ${resultType}: ${err.message}`);
      }
    }
    
    console.log(`🧹 User data cleanup completed for: ${userEmail}`);
    console.log('📋 Summary:');
    console.log(`   - Test Packs deleted: ${testPacks.length}`);
    console.log(`   - Payments deleted: ${payments.length}`);
    console.log(`   - Test Results deleted: ${totalResults}`);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
}

// Check if node-fetch is available, if not provide instructions
try {
  require('node-fetch');
  cleanupUserData();
} catch (err) {
  console.log('❌ node-fetch is not installed.');
  console.log('💡 Please install it with: npm install node-fetch');
  console.log('💡 Or run this manually through Strapi admin panel');
}

const mysql = require('mysql2/promise');

async function cleanupUserData() {
  const userEmail = '<EMAIL>';
  
  // Database connection configuration
  const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'maxima_db'
  };
  
  let connection;
  
  try {
    console.log(`🔍 Starting cleanup for user: ${userEmail}`);
    
    // Create database connection
    connection = await mysql.createConnection(dbConfig);
    
    // 1. Find user by email
    const [users] = await connection.execute(
      'SELECT id, username FROM up_users WHERE email = ?',
      [userEmail]
    );
    
    if (users.length === 0) {
      console.log(`❌ User with email ${userEmail} not found`);
      return;
    }
    
    const userId = users[0].id;
    console.log(`✅ Found user: ${users[0].username} (ID: ${userId})`);
    
    // 2. Find test packs for this user
    const [testPacks] = await connection.execute(
      `SELECT tp.id, tp.status, tp.paymentStatus 
       FROM test_packs tp
       JOIN test_packs_users_links tpul ON tp.id = tpul.test_pack_id
       WHERE tpul.user_id = ?`,
      [userId]
    );
    
    console.log(`📦 Found ${testPacks.length} test pack(s) for user`);
    
    // 3. Delete test managers for these test packs
    for (const testPack of testPacks) {
      console.log(`🗑️ Processing test pack: ${testPack.id}`);
      
      // Find test managers
      const [testManagers] = await connection.execute(
        `SELECT tm.id FROM test_managers tm
         JOIN test_managers_test_packs_links tmtpl ON tm.id = tmtpl.test_manager_id
         WHERE tmtpl.test_pack_id = ?`,
        [testPack.id]
      );
      
      // Delete test manager links first
      await connection.execute(
        'DELETE FROM test_managers_test_packs_links WHERE test_pack_id = ?',
        [testPack.id]
      );
      
      // Delete test managers
      for (const tm of testManagers) {
        console.log(`  - Deleting test manager: ${tm.id}`);
        await connection.execute('DELETE FROM test_managers WHERE id = ?', [tm.id]);
      }
    }
    
    // 4. Delete test pack user links
    await connection.execute(
      'DELETE FROM test_packs_users_links WHERE user_id = ?',
      [userId]
    );
    
    // 5. Delete test packs themselves
    for (const testPack of testPacks) {
      await connection.execute('DELETE FROM test_packs WHERE id = ?', [testPack.id]);
    }
    
    // 6. Find and delete payments
    const [payments] = await connection.execute(
      'SELECT id, externalId FROM payments WHERE user_id = ?',
      [userId]
    );
    
    console.log(`� Found ${payments.length} payment(s) for user`);
    
    for (const payment of payments) {
      console.log(`🗑️ Deleting payment: ${payment.id} (${payment.externalId})`);
      
      // Delete payment test pack links first
      await connection.execute(
        'DELETE FROM payments_test_pak_links WHERE payment_id = ?',
        [payment.id]
      );
      
      // Delete payment
      await connection.execute('DELETE FROM payments WHERE id = ?', [payment.id]);
    }
    
    // 7. Delete test results
    const resultTables = [
      { table: 'disc_results', name: 'DISC' },
      { table: 'mbti_results', name: 'MBTI' },
      { table: 'values_results', name: 'VALUES' },
      { table: 'teams_results', name: 'TEAMS' }
    ];
    
    let totalResults = 0;
    for (const { table, name } of resultTables) {
      try {
        const [results] = await connection.execute(
          `SELECT id FROM ${table} WHERE user_id = ?`,
          [userId]
        );
        
        if (results.length > 0) {
          console.log(`📊 Found ${results.length} ${name} result(s) for user`);
          
          for (const result of results) {
            console.log(`🗑️ Deleting ${name} result: ${result.id}`);
            await connection.execute(`DELETE FROM ${table} WHERE id = ?`, [result.id]);
          }
          totalResults += results.length;
        }
      } catch (err) {
        console.log(`⚠️ Table ${table} may not exist: ${err.message}`);
      }
    }
    
    console.log(`🧹 User data cleanup completed for: ${userEmail}`);
    console.log('📋 Summary:');
    console.log(`   - Test Packs deleted: ${testPacks.length}`);
    console.log(`   - Payments deleted: ${payments.length}`);
    console.log(`   - Test Results deleted: ${totalResults}`);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the cleanup
cleanupUserData();
