const testPackOptions = [
  {
    name: 'Paket Dasar',
    description: 'Paket tes dasar untuk mengenal kepribadian Anda',
    price: 150000,
    tests: ['DISC', 'MBTI'],
    isActive: true,
    sortOrder: 1,
    publishedAt: new Date(),
  },
  {
    name: 'Paket Profesional',
    description: 'Paket tes lengkap untuk pengembangan karir profesional',
    price: 250000,
    tests: ['DISC', 'MBTI', 'TEAMS', 'VALUES'],
    isActive: true,
    sortOrder: 2,
    publishedAt: new Date(),
  },
  {
    name: 'Paket Bisnis',
    description: 'Paket tes khusus untuk pemilik bisnis dan entrepreneur',
    price: 350000,
    tests: ['DISC', 'MBTI', 'TEAMS', 'VALUES'],
    isActive: true,
    sortOrder: 3,
    publishedAt: new Date(),
  },
];

async function createTestPackOptions() {
  console.log('🚀 Creating test pack options...');
  
  for (const option of testPackOptions) {
    try {
      const response = await fetch('http://localhost:1337/api/test-pack-options', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: option }),
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Created: ${option.name}`);
      } else {
        const error = await response.text();
        console.error(`❌ Failed to create ${option.name}:`, error);
      }
    } catch (error) {
      console.error(`❌ Error creating ${option.name}:`, error.message);
    }
  }
  
  console.log('✨ Done!');
}

createTestPackOptions();
