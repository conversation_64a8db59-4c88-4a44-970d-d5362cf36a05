-- Rollback Migration Script for Maxima API
-- Recreating deleted relationship tables

USE `maxima-api`;

-- 1. Recreate payments_user_links table
CREATE TABLE IF NOT EXISTS `payments_user_links` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` int(10) unsigned DEFAULT NULL,
  `user_id` int(10) unsigned DEFAULT NULL,
  `payment_order` double unsigned DEFAULT NULL,
  `user_order` double unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payments_user_links_unique` (`payment_id`,`user_id`),
  KEY `payments_user_links_fk` (`payment_id`),
  KEY `payments_user_links_inv_fk` (`user_id`),
  KEY `payments_user_links_order_fk` (`payment_order`),
  KEY `payments_user_links_order_inv_fk` (`user_order`),
  CONSTRAINT `payments_user_links_fk` FOREI<PERSON><PERSON> KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `payments_user_links_inv_fk` FOREIGN KEY (`user_id`) REFERENCES `up_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Recreate payments_test_pack_links table
CREATE TABLE IF NOT EXISTS `payments_test_pack_links` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` int(10) unsigned DEFAULT NULL,
  `test_pack_id` int(10) unsigned DEFAULT NULL,
  `payment_order` double unsigned DEFAULT NULL,
  `test_pack_order` double unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payments_test_pack_links_unique` (`payment_id`,`test_pack_id`),
  KEY `payments_test_pack_links_fk` (`payment_id`),
  KEY `payments_test_pack_links_inv_fk` (`test_pack_id`),
  KEY `payments_test_pack_links_order_fk` (`payment_order`),
  KEY `payments_test_pack_links_order_inv_fk` (`test_pack_order`),
  CONSTRAINT `payments_test_pack_links_fk` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `payments_test_pack_links_inv_fk` FOREIGN KEY (`test_pack_id`) REFERENCES `test_packs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Migrate data back from foreign key columns to relationship tables
SELECT 'Migrating data back to relationship tables...' as info;

-- Migrate user relationships back
INSERT IGNORE INTO payments_user_links (payment_id, user_id, payment_order, user_order)
SELECT id, user_id, 1, 1
FROM payments 
WHERE user_id IS NOT NULL;

-- Migrate test pack relationships back
INSERT IGNORE INTO payments_test_pack_links (payment_id, test_pack_id, payment_order, test_pack_order)
SELECT id, test_pack_id, 1, 1
FROM payments 
WHERE test_pack_id IS NOT NULL;

-- 4. Show migration results
SELECT 'Migration results:' as info;
SELECT COUNT(*) as total_user_links FROM payments_user_links;
SELECT COUNT(*) as total_test_pack_links FROM payments_test_pack_links;

-- 5. Show sample data
SELECT 'Sample user links:' as info;
SELECT * FROM payments_user_links LIMIT 5;

SELECT 'Sample test pack links:' as info;
SELECT * FROM payments_test_pack_links LIMIT 5;

-- 6. Remove the foreign key columns (optional - keep for now to maintain compatibility)
-- We'll keep both approaches for now to ensure compatibility
SELECT 'Keeping both foreign key columns and relationship tables for compatibility' as info;

SELECT 'Rollback completed successfully!' as result;
