# Maxima - Personality Test Application

A comprehensive personality test application built with Angular that offers multiple psychological tests (DISC, MBTI, TEAMS, VALUES) with integrated payment processing and detailed reporting.

## Features

- **Multiple Personality Tests**: DISC, MBTI, TEAMS, VALUES assessments
- **Test Packages**: Various test combinations with different pricing
- **Payment Integration**: Secure payment processing with Tripay
- **User Management**: Registration, authentication, and profile management
- **Admin Dashboard**: Payment management and user administration
- **Detailed Reports**: AI-powered insights with charts and visualizations
- **PDF Export**: Downloadable test reports
- **Email Integration**: Automated report delivery

## Tech Stack

- **Frontend**: Angular 15 with Angular Material
- **Styling**: Tailwind CSS
- **Charts**: Chart.js with ng2-charts
- **Payment**: Tripay Payment Gateway
- **Backend**: Strapi (API)
- **Security**: JWT Authentication with HMAC SHA256 signatures

## Quick Start

1. **Installation**
   ```bash
   npm install
   ```

2. **Environment Setup**
   - Copy environment files and configure Tripay credentials
   - See [TRIPAY_SETUP.md](./TRIPAY_SETUP.md) for detailed configuration

3. **Development Server**
   ```bash
   ng serve
   ```
   Navigate to `http://localhost:4200/`

## Payment Integration

This application uses **Tripay** as the payment gateway. For setup instructions and configuration, see [TRIPAY_SETUP.md](./TRIPAY_SETUP.md).

### Migration from Xendit

The application has been migrated from Xendit to Tripay. Key changes include:
- New signature generation using HMAC SHA256
- Updated webhook handling
- Support for Indonesian payment methods
- Enhanced security features

## Project Structure

```
src/
├── app/
│   ├── components/          # Reusable UI components
│   ├── features/           # Feature modules (admin, tests)
│   ├── interfaces/         # TypeScript interfaces
│   ├── pages/             # Main application pages
│   ├── service/           # Business logic services
│   └── shared/            # Shared utilities
├── assets/                # Static assets
└── environments/          # Environment configurations
```

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 15.2.6.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
