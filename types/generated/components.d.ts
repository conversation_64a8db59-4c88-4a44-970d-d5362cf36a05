import type { Schema, Attribute } from '@strapi/strapi';

export interface ComponentTrustStatistic extends Schema.Component {
  collectionName: 'components_component_trust_statistics';
  info: {
    displayName: 'Trust Statistic';
    icon: 'chart-bar';
    description: 'Individual trust statistic item';
  };
  attributes: {
    value: Attribute.String & Attribute.Required;
    label: Attribute.String & Attribute.Required;
  };
}

export interface ComponentTrustIndicators extends Schema.Component {
  collectionName: 'components_component_trust_indicators';
  info: {
    displayName: 'Trust Indicators';
    icon: 'shield';
    description: 'Trust indicators with title, subtitle and statistics';
  };
  attributes: {
    title: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'Dipercaya Oleh'>;
    subtitle: Attribute.Text &
      Attribute.Required &
      Attribute.DefaultTo<'Ribuan profesional dan organisasi telah merasakan manfaatnya'>;
    statistics: Attribute.Component<'component.trust-statistic', true>;
  };
}

export interface ComponentTitle extends Schema.Component {
  collectionName: 'components_component_titles';
  info: {
    displayName: 'Title';
    icon: 'bold';
  };
  attributes: {
    title: Attribute.String;
  };
}

export interface ComponentTextBlock extends Schema.Component {
  collectionName: 'components_component_text_blocks';
  info: {
    displayName: 'Text Block';
    icon: 'file-text';
    description: 'A flexible text block with title and content';
  };
  attributes: {
    title: Attribute.String;
    content: Attribute.RichText & Attribute.Required;
    alignment: Attribute.Enumeration<['left', 'center', 'right']> &
      Attribute.DefaultTo<'left'>;
  };
}

export interface ComponentTestSelectionSection extends Schema.Component {
  collectionName: 'components_component_test_selection_sections';
  info: {
    displayName: 'Test Selection Section';
    icon: 'grid-3x3';
    description: 'Section for customer test selection with title and subtitle';
  };
  attributes: {
    title: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'Mulai Perjalanan Pengembangan Diri Anda'>;
    subtitle: Attribute.Text &
      Attribute.Required &
      Attribute.DefaultTo<'Pilih paket tes yang sesuai dengan kebutuhan dan tujuan pengembangan diri Anda'>;
    showOnHomePage: Attribute.Boolean & Attribute.DefaultTo<true>;
  };
}

export interface ComponentStatistics extends Schema.Component {
  collectionName: 'components_component_statistics_sections';
  info: {
    displayName: 'Statistics';
    icon: 'bar-chart';
    description: 'Section for displaying statistics';
  };
  attributes: {
    title: Attribute.String;
    stats: Attribute.Component<'component.statistic', true>;
  };
}

export interface ComponentStatistic extends Schema.Component {
  collectionName: 'components_component_statistics';
  info: {
    displayName: 'Statistic';
    icon: 'analytics';
    description: 'Individual statistic item';
  };
  attributes: {
    number: Attribute.String & Attribute.Required;
    label: Attribute.String & Attribute.Required;
    description: Attribute.Text;
  };
}

export interface ComponentHeader extends Schema.Component {
  collectionName: 'components_component_headers';
  info: {
    displayName: 'Header';
    icon: 'medium';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Text;
    img: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
  };
}

export interface ComponentFeature extends Schema.Component {
  collectionName: 'components_component_features';
  info: {
    displayName: 'Feature';
    icon: 'star';
    description: 'Individual feature item';
  };
  attributes: {
    icon: Attribute.Text;
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text & Attribute.Required;
  };
}

export interface ComponentFeatureGrid extends Schema.Component {
  collectionName: 'components_component_feature_grids';
  info: {
    displayName: 'Feature Grid';
    icon: 'apps';
    description: 'Grid layout for displaying features';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.Text;
    features: Attribute.Component<'component.feature', true>;
  };
}

export interface ComponentFeatureCard extends Schema.Component {
  collectionName: 'components_component_feature_cards';
  info: {
    displayName: 'Feature Card';
    icon: 'star';
    description: 'Individual feature card with SVG icon';
  };
  attributes: {
    svgIcon: Attribute.Text & Attribute.Required;
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text & Attribute.Required;
  };
}

export interface ComponentCtaSection extends Schema.Component {
  collectionName: 'components_component_cta_sections';
  info: {
    displayName: 'CTA Section';
    icon: 'cursor';
    description: 'Call-to-action section';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    buttonText: Attribute.String;
    buttonUrl: Attribute.String;
  };
}

export interface ComponentButton extends Schema.Component {
  collectionName: 'components_component_buttons';
  info: {
    displayName: 'Button';
    icon: 'cursor';
  };
  attributes: {
    title: Attribute.String;
    url: Attribute.String;
    type: Attribute.Enumeration<
      [
        'primary',
        'secondary',
        'outline',
        'primary-icon',
        'secondary-icon',
        'outline-icon'
      ]
    >;
  };
}

export interface ComponentBenefitsList extends Schema.Component {
  collectionName: 'components_component_benefits_lists';
  info: {
    displayName: 'Benefits List';
    icon: 'list';
    description: 'List of benefits for psychology tests';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.Text;
    benefits: Attribute.Component<'component.benefit', true>;
  };
}

export interface ComponentBenefit extends Schema.Component {
  collectionName: 'components_component_benefits';
  info: {
    displayName: 'Benefit';
    icon: 'check-circle';
    description: 'Individual benefit item';
  };
  attributes: {
    icon: Attribute.Text;
    title: Attribute.String & Attribute.Required;
    description: Attribute.RichText & Attribute.Required;
    highlight: Attribute.Boolean & Attribute.DefaultTo<false>;
  };
}

export interface ComponentAboutSection extends Schema.Component {
  collectionName: 'components_component_about_sections';
  info: {
    displayName: 'About Section';
    icon: 'information';
    description: 'Complete about section with features and trust indicators';
  };
  attributes: {
    title: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'Tentang Maxima Potential'>;
    description: Attribute.Text & Attribute.Required;
    features: Attribute.Component<'component.feature-card', true>;
    trustIndicators: Attribute.Component<'component.trust-indicators'>;
    showOnHomePage: Attribute.Boolean & Attribute.DefaultTo<true>;
  };
}

export interface Component2Button extends Schema.Component {
  collectionName: 'components_component_2_buttons';
  info: {
    displayName: '2Button';
    icon: 'chartBubble';
  };
  attributes: {
    firstButton: Attribute.Component<'component.button'>;
    secondButton: Attribute.Component<'component.button'>;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface Components {
      'component.trust-statistic': ComponentTrustStatistic;
      'component.trust-indicators': ComponentTrustIndicators;
      'component.title': ComponentTitle;
      'component.text-block': ComponentTextBlock;
      'component.test-selection-section': ComponentTestSelectionSection;
      'component.statistics': ComponentStatistics;
      'component.statistic': ComponentStatistic;
      'component.header': ComponentHeader;
      'component.feature': ComponentFeature;
      'component.feature-grid': ComponentFeatureGrid;
      'component.feature-card': ComponentFeatureCard;
      'component.cta-section': ComponentCtaSection;
      'component.button': ComponentButton;
      'component.benefits-list': ComponentBenefitsList;
      'component.benefit': ComponentBenefit;
      'component.about-section': ComponentAboutSection;
      'component.2-button': Component2Button;
    }
  }
}
