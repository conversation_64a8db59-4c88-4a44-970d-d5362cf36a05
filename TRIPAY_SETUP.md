# Tripay Integration Guide

Proyek ini telah dimigrasi dari Xendit ke Tripay sebagai payment gateway. Be<PERSON>ut adalah panduan untuk konfigurasi dan penggunaan.

## Konfigurasi

### 1. Environment Variables

Perbarui konfigurasi di file environment:

**Development (`src/environments/environment.development.ts`):**
```typescript
export const environment = {
  url: 'http://localhost:1337/api/',
  useMockData: true,
  tripay: {
    apiUrl: 'https://tripay.co.id/api-sandbox',
    merchantCode: 'T12345', // Ganti dengan merchant code Anda
    apiKey: 'DEV-123456789', // Ganti dengan API key development Anda
    privateKey: 'your-private-key', // Ganti dengan private key Anda
    callbackToken: 'your-callback-token', // Ganti dengan callback token Anda
  },
};
```

**Production (`src/environments/environment.ts`):**
```typescript
export const environment = {
  url: 'https://your-production-api.com/api/',
  useMockData: false,
  tripay: {
    apiUrl: 'https://tripay.co.id/api',
    merchantCode: 'YOUR_PRODUCTION_MERCHANT_CODE',
    apiKey: 'YOUR_PRODUCTION_API_KEY',
    privateKey: 'YOUR_PRODUCTION_PRIVATE_KEY',
    callbackToken: 'YOUR_PRODUCTION_CALLBACK_TOKEN',
  },
};
```

### 2. Mendapatkan Kredensial Tripay

1. Daftar di [Tripay](https://tripay.co.id)
2. Login ke dashboard merchant
3. Buat aplikasi baru
4. Dapatkan:
   - Merchant Code
   - API Key
   - Private Key
   - Callback Token

### 3. Konfigurasi Webhook URL

Set webhook URL di dashboard Tripay:
- Development: `http://localhost:4200/api/tripay/webhook`
- Production: `https://yourdomain.com/api/tripay/webhook`

## Fitur yang Diimplementasikan

### 1. Payment Creation
- Menggunakan API Tripay untuk membuat transaksi
- Support multiple payment methods (VA, E-Wallet, dll)
- Automatic signature generation dengan HMAC SHA256

### 2. Payment Status Tracking
- Real-time status update via webhook
- Automatic payment status mapping
- Integration dengan sistem internal

### 3. Webhook Handling
- Signature verification untuk keamanan
- Automatic payment status update
- Logging semua webhook events

### 4. Payment Methods Support
- Bank Transfer (VA)
- E-Wallet (OVO, DANA, LinkAja, dll)
- Retail Outlet (Alfamart, Indomaret)
- Credit Card

## Alur Pembayaran

1. User memilih test pack
2. Sistem membuat transaksi di Tripay
3. User diarahkan ke halaman pembayaran Tripay
4. User melakukan pembayaran
5. Tripay mengirim webhook ke sistem
6. Sistem memverifikasi dan update status payment
7. Test pack diaktivasi untuk user

## Security Features

- HMAC SHA256 signature verification
- Callback token validation
- Encrypted communication dengan Tripay
- Payment data logging untuk audit trail

## Testing

### Mock Mode
Saat `useMockData: true`, sistem akan menggunakan data simulasi tanpa koneksi ke Tripay.

### Payment Simulator
Tetap tersedia di `/payment-simulator/:id` untuk testing flow pembayaran.

## Migration dari Xendit

Perubahan utama:
- `xenditId` → `tripayId` dalam database
- Webhook signature menggunakan HMAC SHA256
- Payment method codes sesuai dengan Tripay
- Status mapping disesuaikan dengan Tripay

## Troubleshooting

### Common Issues:

1. **Signature Mismatch**
   - Pastikan private key benar
   - Periksa format data untuk signature
   - Cek encoding (UTF-8)

2. **Webhook Not Received**
   - Pastikan URL webhook accessible
   - Cek firewall/security groups
   - Verify callback token

3. **Payment Creation Failed**
   - Cek API key dan merchant code
   - Pastikan amount minimal sesuai ketentuan
   - Verify expired_time format

## Dependencies

- `crypto-js`: Untuk HMAC SHA256 signature generation
- `@types/crypto-js`: TypeScript definitions

## Support

Untuk bantuan teknis:
- [Dokumentasi Tripay](https://tripay.co.id/developer)
- [Tripay Support](https://tutorial.tripay.co.id/)
