# Panduan Menambahkan Halaman di Strapi CMS

## ✅ Yang Sudah Diimplementasi di Angular

1. **Dynamic Page Component** - Sudah dimodifikasi untuk menangani multiple pages
2. **Parser Service** - Sudah mendukung parameter slug untuk halaman berbeda
3. **Navbar** - Sudah ditambahkan link untuk About Us dan Why
4. **Routing** - Menggunakan query parameter `?page=` untuk navigasi
5. **Loading State** - Menampilkan loading spinner saat mengambil data
6. **Komponen Baru**:
   - `TextBlockComponent` - Untuk blok teks dengan judul
   - `FeatureGridComponent` - Untuk grid fitur/keunggulan

## ✅ Yang Sudah Diimplementasi di Strapi

1. **Single Type Created**:
   - `about-page` - Halaman tentang kami
   - `why-page` - Halaman mengapa tes psikologi
2. **Components Created**:
   - `component.text-block` - Blok teks fleksibel
   - `component.feature-grid` - Grid fitur
   - `component.feature` - Item fitur individual
   - `component.benefits-list` - List manfaat
   - `component.benefit` - Item manfaat individual
   - `component.statistics` - Section statistik
   - `component.statistic` - Item statistik individual
   - `component.cta-section` - Call-to-action section
3. **API Structure** - Controllers, routes, dan services sudah dibuat

## 🎯 Langkah Selanjutnya di Strapi Admin

Sekarang Anda perlu mengisi content untuk halaman yang sudah dibuat:

### 1. Akses Strapi Admin
```bash
# Strapi sudah running di:
http://localhost:1337/admin
```

### 2. Setup Permissions
1. Pergi ke **Settings** → **Users & Permissions Plugin** → **Roles** → **Public**
2. Scroll ke **About-page** dan centang **find**
3. Scroll ke **Why-page** dan centang **find**
4. Klik **Save**

### 3. Isi Content About Page
1. Pergi ke **Content Manager** → **Single Types** → **About Page**
2. Klik **Create new entry**
3. Tambahkan komponen:
   - **Header**: Title "Tentang Maxima Potential", Description, Upload Image
   - **Text Block**: Title "Misi Kami", Content dengan rich text
   - **Feature Grid**: Title "Mengapa Memilih Kami", tambahkan 3-4 features
4. Klik **Publish**

### 4. Isi Content Why Page
1. Pergi ke **Content Manager** → **Single Types** → **Why Page**
2. Klik **Create new entry**
3. Tambahkan komponen:
   - **Header**: Title "Mengapa Tes Psikologi Penting?", Description, Image
   - **Benefits List**: Title "Manfaat Tes Psikologi", tambahkan benefits
   - **Statistics**: Title "Dampak Positif", tambahkan statistik
   - **CTA Section**: Title, Description, Button untuk test packages
4. Klik **Publish**

### 1. Halaman About Us (`about-page`)

#### Langkah-langkah:
1. Buka Strapi Admin Panel (http://localhost:1337/admin)
2. Pergi ke **Content-Types Builder**
3. Klik **Create new single type**
4. Nama: `about-page`
5. Display name: `About Page`

#### Field Structure:
```
about-page
├── content (Component - repeatable)
    ├── component.header
    │   ├── title (Text)
    │   ├── description (Rich Text)
    │   └── img (Media - Single)
    ├── component.title
    │   └── title (Text)
    ├── component.text-block
    │   ├── title (Text)
    │   ├── content (Rich Text)
    │   └── alignment (Enumeration: left, center, right)
    ├── component.feature-grid
    │   ├── title (Text)
    │   ├── subtitle (Text)
    │   └── features (Component - repeatable)
    │       ├── icon (Text) // SVG icon atau icon class
    │       ├── title (Text)
    │       └── description (Text)
    └── component.2-button
        ├── firstButton (Component)
        │   ├── title (Text)
        │   └── url (Text)
        └── secondButton (Component)
            ├── title (Text)
            └── url (Text)
```

### 2. Halaman Why (`why-page`)

#### Langkah-langkah:
1. Buka Strapi Admin Panel
2. Pergi ke **Content-Types Builder**
3. Klik **Create new single type**
4. Nama: `why-page`
5. Display name: `Why Page`

#### Field Structure:
```
why-page
├── content (Component - repeatable)
    ├── component.header
    │   ├── title (Text)
    │   ├── description (Rich Text)
    │   └── img (Media - Single)
    ├── component.title
    │   └── title (Text)
    ├── component.benefits-list
    │   ├── title (Text)
    │   ├── subtitle (Text)
    │   └── benefits (Component - repeatable)
    │       ├── icon (Text)
    │       ├── title (Text)
    │       ├── description (Rich Text)
    │       └── highlight (Boolean)
    ├── component.statistics
    │   ├── title (Text)
    │   └── stats (Component - repeatable)
    │       ├── number (Text)
    │       ├── label (Text)
    │       └── description (Text)
    └── component.cta-section
        ├── title (Text)
        ├── description (Rich Text)
        ├── buttonText (Text)
        └── buttonUrl (Text)
```

## Komponen Baru yang Perlu Dibuat

### 1. component.text-block
- **Kegunaan**: Untuk menampilkan blok teks dengan judul
- **Fields**:
  - title (Text)
  - content (Rich Text)
  - alignment (Enumeration)

### 2. component.feature-grid
- **Kegunaan**: Grid fitur untuk menampilkan keunggulan
- **Fields**:
  - title (Text)
  - subtitle (Text)
  - features (Component repeatable)

### 3. component.benefits-list
- **Kegunaan**: Daftar manfaat tes psikologi
- **Fields**:
  - title (Text)
  - subtitle (Text)
  - benefits (Component repeatable)

### 4. component.statistics
- **Kegunaan**: Menampilkan statistik dan angka penting
- **Fields**:
  - title (Text)
  - stats (Component repeatable)

### 5. component.cta-section
- **Kegunaan**: Call-to-action section
- **Fields**:
  - title (Text)
  - description (Rich Text)
  - buttonText (Text)
  - buttonUrl (Text)

## Permissions Settings

Pastikan untuk mengatur permissions di Strapi:

1. Pergi ke **Settings** → **Users & Permissions Plugin** → **Roles** → **Public**
2. Berikan permission **find** untuk:
   - `about-page`
   - `why-page`

## Contoh Content untuk About Page

### Header Component:
- **Title**: "Tentang Maxima Potential"
- **Description**: "Platform terdepan untuk pengembangan diri melalui tes kepribadian yang komprehensif dan akurat"
- **Image**: Upload gambar hero

### Text Block:
- **Title**: "Misi Kami"
- **Content**: "Membantu individu dan organisasi memahami potensi diri melalui metodologi tes yang teruji secara ilmiah..."

### Feature Grid:
- **Title**: "Mengapa Memilih Kami"
- **Features**:
  1. **Metodologi Terpercaya** - Menggunakan DISC, MBTI, TEAMS, VALUES
  2. **Hasil Akurat** - Analisis mendalam dengan akurasi tinggi
  3. **Panduan Lengkap** - Rekomendasi pengembangan yang actionable

## Contoh Content untuk Why Page

### Header Component:
- **Title**: "Mengapa Tes Psikologi Penting?"
- **Description**: "Memahami kepribadian adalah kunci utama dalam pengembangan diri dan kesuksesan karir"

### Benefits List:
- **Title**: "Manfaat Tes Psikologi"
- **Benefits**:
  1. **Pemahaman Diri** - Mengenal kekuatan dan kelemahan
  2. **Pengembangan Karir** - Memilih jalur karir yang sesuai
  3. **Hubungan Interpersonal** - Komunikasi yang lebih efektif
  4. **Produktivitas** - Bekerja sesuai dengan gaya alami

### Statistics:
- **Title**: "Dampak Positif Tes Psikologi"
- **Stats**:
  - 85% - Peningkatan self-awareness
  - 78% - Kepuasan kerja yang lebih tinggi
  - 92% - Komunikasi tim yang lebih baik

## URL Access

Setelah setup selesai, halaman dapat diakses melalui:
- About: `http://localhost:4200/?page=about-page`
- Why: `http://localhost:4200/?page=why-page`
- Home: `http://localhost:4200/` (default)

## Testing Setup

### 1. Jalankan Aplikasi
```bash
cd /Users/<USER>/angular/maxima
npm start
```

### 2. Test URLs
- **Home**: `http://localhost:4200/`
- **About**: `http://localhost:4200/?page=about-page`
- **Why**: `http://localhost:4200/?page=why-page`

### 3. Monitoring
- Buka Browser Developer Tools → Network tab
- Lihat request ke API Strapi saat navigasi halaman
- Pastikan endpoint dipanggil dengan benar

## Troubleshooting

### Jika halaman tidak muncul:
1. Periksa Strapi admin panel apakah single type sudah dibuat
2. Pastikan content sudah diisi dan dipublish
3. Periksa permissions di Settings → Users & Permissions
4. Cek console browser untuk error

### Jika komponen tidak tampil:
1. Pastikan field `__component` sesuai dengan kondisi di template
2. Periksa struktur data di Network tab browser
3. Pastikan semua field required sudah diisi

## ✅ SETUP SELESAI! - LANGKAH SELANJUTNYA

### Status Implementation:
- ✅ **Angular Components** - Semua komponen dynamic page sudah dibuat dan siap
- ✅ **Strapi Content Types** - About-page dan Why-page sudah dibuat
- ✅ **Strapi Components** - Semua komponen sudah tersedia
- ✅ **Strapi Server** - Sudah running di `http://localhost:1337`

### 🚀 Aksi yang Perlu Dilakukan Sekarang:

1. **Buka Strapi Admin**: `http://localhost:1337/admin`
2. **Set Permissions** untuk about-page dan why-page
3. **Isi Content** untuk kedua halaman
4. **Test** di Angular: `http://localhost:4200/?page=about-page`

### 📋 Checklist Setup:
- [x] Angular dynamic page component
- [x] Parser service modification
- [x] Navbar with new links
- [x] Strapi about-page single type
- [x] Strapi why-page single type  
- [x] All Strapi components created
- [ ] Set permissions di Strapi admin
- [ ] Fill content about-page
- [ ] Fill content why-page
- [ ] Test halaman About
- [ ] Test halaman Why
