# Database Migration Notes

## Changes Made to Schema

### 1. Payment Schema Changes
File: `src/api/payment/content-types/payment/schema.json`

**Changes:**
- Fixed typo: `padiAt` → `paidAt`
- Changed `users` relation from `manyToMany` to `manyToOne` (renamed to `user`)
- Changed `testPak` relation from `manyToMany` to `manyToOne` (renamed to `testPack`)

**Before:**
```json
{
  "padiAt": { "type": "datetime" },
  "users": {
    "type": "relation",
    "relation": "manyToMany",
    "target": "plugin::users-permissions.user",
    "inversedBy": "payments"
  },
  "testPak": {
    "type": "relation",
    "relation": "manyToMany", 
    "target": "api::test-pack.test-pack",
    "mappedBy": "payment"
  }
}
```

**After:**
```json
{
  "paidAt": { "type": "datetime" },
  "user": {
    "type": "relation",
    "relation": "manyToOne",
    "target": "plugin::users-permissions.user",
    "inversedBy": "payments"
  },
  "testPack": {
    "type": "relation",
    "relation": "manyToOne",
    "target": "api::test-pack.test-pack",
    "inversedBy": "payments"
  }
}
```

### 2. Test Pack Schema Changes
File: `src/api/test-pack/content-types/test-pack/schema.json`

**Changes:**
- Changed `payment` relation from `manyToMany` to `oneToMany` (renamed to `payments`)

**Before:**
```json
{
  "payment": {
    "type": "relation",
    "relation": "manyToMany",
    "target": "api::payment.payment",
    "inversedBy": "testPak"
  }
}
```

**After:**
```json
{
  "payments": {
    "type": "relation",
    "relation": "oneToMany",
    "target": "api::payment.payment",
    "mappedBy": "testPack"
  }
}
```

## Required Database Migration Steps

### For MySQL Database:

1. **Backup your database first!**
   ```bash
   mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Update payment table:**
   ```sql
   -- Fix column name typo
   ALTER TABLE payments CHANGE COLUMN padiAt paidAt DATETIME;
   
   -- Drop old many-to-many relationship tables if they exist
   DROP TABLE IF EXISTS payments_users_links;
   DROP TABLE IF EXISTS payments_test_paks_links;
   
   -- Add new foreign key columns
   ALTER TABLE payments ADD COLUMN user_id INT;
   ALTER TABLE payments ADD COLUMN test_pack_id INT;
   
   -- Add foreign key constraints
   ALTER TABLE payments 
   ADD CONSTRAINT fk_payments_user 
   FOREIGN KEY (user_id) REFERENCES up_users(id) ON DELETE SET NULL;
   
   ALTER TABLE payments 
   ADD CONSTRAINT fk_payments_test_pack 
   FOREIGN KEY (test_pack_id) REFERENCES test_packs(id) ON DELETE SET NULL;
   ```

3. **Restart Strapi** to apply schema changes

4. **Verify the changes** by checking:
   - Payment history displays correctly
   - Test pack reports show proper ownership
   - New payments are linked correctly

## Code Changes Made

### 1. User Flow Changes
- Changed default login redirect from `/home/<USER>/home/<USER>
- Created new `UserTestResultsComponent` for displaying user's test results
- Added routing for `/home/<USER>

### 2. Navigation Changes
- Added "Hasil Test Saya" menu item for all users
- Hidden assessment menus (DISC, TEAMS, VALUES, MBTI) from non-admin users
- Assessment menus now only visible to admin users

### 3. Payment Service Updates
- Fixed `updatePaymentStatus` to use correct field names (`user`, `testPack`)
- Updated payment history query to use correct relations

### 4. Access Control
- Added ownership check in `TestPackReportComponent`
- Non-admin users can only view their own test pack reports
- Admin users can view all test pack reports

## Testing Checklist

- [ ] Login as regular user redirects to test results page
- [ ] Regular users see "Hasil Test Saya" in navigation
- [ ] Regular users cannot see assessment menus (DISC, TEAMS, etc.)
- [ ] Admin users can see all menus including assessments
- [ ] Payment history displays correctly for users
- [ ] Test pack reports show proper ownership restrictions
- [ ] New payments are properly linked to users and test packs
