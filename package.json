{"name": "maxima", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^15.2.0", "@angular/cdk": "^15.2.9", "@angular/common": "^15.2.0", "@angular/compiler": "^15.2.0", "@angular/core": "^15.2.0", "@angular/forms": "^15.2.0", "@angular/material": "^15.2.9", "@angular/platform-browser": "^15.2.0", "@angular/platform-browser-dynamic": "^15.2.0", "@angular/router": "^15.2.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/crypto-js": "^4.2.2", "chart.js": "~3.9.1", "crypto-js": "^4.2.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "marked": "^15.0.2", "ng2-charts": "~4.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.6", "@angular/cli": "~15.2.6", "@angular/compiler-cli": "^15.2.0", "@types/jasmine": "~4.3.0", "autoprefixer": "^10.4.20", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "~4.9.4"}}