{"name": "maxima-api", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi", "deploy": "strapi deploy"}, "dependencies": {"@strapi/plugin-cloud": "4.25.11", "@strapi/plugin-i18n": "4.25.11", "@strapi/plugin-users-permissions": "4.25.11", "@strapi/strapi": "4.25.11", "@swc/core": "^1.7.26", "better-sqlite3": "8.6.0", "mysql": "^2.18.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "5.3.4", "styled-components": "5.3.3"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "3c5fb91a-8643-4d0f-8e06-f8dc57193a70"}, "engines": {"node": ">=18.0.0 <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT"}